from cryptography.fernet import Fernet
import base64
import os
from dotenv import load_dotenv
import hashlib

# 加载环境变量
load_dotenv()

# 从环境变量获取密钥，如果不存在则生成一个并保存
def get_encryption_key():
    key = os.getenv("ENCRYPTION_KEY")
    if not key:
        # 生成新密钥
        key = Fernet.generate_key().decode()
        # 将密钥写入.env文件
        with open(".env", "a") as env_file:
            env_file.write(f"\nENCRYPTION_KEY={key}")
        os.environ["ENCRYPTION_KEY"] = key
    return key

# 初始化密钥
ENCRYPTION_KEY = get_encryption_key()

def get_fernet():
    """获取Fernet实例用于加密解密"""
    # 确保密钥是32位的URL安全base64编码字符串
    key_bytes = ENCRYPTION_KEY.encode()
    # 如果密钥不是32位URL安全base64编码，则使用哈希处理
    if len(base64.urlsafe_b64decode(key_bytes + b'=' * (-len(key_bytes) % 4))) != 32:
        # 使用SHA-256哈希处理密钥并转为URL安全base64编码
        hashed_key = hashlib.sha256(key_bytes).digest()
        key = base64.urlsafe_b64encode(hashed_key)
    else:
        key = key_bytes
    
    return Fernet(key)

def encrypt_data(data):
    """加密数据"""
    if not data:
        return data
    
    fernet = get_fernet()
    return fernet.encrypt(data.encode()).decode()

def decrypt_data(data):
    """解密数据"""
    if not data:
        return data
    
    try:
        fernet = get_fernet()
        return fernet.decrypt(data.encode()).decode()
    except Exception:
        # 如果解密失败，返回原始数据（可能是未加密的数据）
        return data

def is_encrypted(data):
    """检查数据是否已加密"""
    if not data:
        return False
    
    try:
        # 尝试解密数据，如果成功则说明数据已加密
        decrypt_data(data)
        return True
    except Exception:
        return False 