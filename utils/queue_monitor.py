from datetime import datetime, date
import asyncio
import json
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from database import get_db
from models import DistributionQueue
import httpx
from utils.pending_data import load_pending_data, clear_pending_data
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QueueMonitor:
    def __init__(self):
        self.is_running = False
        self.monitored_dates = set()
        
    async def start_monitoring(self):
        """启动监测程序"""
        self.is_running = True
        logger.info("队列监测程序已启动")
        while self.is_running:
            try:
                # 获取数据库会话
                db = next(get_db())
                try:
                    # 检查所有监测日期的队列
                    for target_date in list(self.monitored_dates):
                        await self.check_and_process_queue(target_date, db)
                finally:
                    db.close()
            except Exception as e:
                logger.error(f"监测过程中出错: {str(e)}")
            
            # 每30秒检查一次
            await asyncio.sleep(30)
    
    def stop_monitoring(self):
        """停止监测程序"""
        self.is_running = False
        logger.info("队列监测程序已停止")
    
    def add_monitored_date(self, target_date: date):
        """添加需要监测的日期"""
        self.monitored_dates.add(target_date)
        logger.info(f"添加监测日期: {target_date}")
    
    def remove_monitored_date(self, target_date: date):
        """移除监测的日期"""
        self.monitored_dates.discard(target_date)
        logger.info(f"移除监测日期: {target_date}")
    
    async def check_and_process_queue(self, target_date: date, db: Session):
        """检查并处理指定日期的队列"""
        try:
            # 加载待发送数据
            pending_data = load_pending_data(target_date)
            if not pending_data:
                return
            
            logger.info(f"发现 {target_date} 有 {len(pending_data)} 条待发送数据")
            
            # 记录成功处理的数据索引
            processed_indices = []
            
            # 处理每条待发送数据
            for idx, pending_item in enumerate(pending_data):
                try:
                    item = pending_item["data"]
                    # 检查店铺数据是否包含"F"字符来判断队列类型
                    shop_info = item.get("shop", "") or item.get("store", "")
                    
                    if not shop_info:
                        continue
                    
                    # 判断队列类型
                    queue_type = "free"  # 默认为免费队列
                    if "F" in shop_info.upper() or "付费" in shop_info:
                        queue_type = "paid"
                    elif "formatted_text" in item and item["formatted_text"]:
                        formatted_text = item["formatted_text"].upper()
                        if "F" in formatted_text or "付费" in formatted_text:
                            queue_type = "paid"
                    
                    logger.info(f"数据判定为 {queue_type} 类型")
                    
                    # 查询相应的队列
                    queue_items = db.query(DistributionQueue).filter(
                        DistributionQueue.queue_date == target_date,
                        DistributionQueue.channel_type == "新媒体渠道",
                        DistributionQueue.queue_type == queue_type,
                        DistributionQueue.status == "pending"
                    ).order_by(DistributionQueue.position).all()
                    
                    if not queue_items:
                        logger.info(f"未找到可用的 {queue_type} 队列")
                        continue
                    
                    # 找到符合当前时间的队列项
                    current_time = datetime.now().time()
                    suitable_queue = None
                    
                    for q_item in queue_items:
                        time_range = q_item.time_slot.split("-")
                        if len(time_range) != 2:
                            continue
                        
                        try:
                            start_time = datetime.strptime(time_range[0].strip(), "%H:%M").time()
                            end_time = datetime.strptime(time_range[1].strip(), "%H:%M").time()
                            
                            if self.is_time_in_range(current_time, start_time, end_time):
                                suitable_queue = q_item
                                break
                        except ValueError:
                            continue
                    
                    if not suitable_queue:
                        logger.info(f"未找到当前时间 {current_time.strftime('%H:%M')} 范围内的待分发队列")
                        continue
                    
                    # 发送数据到钉钉群
                    success = await self.send_to_dingtalk(item, suitable_queue, db)
                    
                    if success:
                        processed_indices.append(idx)
                        logger.info(f"成功处理第 {idx + 1} 条数据")
                
                except Exception as e:
                    logger.error(f"处理第 {idx + 1} 条数据时出错: {str(e)}")
            
            # 清除已处理的数据
            if processed_indices:
                clear_pending_data(target_date, processed_indices)
                logger.info(f"已清除 {len(processed_indices)} 条已处理的数据")
        
        except Exception as e:
            logger.error(f"处理 {target_date} 的队列时出错: {str(e)}")
    
    @staticmethod
    def is_time_in_range(current: datetime.time, start: datetime.time, end: datetime.time) -> bool:
        """检查当前时间是否在时间范围内"""
        if start <= end:
            return start <= current <= end
        else:  # 处理跨午夜的情况
            return current >= start or current <= end
    
    async def send_to_dingtalk(self, item: Dict[str, Any], queue_item: DistributionQueue, db: Session) -> bool:
        """发送数据到钉钉群"""
        try:
            # 构建发送数据
            send_data = {
                "name": queue_item.member,
                "type": "markdown",
                "title": "新客户线索"
            }
            
            # 使用格式化文本或原始数据
            if "formatted_text" in item and item["formatted_text"]:
                send_data["data"] = item["formatted_text"]
                logger.info("使用格式化后的文本发送")
            else:
                send_data["data"] = item
                logger.info("使用原始数据发送")
            
            # 发送到钉钉
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "http://localhost:8000/api/dingtalk/auto-send",
                    json=send_data
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get("success"):
                        # 更新队列状态
                        queue_item.status = "distributed"
                        queue_item.distributed_at = datetime.now()
                        queue_item.distributed_by = "自动分发系统"
                        queue_item.remark = f"自动分发数据：{json.dumps(item, ensure_ascii=False)[:100]}..."
                        
                        # 更新实际接待数据
                        from models import DistributionRule
                        rule = db.query(DistributionRule).filter(
                            DistributionRule.date == queue_item.queue_date,
                            DistributionRule.member == queue_item.member
                        ).first()
                        
                        if rule:
                            # 初始化实际接待数
                            if rule.actual_total is None:
                                rule.actual_total = 0
                            if rule.actual_free is None:
                                rule.actual_free = 0
                            if rule.actual_paid is None:
                                rule.actual_paid = 0
                            
                            # 更新实际接待数
                            rule.actual_total += 1
                            
                            if queue_item.queue_type == "paid":
                                rule.actual_paid += 1
                                logger.info(f"更新付费接待数 +1，当前值: {rule.actual_paid}")
                            else:
                                rule.actual_free += 1
                                logger.info(f"更新免费接待数 +1，当前值: {rule.actual_free}")
                            
                            # 更新时间戳
                            rule.updated_at = datetime.now()
                            logger.info(f"成功更新 {queue_item.member} 的实际接待数据")
                        
                        db.commit()
                        return True
                    else:
                        logger.error(f"发送消息失败: {response_data.get('message', '未知错误')}")
                else:
                    logger.error(f"发送消息请求失败，状态码: {response.status_code}")
        
        except Exception as e:
            logger.error(f"发送数据到钉钉时出错: {str(e)}")
            db.rollback()
        
        return False

# 创建全局监测器实例
queue_monitor = QueueMonitor()

# 异步启动监测
async def start_queue_monitor():
    await queue_monitor.start_monitoring()

# 停止监测
def stop_queue_monitor():
    queue_monitor.stop_monitoring() 