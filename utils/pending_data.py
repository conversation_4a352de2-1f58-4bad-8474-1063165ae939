import json
import os
from datetime import datetime, date
from typing import Dict, List, Any

# 配置文件路径
PENDING_DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
PENDING_DATA_FILE_TEMPLATE = 'pending_data_{date}.json'

def get_pending_file_path(target_date: date) -> str:
    """获取指定日期的待发送数据文件路径"""
    filename = PENDING_DATA_FILE_TEMPLATE.format(date=target_date.strftime('%Y%m%d'))
    return os.path.join(PENDING_DATA_DIR, filename)

def ensure_pending_dir():
    """确保待发送数据目录存在"""
    if not os.path.exists(PENDING_DATA_DIR):
        os.makedirs(PENDING_DATA_DIR)

def load_pending_data(target_date: date) -> List[Dict[str, Any]]:
    """加载指定日期的待发送数据"""
    file_path = get_pending_file_path(target_date)
    if not os.path.exists(file_path):
        return []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载待发送数据失败: {e}")
        return []

def save_pending_data(target_date: date, data: Dict[str, Any]):
    """保存待发送数据"""
    ensure_pending_dir()
    file_path = get_pending_file_path(target_date)
    
    try:
        # 读取现有数据
        existing_data = load_pending_data(target_date)
        # 添加新数据
        existing_data.append({
            "data": data,
            "created_at": datetime.now().isoformat()
        })
        
        # 保存到文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
        
        print(f"成功保存待发送数据到文件: {file_path}")
        return True
    except Exception as e:
        print(f"保存待发送数据失败: {e}")
        return False

def clear_pending_data(target_date: date, processed_indices: List[int] = None):
    """清除已处理的待发送数据"""
    file_path = get_pending_file_path(target_date)
    if not os.path.exists(file_path):
        return
    
    try:
        if processed_indices is None:
            # 如果没有指定索引，删除整个文件
            os.remove(file_path)
            print(f"已删除待发送数据文件: {file_path}")
        else:
            # 否则只删除指定的数据项
            data = load_pending_data(target_date)
            # 过滤掉已处理的数据
            remaining_data = [item for i, item in enumerate(data) if i not in processed_indices]
            
            if remaining_data:
                # 如果还有剩余数据，更新文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(remaining_data, f, ensure_ascii=False, indent=2)
                print(f"已从文件中删除 {len(processed_indices)} 条已处理的数据")
            else:
                # 如果没有剩余数据，删除文件
                os.remove(file_path)
                print(f"所有数据已处理完成，删除文件: {file_path}")
    except Exception as e:
        print(f"清除待发送数据失败: {e}")

def get_all_pending_dates() -> List[date]:
    """获取所有有待发送数据的日期"""
    ensure_pending_dir()
    dates = []
    
    try:
        for filename in os.listdir(PENDING_DATA_DIR):
            if filename.startswith('pending_data_') and filename.endswith('.json'):
                date_str = filename[13:21]  # 提取日期部分 YYYYMMDD
                dates.append(datetime.strptime(date_str, '%Y%m%d').date())
        return sorted(dates)
    except Exception as e:
        print(f"获取待发送数据日期列表失败: {e}")
        return [] 