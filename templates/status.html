<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lead Distribution API 服务状态</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
            color: #1f2937;
        }
        .container {
            width: 100%;
            max-width: 800px;
            padding: 2rem;
        }
        .status-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            margin-bottom: 1.5rem;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .header h1 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #111827;
            margin: 0 0 0.5rem 0;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .status-item {
            background: #f9fafb;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        .status-indicator {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            transition: all 0.3s ease;
        }
        .status-healthy {
            background-color: #10b981;
            box-shadow: 0 0 15px rgba(16, 185, 129, 0.3);
        }
        .status-unhealthy {
            background-color: #ef4444;
            box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
        }
        .status-text {
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0.5rem 0;
        }
        .status-detail {
            color: #6b7280;
            font-size: 0.9rem;
        }
        .info-section {
            margin-top: 1rem;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 8px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-label {
            color: #4b5563;
            font-weight: 500;
        }
        .info-value {
            color: #111827;
        }
        .last-updated {
            text-align: center;
            color: #6b7280;
            font-size: 0.9rem;
            margin-top: 2rem;
        }
        @media (max-width: 640px) {
            .container {
                padding: 1rem;
            }
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-card">
            <div class="header">
                <h1>Lead Distribution API 服务状态</h1>
                <p>实时监控系统运行状态</p>
            </div>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-indicator {{ 'status-healthy' if status == 'healthy' else 'status-unhealthy' }}">
                        {{ '✓' if status == 'healthy' else '✗' }}
                    </div>
                    <h2 class="status-text">数据库连接</h2>
                    <p class="status-detail">{{ '正常运行' if status == 'healthy' else '连接异常' }}</p>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-healthy">
                        ✓
                    </div>
                    <h2 class="status-text">API 服务</h2>
                    <p class="status-detail">正常运行</p>
                </div>
            </div>
            <div class="info-section">
                <div class="info-item">
                    <span class="info-label">服务器地址</span>
                    <span class="info-value">{{ request.base_url }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">数据库主机</span>
                    <span class="info-value">{{ db_host }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">数据库名称</span>
                    <span class="info-value">{{ db_name }}</span>
                </div>
            </div>
            <p class="last-updated">最后更新时间: {{ timestamp }}</p>
        </div>
    </div>
    <script>
        setTimeout(() => window.location.reload(), 5000);
    </script>
</body>
</html>