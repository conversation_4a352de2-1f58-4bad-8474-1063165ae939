<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分发计划 - Lead Distribution</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus/dist/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue">
    <style>
        :root {
            --primary-color: #409eff;
            --primary-lighter: #a0cfff;
            --primary-darker: #337ecc;
            --success-color: #67c23a;
            --warning-color: #e6a23c;
            --danger-color: #f56c6c;
            --info-color: #909399;
            --primary-text: #303133;
            --regular-text: #606266;
            --secondary-text: #909399;
            --placeholder-text: #c0c4cc;
            --border-color: #dcdfe6;
            --border-hover: #c0c4cc;
            --background: #f5f7fa;
            --card-bg: #ffffff;
            --transition-time: 0.3s;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--background);
            color: var(--primary-text);
        }
        
        /* 自定义算法说明的tooltip样式 */
        .algorithm-tooltip {
            max-width: 350px !important;
            line-height: 1.5;
        }
        
        .algorithm-tooltip .algorithm-title {
            font-size: 16px !important;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary-darker);
        }
        
        .algorithm-tooltip .algorithm-desc {
            font-size: 14px !important;
            white-space: normal !important;
            margin-bottom: 6px;
        }
        
        /* 强制Element Plus修改tooltip样式 */
        .el-tooltip__popper {
            max-width: 350px !important;
        }
        
        .el-tooltip__popper .el-tooltip__content {
            font-size: 14px !important;
            line-height: 1.5 !important;
            white-space: normal !important;
        }
        
        .container {
            width: 100%;
            max-width: 100%; /* 让页面充满整个屏幕 */
            margin: 0;
            padding: 24px;
            box-sizing: border-box;
        }
        
        /* 添加垂直分栏布局 */
        .content {
            display: flex;
            gap: 24px;
            margin-top: 24px;
        }
        
        .left-column, .right-column {
            /* width 在 HTML 中通过内联样式设置 */
        }
        
        /* 响应式布局调整 */
        @media screen and (max-width: 1200px) {
            .content {
                flex-direction: column; /* 小屏幕上改为上下布局 */
            }
            
            .left-column, .right-column {
                width: 100% !important; /* 覆盖内联样式 */
            }
        }
        
        .page-header {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex; /* 使用flex布局 */
            justify-content: space-between; /* 一个靠左一个靠右 */
            align-items: center;
        }
        
        .page-title {
            font-size: 28px;
            color: var(--primary-text);
            margin: 0;
            padding: 0;
            font-weight: 600;
        }
        
        .home-link {
            color: #ffffff;
            text-decoration: none;
            background-color: #409eff;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            display: flex;
            align-items: center;
            transition: background-color 0.3s, transform 0.2s;
        }
        
        .home-link:hover {
            background-color: #66b1ff;
            transform: translateY(-2px);
        }
        
        .home-link i {
            margin-right: 8px;
        }
        
        .section {
            margin-bottom: 40px;
            background: var(--card-bg);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            transition: transform var(--transition-time), box-shadow var(--transition-time);
        }
        
        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-text);
            margin: 0;
        }
        
        .section-subtitle {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-text);
            margin-top: 0;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px dashed var(--border-color);
        }
        
        .add-button {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .add-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
        
        .chat-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr); /* 每行显示4个卡片 */
            gap: 16px;
            margin-top: 24px;
        }
        
        .limited-cards {
            max-height: calc(2 * (70px + 12px + 16px * 2 + 2px + 40px)); /* 调整为显示完整两行卡片的高度 */
            overflow: hidden;
        }
        
        .view-all-button {
            margin-top: 16px;
            text-align: center;
        }
        
        .chat-card {
            position: relative;
            border-radius: 8px;
            background: var(--card-bg);
            padding: 16px;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            transition: all var(--transition-time);
            overflow: hidden;
        }
        
        .chat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            border-color: var(--primary-color);
        }
        
        .chat-card .card-content {
            min-height: 70px; /* 缩小卡片内容高度 */
        }
        
        .chat-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-text);
            margin-bottom: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .chat-id {
            font-size: 12px;
            color: var(--secondary-text);
            margin-top: 6px;
        }
        
        .chat-remark {
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 500;
            margin-top: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .chat-group {
            margin-top: 4px;
        }
        
        .group-badge {
            display: inline-block;
            font-size: 12px;
            color: #606266;
            background-color: #f0f0f0;
            border-radius: 3px;
            padding: 2px 6px;
        }
        
        .chat-actions {
            margin-top: 12px;
            display: flex;
            justify-content: space-between;
        }
        
        .send-message-section {
            margin-top: 40px;
        }
        
        .el-form-item-tip {
            font-size: 12px;
            color: var(--secondary-text);
            margin-top: 5px;
        }
        
        .card-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--primary-color);
            color: white;
            font-size: 11px;
            padding: 3px 6px;
            border-bottom-left-radius: 6px;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        /* 表单弹窗样式 */
        .dialog-form .el-form-item {
            margin-bottom: 22px;
        }
        
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 24px;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c0c4cc;
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #909399;
        }
        
        ::-webkit-scrollbar-track {
            background: #f5f7fa;
            border-radius: 3px;
        }
        
        /* 响应式布局调整 */
        @media screen and (max-width: 768px) {
            .chat-cards {
                grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            }
            .container {
                padding: 16px;
            }
        }
        
        /* 分发队列样式 */
        .queue-control-area {
            margin-bottom: 20px;
        }
        
        .queue-form {
            margin-bottom: 20px;
        }
        
        .algorithm-description {
            margin: 15px 0;
        }
        
        .queue-display-area {
            margin-top: 30px;
        }
        
        /* 队列总长度样式 */
        .queue-total-count {
            display: inline-block;
            margin-left: 15px;
            padding: 5px 10px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
        }
        
        /* 队列数量间隔样式 */
        .queue-count-spacer {
            display: inline-block;
            width: 1px;
            height: 14px;
            background-color: white;
            margin: 0 10px;
            vertical-align: middle;
            opacity: 0.8;
        }
        
        /* 双表格布局 */
        .queue-tables {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }
        
        @media screen and (min-width: 1400px) {
            .queue-tables {
                display: grid;
                grid-template-columns: 1fr 1fr; /* 在大屏幕上使用两栏布局 */
                gap: 20px;
            }
        }
        
        .queue-table-wrapper {
            display: flex;
            flex-direction: column;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .queue-table-title {
            text-align: center;
            margin-bottom: 15px;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 16px;
            padding: 10px 0;
            background-color: #f9f9f9;
            border-bottom: 1px solid #ebeef5;
        }
        
        .queue-table-container {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        /* 表格行悬停效果 */
        .el-table--enable-row-hover .el-table__body tr:hover > td {
            background-color: #f0f7ff !important;
        }
        
        /* 表头样式 */
        .el-table th {
            background-color: #f5f7fa !important;
            color: #606266 !important;
            font-weight: 600 !important;
        }
        
        /* 表格内容区域边框 */
        .el-table--border .el-table__cell {
            border-right: 1px solid #ebeef5;
        }
        
        /* 条纹效果增强 */
        .el-table--striped .el-table__body tr.el-table__row--striped td {
            background-color: #fafafa;
        }
        
        /* 数据分发样式 */
        .distribute-form {
            margin-top: 20px;
        }
        
        .formatted-data-preview {
            background-color: var(--background);
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .formatted-data-header {
            background-color: var(--primary-lighter);
            padding: 10px;
            margin: 0;
            color: var(--primary-text);
            font-size: 14px;
            display: flex;
            justify-content: space-between;
        }
        
        .formatted-data-time {
            color: var(--regular-text);
            font-size: 12px;
        }
        
        .formatted-data-content {
            padding: 15px;
            background-color: white;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .formatted-data-content pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-word;
            font-family: monospace;
            font-size: 14px;
            color: var(--primary-text);
        }
        
        /* 算法说明样式 */
        .algorithm-explanation {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .algorithm-explanation h4 {
            color: #409EFF;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .algorithm-explanation ul {
            padding-left: 20px;
        }
        
        .algorithm-explanation li {
            margin-bottom: 5px;
        }
        
        /* 算法说明区域 */
        .algorithm-explanation {
            margin-bottom: 20px;
        }
        
        .algorithm-explanation p {
            margin-bottom: 10px;
        }
        
        .algorithm-explanation .algorithm-flow {
            margin-bottom: 10px;
        }
        
        .algorithm-explanation .algorithm-flow-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .algorithm-explanation .algorithm-flow-diagram {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }
        
        .algorithm-explanation .flow-step {
            display: flex;
            align-items: center;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .algorithm-explanation .flow-step-number {
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
        }
        
        .algorithm-explanation .flow-step-content {
            flex: 1;
        }
        
        .algorithm-explanation .flow-step-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .algorithm-explanation .flow-step-desc {
            color: var(--secondary-text);
        }
        
        .algorithm-explanation .flow-arrow {
            margin: 0 10px;
            font-size: 18px;
            color: var(--primary-color);
            font-weight: bold;
        }
        
        .algorithm-explanation .algorithm-note {
            background-color: #f0f0f0;
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .algorithm-explanation .el-icon-info-filled {
            margin-right: 8px;
        }
        
        /* 在小屏幕上调整布局 */
        @media (max-width: 768px) {
            .algorithm-explanation .algorithm-flow-diagram {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .algorithm-explanation .flow-step {
                margin-bottom: 15px;
            }
            
            .algorithm-explanation .flow-arrow {
                transform: rotate(90deg);
                margin: 5px 0;
            }
        }
        
        /* 队列统计卡片样式 */
        .queue-stats-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        
        .queue-stats-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--primary-text);
        }
        
        .queue-stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .queue-stats-item {
            flex: 1;
            text-align: center;
            padding: 2px 0;
        }
        
        .queue-stats-label {
            font-size: 12px;
            color: var(--secondary-text);
            margin-bottom: 3px;
        }
        
        .queue-stats-value {
            font-size: 15px;
            font-weight: 600;
        }
        
        .queue-stats-progress-container {
            height: 4px;
            background-color: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 3px;
        }
        
        .queue-stats-progress-bar {
            height: 100%;
            border-radius: 3px;
        }
        
        .free-progress {
            background-color: #67c23a;
        }
        
        .paid-progress {
            background-color: #409eff;
        }
        
        /* 队列项操作按钮样式 */
        .queue-item-actions {
            display: flex;
            gap: 5px;
        }
        
        .queue-item-status {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #e6a23c;
            color: #fff;
        }
        
        .status-distributed {
            background-color: #67c23a;
            color: #fff;
        }
        
        .status-cancelled {
            background-color: #f56c6c;
            color: #fff;
        }
        
        /* 删除队列按钮延迟确认样式 */
        .delayed-confirm-button {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #F56C6C !important;
            border-color: #F56C6C !important;
            color: #FFFFFF !important;
        }
        
        .delayed-confirm-button.can-confirm {
            opacity: 1;
            cursor: pointer;
            background-color: #F56C6C !important;
            border-color: #F56C6C !important;
            color: #FFFFFF !important;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        /* 队列表格容器样式 */
        .queue-tables-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }
        
        .queue-table-wrapper {
            width: 100%;
            margin-bottom: 20px;
        }
        
        /* 将两栏布局改为一栏垂直布局 */
        @media screen and (min-width: 1400px) {
            .queue-tables-container {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
        }
        
        /* ElementUI弹窗样式修改 */
        .el-message-box__btns .delayed-confirm-button.can-confirm:hover {
            opacity: 0.9;
            background-color: #DD6161 !important;
        }
        
        /* 监测功能样式 */
        .monitoring-section {
            margin-bottom: 24px;
        }
        
        .monitoring-controls {
            display: flex;
            align-items: center;
        }
        
        .monitoring-status-label {
            margin-right: 10px;
            font-size: 15px;
            color: var(--primary-text);
        }
        
        .monitoring-content {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-top: 8px;
        }
        
        .stats-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            display: flex;
            position: relative;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }
        
        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: #ffffff;
        }
        
        .stats-card:nth-child(1) .stats-icon {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        
        .stats-card:nth-child(2) .stats-icon {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .stats-card:nth-child(3) .stats-icon {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .stats-card:nth-child(4) .stats-icon {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }
        
        .stats-content {
            flex: 1;
        }
        
        .stats-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-text);
            margin-bottom: 4px;
        }
        
        .stats-label {
            font-size: 14px;
            color: var(--secondary-text);
        }
        
        .stats-total {
            position: static;
            margin-top: 48px;
            margin-left: 64px;
            font-size: 12px;
            color: var(--secondary-text);
        }
        
        .stats-trend {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .trend-up {
            color: #f56c6c;
            background-color: rgba(245, 108, 108, 0.1);
        }
        
        .trend-down {
            color: #67c23a;
            background-color: rgba(103, 194, 58, 0.1);
        }
        
        .trend-flat {
            color: #909399;
            background-color: rgba(144, 147, 153, 0.1);
        }
        
        .stats-updated {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 12px;
            color: var(--secondary-text);
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .date-filter {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: 8px;
        }
        
        /* 表格行状态样式 */
        .el-table .distributed-row {
            background-color: #f0f9eb !important;
        }
        .el-table .distributed-row:hover > td {
            background-color: #e1f3d8 !important;
        }
        
        .el-table .pending-row {
            cursor: pointer;
        }
        
        .el-table .cancelled-row {
            background-color: #fef0f0 !important;
            color: #999;
        }
        .el-table .cancelled-row:hover > td {
            background-color: #fde2e2 !important;
        }
        
        /* 表格状态行的样式 */
        .distributed-row td {
            background-color: rgba(103, 194, 58, 0.1) !important; /* 浅绿色 */
        }
        
        .pending-row td {
            background-color: rgba(144, 147, 153, 0.1) !important; /* 浅灰色 */
        }
        
        .cancelled-row td {
            background-color: rgba(245, 108, 108, 0.1) !important; /* 浅红色 */
        }
        
        /* 队列统计样式 */
        .queue-stats-container {
            margin-bottom: 20px;
            padding: 16px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .queue-stats-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--primary-text);
        }
        
        .queue-stats-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .queue-stats-item {
            flex: 1;
            background-color: #f9f9f9;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #eee;
        }
        
        .queue-stats-label {
            font-size: 14px;
            color: var(--secondary-text);
            margin-bottom: 8px;
        }
        
        .queue-stats-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-text);
            margin-bottom: 8px;
        }
        
        .queue-stats-progress-container {
            height: 6px;
            background-color: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .queue-stats-progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .queue-stats-progress-bar.free-progress {
            background-color: #67c23a; /* 绿色 */
        }
        
        .queue-stats-progress-bar.paid-progress {
            background-color: #e6a23c; /* 橙色 */
        }
        
        /* 队列表格容器样式 */
        .queue-tables-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .queue-table-wrapper {
            flex: 1;
            min-width: 300px;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="page-header">
            <h1 class="page-title">分发计划</h1>
            <a href="/home" class="home-link">
                <i class="fas fa-home"></i>
                返回首页
            </a>
        </div>
        
        <div class="content">
            
            <!-- 右侧栏扩展到100%宽度 -->
            <div class="right-column" style="width: 100%;">
                <div class="section fade-in">
                    <div class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; padding-bottom: 10px;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <h2 class="section-title" style="margin: 0; flex-shrink: 0;">分发队列</h2>
                            
                            <!-- 自动生成队列开关和时间选择器移到这里 -->
                            <div style="display: flex; align-items: center;">
                                <el-switch
                                    v-model="autoGenerateEnabled"
                                    active-text="自动生成队列"
                                    inactive-text=""
                                    @change="handleAutoGenerateChange"
                                    style="margin-right: 10px;">
                                </el-switch>
                                <el-time-picker
                                    v-model="autoGenerateTime"
                                    placeholder="自动生成时间"
                                    format="HH:mm"
                                    value-format="HH:mm"
                                    :disabled="!autoGenerateEnabled"
                                    style="width: 130px;"
                                    :default-value="new Date(2000, 0, 1, 9, 0, 0)"
                                    popper-class="time-picker-popper"
                                    @change="handleTimeChange" 
                                    :clearable="false">
                                </el-time-picker>
                            </div>
                        </div>
                        
                        <!-- 日期选择器保持在中间 -->
                        <div class="date-picker-container" style="display: flex; align-items: center; justify-content: center;">
                            <el-button 
                                type="text" 
                                @click="changeDate(-1)" 
                                style="margin-right: 5px;">
                                <i class="fas fa-chevron-left"></i>
                            </el-button>
                            <el-date-picker
                                v-model="queueForm.date"
                                type="date"
                                placeholder="选择日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                style="width: 240px;"
                            ></el-date-picker>
                            <el-button 
                                type="text" 
                                @click="changeDate(1)" 
                                style="margin-left: 5px;">
                                <i class="fas fa-chevron-right"></i>
                            </el-button>
                        </div>
                        
                        <!-- 所有操作按钮放在右侧 -->
                        <div style="flex-shrink: 0; display: flex; gap: 10px;">
                            <el-button 
                                type="warning" 
                                @click="updateQueue"
                                :loading="updateLoading">
                                <i class="fas fa-redo"></i> 更新分发队列
                            </el-button>
                            <el-button 
                                type="primary" 
                                @click="refreshQueueStats(true)"
                                :loading="statsLoading">
                                <i class="fas fa-sync"></i> 刷新统计
                            </el-button>
                            <el-button 
                                type="danger" 
                                @click="deleteQueue" 
                                :loading="deleteLoading">
                                <i class="fas fa-trash"></i> 删除队列
                            </el-button>
                            <el-button 
                                type="primary" 
                                @click="generateQueue" 
                                :loading="queueLoading"
                                :disabled="!queueForm.date || hasExistingQueue"
                                style="flex-shrink: 0;">
                                生成分发队列
                            </el-button>
                        </div>
                    </div>
                    <div>
                        <!-- 控制区域完全移除上边距 -->
                        <div class="queue-control-area" style="margin: 0; padding: 0; height: 0;">
                            <!-- 隐藏表单，保留功能但不占用空间 -->
                            <el-form :inline="true" class="queue-form" style="display: none;">
                                <el-form-item>
                                    <!-- 移除了原来的按钮和开关 -->
                                </el-form-item>
                            </el-form>
                        </div>
                        
                        <!-- 队列显示区域 -->
                        <div class="queue-display-area" v-if="true" style="margin-top: 0;">
                            <!-- 队列统计卡片 -->
                            <div class="queue-stats-card" v-if="queueStats" style="margin: 0 0 10px 0; padding-top: 5px;">
                                <div class="queue-stats-title">队列统计信息</div>
                                <!-- 显示所有统计信息 -->
                                <div class="queue-stats-row">
                                    <!-- 电商渠道统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">电商渠道队列</div>
                                        <div class="queue-stats-value">${ queueStats.ecommerce_distributed || 0 }$ / ${ queueStats.ecommerce_total || 0 }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar" 
                                                :style="{width: (queueStats.ecommerce_total > 0 ? ((queueStats.ecommerce_distributed || 0) / queueStats.ecommerce_total * 100) : 0) + '%',
                                                        backgroundColor: '#409EFF'}">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 新媒体渠道统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">新媒体渠道总量</div>
                                        <div class="queue-stats-value">${ (queueStats.free_distributed || 0) + (queueStats.paid_distributed || 0) }$ / ${ (queueStats.free_total || 0) + (queueStats.paid_total || 0) }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar" 
                                                :style="{width: ((queueStats.free_total + queueStats.paid_total) > 0 ? ((queueStats.free_distributed + queueStats.paid_distributed) / (queueStats.free_total + queueStats.paid_total) * 100) : 0) + '%',
                                                        backgroundColor: '#67c23a'}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="queue-stats-row">
                                    <!-- 免费队列统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">新媒体免费队列</div>
                                        <div class="queue-stats-value">${ queueStats.free_distributed || 0 }$ / ${ queueStats.free_total || 0 }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar free-progress" 
                                                :style="{width: (queueStats.free_total > 0 ? ((queueStats.free_distributed || 0) / queueStats.free_total * 100) : 0) + '%'}">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 付费队列统计 -->
                                    <div class="queue-stats-item">
                                        <div class="queue-stats-label">新媒体付费队列</div>
                                        <div class="queue-stats-value">${ queueStats.paid_distributed || 0 }$ / ${ queueStats.paid_total || 0 }$</div>
                                        <div class="queue-stats-progress-container">
                                            <div class="queue-stats-progress-bar paid-progress" 
                                                :style="{width: (queueStats.paid_total > 0 ? ((queueStats.paid_distributed || 0) / queueStats.paid_total * 100) : 0) + '%'}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 成员分发统计 -->
                                <div v-if="queueStats.member_statistics && queueStats.member_statistics.length > 0" style="margin-top: 8px;">
                                    <div class="queue-stats-title" style="margin-top: 5px; margin-bottom: 4px;">成员分发统计</div>
                                    
                                    <!-- 电商渠道成员统计 -->
                                    <div style="margin-bottom: 8px;">
                                        <div style="font-weight: 600; margin-bottom: 5px; font-size: 13px; color: #409EFF;">
                                            <i class="fas fa-shopping-cart" style="margin-right: 5px;"></i>电商渠道
                                        </div>
                                        <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-left: 10px;">
                                            <template v-if="getChannelMemberStats('电商渠道').length > 0">
                                                <div v-for="(memberStat, index) in getChannelMemberStats('电商渠道')" :key="'ecommerce-'+index" 
                                                    style="background-color: #ecf5ff; padding: 3px 8px; border-radius: 4px; font-size: 12px; border: 1px solid #d9ecff;">
                                                    <span style="font-weight: 600;">${ memberStat.member }$:</span> 
                                                    <span>${ memberStat.total_distributed }$条</span>
                                                </div>
                                            </template>
                                            <template v-else>
                                                <div style="color: #909399; font-size: 12px;">暂无分发数据</div>
                                            </template>
                                        </div>
                                    </div>
                                    
                                    <!-- 新媒体渠道成员统计 -->
                                    <div>
                                        <div style="font-weight: 600; margin-bottom: 5px; font-size: 13px; color: #67c23a;">
                                            <i class="fas fa-video" style="margin-right: 5px;"></i>新媒体渠道
                                        </div>
                                        <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-left: 10px;">
                                            <template v-if="getChannelMemberStats('新媒体渠道').length > 0">
                                                <div v-for="(memberStat, index) in getChannelMemberStats('新媒体渠道')" :key="'newmedia-'+index" 
                                                    style="background-color: #f0f9eb; padding: 3px 8px; border-radius: 4px; font-size: 12px; border: 1px solid #e1f3d8;">
                                                    <span style="font-weight: 600;">${ memberStat.member }$:</span> 
                                                    <span>${ memberStat.total_distributed }$条</span>
                                                </div>
                                            </template>
                                            <template v-else>
                                                <div style="color: #909399; font-size: 12px;">暂无分发数据</div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 没有队列数据时显示的内容 -->
                        <div class="no-queue-data" v-if="!hasExistingQueue && !queueGenerated">
                            <!-- 空白占位，移除所有内容 -->
                        </div>
                        
                        <div v-else>
                            <!-- 空白占位，移除所有内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
 
        <!-- 数据分发对话框 -->
        <el-dialog
            title="数据分发"
            v-model="distributeDialogVisible"
            width="600px"
            :close-on-click-modal="false">
            <el-form :model="distributeForm" label-width="100px" class="distribute-form">
                <el-form-item label="分发对象">
                    <p><strong>分组：</strong>${ distributeForm.group_name }$</p>
                    <p><strong>负责人：</strong>${ distributeForm.leader }$</p>
                    <p><strong>成员：</strong>${ distributeForm.member }$</p>
                </el-form-item>
                <el-form-item label="格式化数据" v-if="formattedData">
                    <div class="formatted-data-preview">
                        <p class="formatted-data-header">
                            <strong>ID:</strong> ${ formattedData.id }$ 
                            <span class="formatted-data-time">${ formatDateTime(formattedData.created_at) }$</span>
                        </p>
                        <div class="formatted-data-content">
                            <pre>${ formattedData.formatted_text }$</pre>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="distributeDialogVisible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
    <script src="/static/js/fetch.js"></script>
    <script src="https://unpkg.com/vue@3.2.47/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        const { createApp, ref, onMounted, watch, onUnmounted, computed } = Vue;
        
        const app = createApp({
            // 设置自定义分隔符，避免与Jinja2冲突
            delimiters: ['${', '}$'],
            
            setup() {
                console.log('Vue组件初始化...');
                // 群聊相关变量初始化为空值（钉钉功能已禁用）
                const chatForm = ref({
                    chat_id: '',
                    chat_name: '',
                    group_id: '',
                    member_id: '',
                    webhook_url: '',
                    secret: '',
                    owner_name: ''
                });
                
                // 弹窗显示控制
                const dialogVisible = ref(false);
                const allChatsDialogVisible = ref(false);
                
                // 编辑模式标志
                const isEditMode = ref(false);
                
                // 群聊列表（初始化为空数组）
                const chatList = ref([]);
                
                // 分组和成员列表
                const groupList = ref([]);
                const memberList = ref([]);
                const membersLoading = ref(false);
                
                // 搜索功能
                const searchQuery = ref('');
                const filteredChatList = ref([]);
                
                // 添加分组过滤功能
                const selectedGroupFilter = ref('');
                
                // 从现有群聊中提取唯一的分组名称
                const uniqueGroupNames = computed(() => {
                    // 从群聊列表中获取所有不重复的分组名称
                    const groupNames = new Set();
                    chatList.value.forEach(chat => {
                        const groupName = getGroupName(chat.group_id);
                        if (groupName !== '未知分组') {
                            groupNames.add(groupName);
                        }
                    });
                    // 转换为数组并排序
                    return Array.from(groupNames).sort();
                });
                
                // 消息表单数据
                const messageForm = ref({
                    chat_id: '',
                    type: 'text',
                    title: '',
                    content: ''
                });
                
                // 日期格式化函数
                const formatDate = (dateString) => {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };
                
                // 分发队列相关数据
                const queueForm = ref({
                    date: formatDate(new Date()),
                    channelType: '新媒体渠道'
                });
                
                const queueData = ref(null);
                const ecommerceQueueData = ref({ free_queue: [], paid_queue: [] });
                const newMediaQueueData = ref({ free_queue: [], paid_queue: [] });
                const queueLoading = ref(false);
                const queueGenerated = ref(false);
                const deleteLoading = ref(false);
                const updateLoading = ref(false);
                const hasExistingQueue = ref(false); // 新增变量，标记当日是否已有队列
                
                // 数据分发相关
                const distributeDialogVisible = ref(false);
                const distributeLoading = ref(false);
                const formattedData = ref(null);
                const distributeForm = ref({
                    group_name: '',
                    leader: '',
                    member: '',
                    position: 0,
                    chat_id: '',
                    type: 'text',
                    title: '',
                    content: ''
                });
                
                // 日期时间格式化函数
                const formatDateTime = (dateTimeString) => {
                    if (!dateTimeString) return '';
                    const date = new Date(dateTimeString);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                };
                
                // 添加统计相关的状态变量
                const queueStats = ref(null);
                const statsLoading = ref(false);
                
                // 添加自动生成队列相关的变量
                const autoGenerateEnabled = ref(false);
                const autoGenerateTime = ref('09:00');
                let autoGenerateTimer = null;
                
                // 处理自动生成队列开关变化
                const handleAutoGenerateChange = (value) => {
                    console.log('自动生成队列开关状态变更为:', value);
                    autoGenerateEnabled.value = value;
                    
                    // 保存开关状态到本地存储
                    localStorage.setItem('autoGenerateEnabled', value);
                    
                    // 根据开关状态设置或清除定时器
                    if (value) {
                        ElementPlus.ElMessage.success('自动生成队列已启用');
                        setAutoGenerateTimer();
                    } else {
                        ElementPlus.ElMessage.info('自动生成队列已禁用');
                        clearAutoGenerateTimer();
                    }
                };
                
                // 处理时间输入，但不立即应用
                const handleTimeInput = (value) => {
                    console.log('时间输入值:', value);
                    if (value) {
                        autoGenerateTime.value = value;
                    }
                };
                
                // 处理时间组件的确认按钮点击
                const handleTimeChange = (value) => {
                    console.log('时间确认为:', value);
                    if (value) {
                        try {
                            // 保存时间设置到本地存储
                            localStorage.setItem('autoGenerateTime', value);
                            
                            // 显示提示消息
                            ElementPlus.ElMessage.success(`自动生成队列时间已更新为 ${value}`);
                            
                            // 如果自动生成已启用，则重新设置定时器
                            if (autoGenerateEnabled.value) {
                                // 重新设置定时器
                                clearAutoGenerateTimer();
                                setAutoGenerateTimer();
                                console.log('已重新设置自动生成定时器');
                            }
                        } catch (error) {
                            console.error('保存时间设置出错:', error);
                            ElementPlus.ElMessage.error('保存时间设置失败');
                        }
                    }
                };
                
                // 设置自动生成队列的定时器
                const setAutoGenerateTimer = () => {
                    console.log('开始设置自动生成定时器');
                    if (!autoGenerateTime.value) {
                        console.error('自动生成时间未设置');
                        ElementPlus.ElMessage.warning('自动生成时间未设置，请先设置时间');
                        return false;
                    }
                    
                    try {
                        // 清除现有定时器
                        clearAutoGenerateTimer();
                        
                        // 解析时间字符串 (格式: HH:mm)
                        const [hours, minutes] = autoGenerateTime.value.split(':').map(Number);
                        
                        // 获取当前时间
                        const now = new Date();
                        
                        // 设置目标时间为今天的指定时间
                        const targetTime = new Date(
                            now.getFullYear(),
                            now.getMonth(),
                            now.getDate(),
                            hours,
                            minutes,
                            0
                        );
                        
                        // 如果目标时间已经过去，设置为明天的同一时间
                        if (targetTime <= now) {
                            targetTime.setDate(targetTime.getDate() + 1);
                        }
                        
                        // 计算距离目标时间的毫秒数
                        const timeUntilTarget = targetTime - now;
                        
                        // 记录下一次触发的时间
                        console.log(`下一次自动生成队列将在 ${targetTime.toLocaleString()} 触发，还有 ${Math.floor(timeUntilTarget / 60000)} 分钟`);
                        
                        // 设置定时器
                        autoGenerateTimer = setTimeout(() => {
                            console.log('定时器触发，开始自动生成队列');
                            // 检查是否应该继续自动生成
                            if (!autoGenerateEnabled.value) {
                                console.log('自动生成已被禁用，不执行队列生成');
                                return;
                            }
                            
                            // 执行队列生成
                            generateQueue();
                            
                            // 设置下一次定时器（24小时后的同一时间）
                            setAutoGenerateTimer();
                        }, timeUntilTarget);
                        
                        return true;
                    } catch (error) {
                        console.error('设置自动生成定时器失败:', error);
                        ElementPlus.ElMessage.error('设置自动队列生成定时器失败');
                        return false;
                    }
                };
                
                // 清除自动生成队列的定时器
                const clearAutoGenerateTimer = () => {
                    if (autoGenerateTimer) {
                        console.log('清除现有的自动生成定时器');
                        clearTimeout(autoGenerateTimer);
                        autoGenerateTimer = null;
                    }
                };
                
                // 计算下一次执行时间
                const calculateNextExecutionTime = (timeString) => {
                    const [hours, minutes] = timeString.split(':').map(Number);
                    const now = new Date();
                    const executionTime = new Date(now);
                    
                    executionTime.setHours(hours, minutes, 0, 0);
                    
                    // 如果设定时间已经过了，设置为明天的该时间
                    if (executionTime <= now) {
                        executionTime.setDate(executionTime.getDate() + 1);
                    }
                    
                    return executionTime;
                };
                
                // 执行自动生成队列
                const executeAutoGenerate = async () => {
                    console.log('执行自动生成队列...');
                    
                    try {
                        // 设置当天日期
                        queueForm.value.date = formatDate(new Date());
                        
                        // 检查是否已存在队列数据
                        const response = await fetch(`/api/distribution/queue/data?queue_date=${queueForm.value.date}&channel_type=${queueForm.value.channelType}`);
                        
                        if (response.ok) {
                            const data = await response.json();
                            
                            // 如果没有队列数据，则生成新队列
                            if (!data || (!data.free_queue || data.free_queue.length === 0) && (!data.paid_queue || data.paid_queue.length === 0)) {
                                console.log('当天没有队列数据，开始自动生成...');
                                await generateQueue();
                                ElementPlus.ElMessage.success(`已自动生成 ${queueForm.value.date} 的分发队列`);
                            } else {
                                console.log('当天已有队列数据，跳过自动生成');
                            }
                        } else {
                            console.error('检查队列数据失败，跳过自动生成');
                        }
                    } catch (error) {
                        console.error('自动生成队列出错:', error);
                    } finally {
                        // 设置下一次执行的定时器
                        if (autoGenerateEnabled.value) {
                            setAutoGenerateTimer();
                        }
                    }
                };
                
                // 在组件挂载时加载群聊列表和检查已保存的队列
                onMounted(async () => {
                    console.log('组件挂载开始...');
                    try {
                        // 先加载分组列表数据
                        console.log('正在加载分组列表...');
                        await loadGroupList();
                        console.log('分组列表加载完成');
                        
                        // 移除群聊加载，设置为空数组
                        chatList.value = [];
                        filteredChatList.value = [];
                        console.log('设置群聊列表为空数组');
                        
                        console.log('正在加载已保存队列...');
                        await loadSavedQueue();
                        console.log('已保存队列加载完成');
                        
                        console.log('正在刷新队列统计...');
                        await refreshQueueStats(false); // 初始加载时不重复加载队列数据
                        console.log('队列统计刷新完成');
                        
                        // 设置默认值，不再从URL参数获取
                        queueForm.value.date = formatDate(new Date()); // 默认使用当天日期
                        queueForm.value.channelType = '新媒体渠道'; // 默认使用新媒体渠道
                        
                        // 尝试从本地存储加载自动生成队列的设置
                        const savedAutoGenerateEnabled = localStorage.getItem('autoGenerateEnabled');
                        const savedAutoGenerateTime = localStorage.getItem('autoGenerateTime');
                        
                        console.log('从本地存储加载自动生成设置:', {
                            enabled: savedAutoGenerateEnabled,
                            time: savedAutoGenerateTime
                        });
                        
                        // 设置自动生成开关状态
                        autoGenerateEnabled.value = savedAutoGenerateEnabled === 'true';
                        
                        // 尝试加载保存的时间，如果存在则使用本地存储的值
                        if (savedAutoGenerateTime) {
                            console.log('设置自动生成时间为:', savedAutoGenerateTime);
                            autoGenerateTime.value = savedAutoGenerateTime;
                            
                            // 检查时间是否有效
                            const timePattern = /^([01]\d|2[0-3]):([0-5]\d)$/;
                            if (!timePattern.test(savedAutoGenerateTime)) {
                                console.error('本地存储的时间格式无效，重置为默认值09:00');
                                autoGenerateTime.value = '09:00';
                                localStorage.setItem('autoGenerateTime', '09:00');
                            }
                        } else {
                            // 如果没有保存的时间，设置默认值并保存
                            console.log('没有找到已保存的时间设置，使用默认值09:00');
                            autoGenerateTime.value = '09:00';
                            localStorage.setItem('autoGenerateTime', '09:00');
                        }
                        
                        // 只有在自动生成功能启用时，才设置定时器
                        if (autoGenerateEnabled.value) {
                            console.log('自动生成功能已启用，设置定时器');
                            setAutoGenerateTimer();
                        } else {
                            console.log('自动生成功能未启用，不设置定时器');
                        }
                        
                        console.log('组件挂载完成');
                    } catch (error) {
                        console.error('组件挂载过程中出错:', error);
                        ElementPlus.ElMessage.error('加载数据失败，请尝试刷新页面');
                    }
                });
                
                // 在组件卸载前清除定时器
                onUnmounted(() => {
                    // 清除定时器
                    clearAutoGenerateTimer();
                    console.log('组件卸载，已清除自动生成队列定时器');
                });
                
                // 处理搜索功能
                const handleSearch = () => {
                    console.log('搜索条件变更:', searchQuery.value);
                    applyFilters();
                };
                
                // 处理分组过滤变化
                const handleGroupFilterChange = () => {
                    console.log('分组筛选条件变更:', selectedGroupFilter.value);
                    applyFilters();
                };
                
                // 监听筛选条件变化，自动应用筛选
                watch([searchQuery, selectedGroupFilter], () => {
                    console.log('筛选条件变化，自动应用');
                    applyFilters();
                });
                
                // 应用所有过滤条件
                const applyFilters = () => {
                    // 钉钉功能已禁用，简化为空实现
                    console.log('钉钉功能已禁用，过滤功能不可用');
                };
                
                // 显示添加群聊对话框
                const showAddChatDialog = () => {
                    // 钉钉功能已禁用，显示提示消息
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，无法添加群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 显示编辑群聊对话框
                const editChat = (chat) => {
                    // 钉钉功能已禁用，显示提示消息
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，无法编辑群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 加载群聊列表
                const loadChatList = async () => {
                    // 钉钉功能已禁用，不再调用API，直接返回空数组
                    chatList.value = [];
                    filteredChatList.value = [];
                    console.log('钉钉功能已禁用，群聊列表设置为空');
                    return [];
                };
                
                // 检查同一分组内是否存在相同姓名的群聊
                const checkDuplicateOwnerInGroup = (groupId, ownerName, excludeChatId = null) => {
                    // 钉钉功能已禁用，简化为空实现
                    return null;
                };
                
                // 钉钉功能已禁用，移除了原addChat函数
                const addChat = async () => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，无法添加群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 钉钉功能已禁用，移除了原updateChat函数
                const updateChat = async () => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，无法更新群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 钉钉功能已禁用，移除了原deleteChat函数
                const deleteChat = async (chatId) => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，请使用其他方式管理群聊。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 钉钉功能已禁用，移除了原sendMessage函数
                const sendMessage = async () => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，请使用其他方式发送数据。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 更新接待数据的函数
                const updateReceptionCount = async (memberName, ruleDate, isPaid) => {
                    try {
                        // 先获取该成员的分发规则
                        const rulesResponse = await fetch(`/api/distribution/rules?rule_date=${ruleDate}`);
                        const rulesData = await rulesResponse.json();
                        
                        // 查找匹配的规则
                        const rule = rulesData.find(r => r.member === memberName);
                        if (!rule) {
                            console.warn(`未找到${memberName}的分发规则`);
                            return;
                        }
                        
                        // 准备更新数据
                        const actual_total = (rule.actual_total || 0) + 1;
                        // 修复计算逻辑，直接判断isPaid来决定增加哪个计数
                        let actual_free_reception = rule.actual_free || 0;
                        let actual_paid_reception = rule.actual_paid || 0;
                        
                        if (isPaid) {
                            actual_paid_reception += 1;
                        } else {
                            actual_free_reception += 1;
                        }
                        
                        console.log(`更新接待数据: 总数=${actual_total}, 免费=${actual_free_reception}, 付费=${actual_paid_reception}`);
                        
                        // 调用API更新实际接待数
                        const updateResponse = await fetch(`/api/distribution/update-actual-reception/${rule.id}?actual_reception=${actual_total}&actual_free_reception=${actual_free_reception}&actual_paid_reception=${actual_paid_reception}`, {
                            method: 'PUT'
                        });
                        
                        const updateData = await updateResponse.json();
                        console.log('更新接待数据结果：', updateData);
                        
                    } catch (error) {
                        console.error('更新接待数据API调用失败：', error);
                    }
                };
                
                // 钉钉功能已禁用，移除了原showAllChatsDialog函数
                const showAllChatsDialog = async () => {
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，群聊列表不可用。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 生成队列数据（处理电商渠道和新媒体渠道的不同数据结构）
                const generateQueueData = async (channelType) => {
                    try {
                        // 使用选定的日期
                        const formattedDate = formatDate(queueForm.value.date);
                        
                        console.log(`正在请求分发规则: ${formattedDate}, 渠道类型: ${channelType}`);
                        // 调用API获取分发规则
                        const rulesResponse = await fetch(`/api/distribution/rules?rule_date=${formattedDate}&channel_type=${channelType}`);
                        
                        if (!rulesResponse.ok) {
                            console.error(`获取 ${channelType} 分发规则失败: ${rulesResponse.status}`, rulesResponse);
                            return null;
                        }
                        
                        const rules = await rulesResponse.json();
                        console.log(`获取到 ${channelType} 规则数据共 ${rules.length} 条:`, rules);
                        
                        // 检查电商渠道是否有expected_total字段
                        if(channelType === '电商渠道') {
                            console.log('电商渠道规则数据字段:');
                            if(rules.length > 0) {
                                console.log('示例规则:', rules[0]);
                                console.log('expected_total:', rules[0].expected_total);
                                console.log('time_range:', rules[0].time_range);
                            } else {
                                console.warn('电商渠道没有规则数据!');
                            }
                        }
                        
                        // 实现智能分发算法
                        const allMembers = {};
                        
                        // 整理成员数据和容量限制 - 处理电商渠道和新媒体渠道不同的数据结构
                        rules.forEach(rule => {
                            if (!rule.member) return;
                            
                            if (!allMembers[rule.member]) {
                                allMembers[rule.member] = {
                                    name: rule.member,
                                    group_name: rule.group_name || '',
                                    leader: rule.leader || '',
                                    time_slots: [],
                                    free_capacity: 0,
                                    paid_capacity: 0,
                                    total_capacity: 0 // 添加总容量字段，用于电商渠道
                                };
                            }
                            
                            // 解析时间范围
                            const [start_time, end_time] = rule.time_range ? rule.time_range.split('-').map(t => t.trim()) : ['10:00', '23:59'];
                            
                            // 创建一个时间段对象
                            const timeSlot = {
                                start_time: start_time || '10:00',
                                end_time: end_time || '23:59',
                                free_capacity: 0,
                                paid_capacity: 0,
                                total_capacity: 0
                            };
                            
                            if (channelType === '电商渠道') {
                                // 电商渠道 - 只使用总容量，从expected_total字段获取
                                // 对于电商渠道，把所有预计接待数统一到total_capacity
                                timeSlot.total_capacity = rule.expected_total || 0;
                                allMembers[rule.member].total_capacity += timeSlot.total_capacity;
                                
                                console.log(`电商渠道 ${rule.member} 添加时间段 ${timeSlot.start_time}-${timeSlot.end_time}, 预计接待数 ${timeSlot.total_capacity}`);
                            } else {
                                // 新媒体渠道 - 使用免费和付费容量
                                timeSlot.free_capacity = rule.expected_free || 0;
                                timeSlot.paid_capacity = rule.expected_paid || 0;
                                allMembers[rule.member].free_capacity += timeSlot.free_capacity;
                                allMembers[rule.member].paid_capacity += timeSlot.paid_capacity;
                            }
                            
                            // 将时间段添加到成员的时间段列表
                            allMembers[rule.member].time_slots.push(timeSlot);
                        });
                        
                        // 检查是否有有效的成员数据
                        const validMembers = Object.values(allMembers).filter(member => {
                            if (channelType === '电商渠道') {
                                return member.time_slots.length > 0 && member.total_capacity > 0;
                            } else {
                                return member.time_slots.length > 0 && (member.free_capacity > 0 || member.paid_capacity > 0);
                            }
                        });
                        
                        if (validMembers.length === 0) {
                            console.warn(`${channelType} 没有找到有效的分发规则数据`);
                            return {
                                free_queue: [],
                                paid_queue: [],
                                total_items: 0,
                                channel_type: channelType
                            };
                        }
                        
                        // 打印有效成员数据
                        console.log(`${channelType} 有效成员数据:`, validMembers);
                        
                        // 辅助函数：检查时间是否在范围内
                        function isInTimeRange(currentTime, startTime, endTime) {
                            // 转换为分钟便于比较
                            const current = convertToMinutes(currentTime);
                            const start = convertToMinutes(startTime);
                            const end = convertToMinutes(endTime);
                            return current >= start && current <= end;
                        }
                        
                        function convertToMinutes(timeStr) {
                            const [hours, minutes] = timeStr.split(':').map(Number);
                            return hours * 60 + minutes;
                        }
                        
                        // 生成队列函数 - 处理电商渠道的总容量
                        function generateQueueForEcommerce() {
                            const queue = [];
                            // 使用total_capacity字段
                            const memberList = Object.values(allMembers).filter(member => 
                                member.total_capacity > 0
                            );
                            
                            console.log(`电商渠道有 ${memberList.length} 名有效成员`);
                            
                            if (memberList.length === 0) return [];
                            
                            // 初始时间设置为早上10点
                            let currentTime = "10:00";
                            let position = 1;
                            let availableMembers = [];
                            let currentIndex = 0;
                            
                            // 模拟时间流逝和分配过程
                            const timeSlots = ["10:00", "18:30", "23:59"];
                            let currentTimeSlotIndex = 0;
                            
                            while (true) {
                                // 更新当前时间
                                currentTime = timeSlots[currentTimeSlotIndex];
                                
                                // 根据当前时间，更新可用成员列表
                                availableMembers = memberList.filter(member => {
                                    // 检查成员是否有当前时间段的可用容量
                                    return member.total_capacity > 0 && member.time_slots.some(slot => {
                                        return isInTimeRange(currentTime, slot.start_time, slot.end_time)
                                            && slot.total_capacity > 0;
                                    });
                                });
                                
                                if (availableMembers.length === 0) {
                                    // 如果当前时间段没有可用成员，移到下一个时间段
                                    currentTimeSlotIndex++;
                                    if (currentTimeSlotIndex >= timeSlots.length) {
                                        break; // 所有时间段都处理完毕
                                    }
                                    continue;
                                }
                                
                                // 按轮序选择一个成员
                                const selectedMember = availableMembers[currentIndex % availableMembers.length];
                                
                                // 找到该成员适合当前时间的时间段
                                const suitableTimeSlot = selectedMember.time_slots.find(slot => 
                                    isInTimeRange(currentTime, slot.start_time, slot.end_time)
                                    && slot.total_capacity > 0
                                );
                                
                                if (suitableTimeSlot) {
                                    // 添加到队列
                                    queue.push({
                                        position: position++,
                                        group_name: selectedMember.group_name,
                                        leader: selectedMember.leader,
                                        member: selectedMember.name,
                                        time_slot: `${suitableTimeSlot.start_time}-${suitableTimeSlot.end_time}`,
                                        status: 'pending'
                                    });
                                    
                                    // 减少该成员的容量
                                    selectedMember.total_capacity--;
                                    suitableTimeSlot.total_capacity--;
                                }
                                
                                // 更新索引，使下一次选择不同的成员
                                currentIndex++;
                                
                                // 检查是否所有成员的容量都用完了
                                if (memberList.every(m => m.total_capacity <= 0)) {
                                    break;
                                }
                            }
                            
                            console.log(`为电商渠道生成了 ${queue.length} 条队列数据`);
                            return queue;
                        }
                        
                        // 原有的生成队列函数 - 用于新媒体渠道的免费和付费队列
                        function generateQueueByType(type) {
                            const queue = [];
                            const memberList = Object.values(allMembers).filter(member => 
                                member[`${type}_capacity`] > 0
                            );
                            
                            if (memberList.length === 0) return [];
                            
                            // 初始时间设置为早上10点
                            let currentTime = "10:00";
                            let position = 1;
                            let availableMembers = [];
                            let currentIndex = 0;
                            
                            // 模拟时间流逝和分配过程
                            const timeSlots = ["10:00", "18:30", "23:59"];
                            let currentTimeSlotIndex = 0;
                            
                            while (true) {
                                // 更新当前时间
                                currentTime = timeSlots[currentTimeSlotIndex];
                                
                                // 根据当前时间，更新可用成员列表
                                availableMembers = memberList.filter(member => {
                                    // 检查成员是否有当前时间段的可用容量
                                    return member[`${type}_capacity`] > 0 && member.time_slots.some(slot => {
                                        return isInTimeRange(currentTime, slot.start_time, slot.end_time)
                                            && slot[`${type}_capacity`] > 0;
                                    });
                                });
                                
                                if (availableMembers.length === 0) {
                                    // 如果当前时间段没有可用成员，移到下一个时间段
                                    currentTimeSlotIndex++;
                                    if (currentTimeSlotIndex >= timeSlots.length) {
                                        break; // 所有时间段都处理完毕
                                    }
                                    continue;
                                }
                                
                                // 按轮序选择一个成员
                                const selectedMember = availableMembers[currentIndex % availableMembers.length];
                                
                                // 找到该成员适合当前时间的时间段
                                const suitableTimeSlot = selectedMember.time_slots.find(slot => 
                                    isInTimeRange(currentTime, slot.start_time, slot.end_time)
                                    && slot[`${type}_capacity`] > 0
                                );
                                
                                if (suitableTimeSlot) {
                                    // 添加到队列
                                    queue.push({
                                        position: position++,
                                        group_name: selectedMember.group_name,
                                        leader: selectedMember.leader,
                                        member: selectedMember.name,
                                        time_slot: `${suitableTimeSlot.start_time}-${suitableTimeSlot.end_time}`,
                                        status: 'pending'
                                    });
                                    
                                    // 减少该成员的容量
                                    selectedMember[`${type}_capacity`]--;
                                    suitableTimeSlot[`${type}_capacity`]--;
                                }
                                
                                // 更新索引，使下一次选择不同的成员
                                currentIndex++;
                                
                                // 检查是否所有成员的容量都用完了
                                if (memberList.every(m => m[`${type}_capacity`] <= 0)) {
                                    break;
                                }
                            }
                            
                            return queue;
                        }
                        
                        if (channelType === '电商渠道') {
                            // 电商渠道：生成单一队列
                            const ecommerceQueue = generateQueueForEcommerce();
                            
                            return {
                                free_queue: ecommerceQueue, // 电商渠道队列存储在free_queue字段中
                                paid_queue: [],
                                total_items: ecommerceQueue.length,
                                channel_type: channelType
                            };
                        } else {
                            // 新媒体渠道：生成免费和付费两个队列
                            const freeQueue = generateQueueByType('free');
                            const paidQueue = generateQueueByType('paid');
                            
                            return {
                                free_queue: freeQueue,
                                paid_queue: paidQueue,
                                total_items: freeQueue.length + paidQueue.length,
                                channel_type: channelType
                            };
                        }
                    } catch (error) {
                        console.error(`生成 ${channelType} 队列数据出错:`, error);
                        return {
                            free_queue: [],
                            paid_queue: [],
                            total_items: 0,
                            channel_type: channelType
                        };
                    }
                };
                
                // 执行自动生成队列
                const generateQueue = async () => {
                    // 验证表单数据
                    if (!queueForm.value.date) {
                        ElementPlus.ElMessage.warning('请选择日期');
                        return;
                    }
                    
                    // 检查是否已存在队列
                    if (hasExistingQueue.value) {
                        ElementPlus.ElMessage.warning('当日已有分发队列，无法重新生成');
                        return;
                    }
                    
                    queueLoading.value = true;
                    queueGenerated.value = false;
                    
                    try {
                        // 依次生成电商渠道和新媒体渠道的队列数据
                        const channelTypes = ['电商渠道', '新媒体渠道'];
                        const results = {}; // 用于存储所有渠道的队列数据
                        
                        // 存储所有生成的队列
                        let totalItemsGenerated = 0;
                        
                        for (const channelType of channelTypes) {
                            try {
                                // 生成当前渠道类型的队列数据
                                console.log(`开始生成 ${channelType} 队列数据...`);
                                const result = await generateQueueData(channelType);
                                
                                if (result && (result.free_queue.length > 0 || result.paid_queue.length > 0)) {
                                    console.log(`成功生成 ${channelType} 队列数据:`, result);
                                    results[channelType] = result;
                                    totalItemsGenerated += result.total_items;
                                    
                                    // 保存到对应的渠道数据
                                    if (channelType === '电商渠道') {
                                        ecommerceQueueData.value = result;
                                    } else {
                                        newMediaQueueData.value = result;
                                    }
                                } else {
                                    console.warn(`${channelType} 队列数据为空`);
                                    const emptyResult = {
                                        free_queue: [],
                                        paid_queue: [],
                                        total_items: 0,
                                        channel_type: channelType
                                    };
                                    results[channelType] = emptyResult;
                                    
                                    // 保存到对应的渠道数据
                                    if (channelType === '电商渠道') {
                                        ecommerceQueueData.value = emptyResult;
                                    } else {
                                        newMediaQueueData.value = emptyResult;
                                    }
                                }
                            } catch (error) {
                                console.error(`生成 ${channelType} 队列数据出错:`, error);
                                ElementPlus.ElMessage.warning(`生成 ${channelType} 队列数据失败: ${error.message}`);
                                
                                const emptyResult = {
                                    free_queue: [],
                                    paid_queue: [],
                                    total_items: 0,
                                    channel_type: channelType
                                };
                                results[channelType] = emptyResult;
                                
                                // 保存到对应的渠道数据
                                if (channelType === '电商渠道') {
                                    ecommerceQueueData.value = emptyResult;
                                } else {
                                    newMediaQueueData.value = emptyResult;
                                }
                            }
                        }
                        
                        // 设置主队列数据，兼容现有逻辑
                        queueData.value = results[queueForm.value.channelType];
                        
                        console.log('当前显示的队列数据:', queueData.value);
                        
                        // 如果产生了队列数据，则自动保存
                        if (totalItemsGenerated > 0) {
                            // 按渠道类型分别保存队列
                            for (const channelType of channelTypes) {
                                if (!results[channelType] || (results[channelType].free_queue.length === 0 && results[channelType].paid_queue.length === 0)) {
                                    console.warn(`跳过保存 ${channelType} 队列，因为没有数据`);
                                    continue; // 跳过没有数据的渠道
                                }
                                
                                try {
                                    const saveData = {
                                        queue_date: queueForm.value.date,
                                        channel_type: channelType,
                                        free_queue: results[channelType].free_queue || [],
                                        paid_queue: results[channelType].paid_queue || []
                                    };
                                    
                                    console.log(`正在保存 ${channelType} 队列数据...`, saveData);
                                    const response = await fetch('/api/distribution/queue/save', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify(saveData)
                                    });
                                    
                                    if (response.ok) {
                                        console.log(`成功保存 ${channelType} 队列数据`);
                                    } else {
                                        const error = await response.json();
                                        console.error(`保存 ${channelType} 队列失败:`, error);
                                    }
                                } catch (error) {
                                    console.error(`保存 ${channelType} 队列出错:`, error);
                                }
                            }
                            
                            ElementPlus.ElMessage.success(`成功生成并保存分发队列数据，共 ${totalItemsGenerated} 条`);
                            queueGenerated.value = true;
                            hasExistingQueue.value = true; // 设置已有队列标记
                            
                            // 刷新统计信息，但不重新加载队列数据
                            await refreshQueueStats(false);
                        } else {
                            ElementPlus.ElMessage.warning('所有分发队列为空，没有数据需要保存');
                            queueGenerated.value = true;
                        }
                        
                    } catch (error) {
                        console.error('生成分发队列出错:', error);
                        ElementPlus.ElMessage.error('生成分发队列失败');
                        queueData.value = null;
                        ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                        newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                    } finally {
                        queueLoading.value = false;
                    }
                };
                
                // 处理分发数据
                const handleDistribute = async (row) => {
                    // 如果已经分发过了，不再处理
                    if (row.status === 'distributed' || row.status === '已分发') {
                        ElementPlus.ElMessage.info('该队列项已完成分发');
                        return;
                    }
                    
                    // 如果已取消，不再处理
                    if (row.status === 'cancelled' || row.status === '已取消') {
                        ElementPlus.ElMessage.warning('该队列项已被取消');
                        return;
                    }
                    
                    // 钉钉功能已禁用，显示提示消息
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，请使用其他方式分发数据。',
                        type: 'warning',
                        duration: 3000
                    });
                };
                
                // 数据分发功能
                const distributeMessage = async () => {
                    // 显示功能已禁用的提示
                    ElementPlus.ElMessage({
                        message: '钉钉机器人功能已禁用，请使用其他方式发送数据。',
                        type: 'warning',
                        duration: 3000
                    });
                    
                    // 关闭对话框
                    distributeDialogVisible.value = false;
                };
                
                // 添加保存队列函数
                const saveQueue = async () => {
                    if (!queueData.value || (!queueData.value.free_queue && !queueData.value.paid_queue)) {
                        ElementPlus.ElMessage.warning('没有队列数据可保存');
                        return;
                    }
                    
                    try {
                        const saveData = {
                            queue_date: queueForm.value.date,
                            channel_type: queueForm.value.channelType,
                            free_queue: queueData.value.free_queue || [],
                            paid_queue: queueData.value.paid_queue || []
                        };
                        
                        const response = await fetch('/api/distribution/queue/save', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(saveData)
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            ElementPlus.ElMessage.success(data.message || '队列保存成功');
                        } else {
                            const error = await response.json();
                            ElementPlus.ElMessage.error(error.detail || '保存队列失败');
                        }
                    } catch (error) {
                        console.error('保存队列出错:', error);
                        ElementPlus.ElMessage.error('保存队列失败');
                    }
                };
                
                // 加载已保存的队列数据
                const loadSavedQueue = async () => {
                    if (!queueForm.value.date) {
                        console.log('未设置日期，无法加载队列数据');
                        return;
                    }
                    
                    console.log(`正在加载队列数据: 日期=${queueForm.value.date}`);
                    
                    try {
                        // 加载电商渠道数据
                        const ecommerceResponse = await fetch(`/api/distribution/queue/data?queue_date=${queueForm.value.date}&channel_type=电商渠道`);
                        
                        if (ecommerceResponse.ok) {
                            const data = await ecommerceResponse.json();
                            console.log('获取到电商渠道队列数据:', data);
                            
                            // 如果存在已保存的队列数据
                            if (data && ((data.free_queue && data.free_queue.length > 0) || (data.paid_queue && data.paid_queue.length > 0))) {
                                console.log('发现已保存电商渠道队列数据');
                                ecommerceQueueData.value = data;
                                queueGenerated.value = true;
                                hasExistingQueue.value = true; // 设置已有队列标记
                            } else {
                                console.log('未找到已保存电商渠道队列数据');
                                ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                            }
                        } else {
                            console.error('加载电商渠道队列数据失败, HTTP状态码:', ecommerceResponse.status);
                            ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                        }
                        
                        // 加载新媒体渠道数据
                        const newMediaResponse = await fetch(`/api/distribution/queue/data?queue_date=${queueForm.value.date}&channel_type=新媒体渠道`);
                        
                        if (newMediaResponse.ok) {
                            const data = await newMediaResponse.json();
                            console.log('获取到新媒体渠道队列数据:', data);
                            
                            // 如果存在已保存的队列数据
                            if (data && ((data.free_queue && data.free_queue.length > 0) || (data.paid_queue && data.paid_queue.length > 0))) {
                                console.log('发现已保存新媒体渠道队列数据');
                                newMediaQueueData.value = data;
                                queueGenerated.value = true;
                                hasExistingQueue.value = true; // 设置已有队列标记
                            } else {
                                console.log('未找到已保存新媒体渠道队列数据');
                                newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                            }
                        } else {
                            console.error('加载新媒体渠道队列数据失败, HTTP状态码:', newMediaResponse.status);
                            newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                        }
                        
                        // 输出队列数据状态
                        console.log('加载完成后队列数据状态：', {
                            'ecommerceQueueData': {
                                'free_queue': ecommerceQueueData.value.free_queue ? ecommerceQueueData.value.free_queue.length : 0,
                                'paid_queue': ecommerceQueueData.value.paid_queue ? ecommerceQueueData.value.paid_queue.length : 0
                            },
                            'newMediaQueueData': {
                                'free_queue': newMediaQueueData.value.free_queue ? newMediaQueueData.value.free_queue.length : 0,
                                'paid_queue': newMediaQueueData.value.paid_queue ? newMediaQueueData.value.paid_queue.length : 0
                            }
                        });
                        
                        // 设置主队列数据，用于兼容现有功能
                        if (queueForm.value.channelType === '电商渠道') {
                            queueData.value = ecommerceQueueData.value;
                        } else {
                            queueData.value = newMediaQueueData.value;
                        }
                        
                        // 如果两个渠道都没有数据，则清空标志
                        if ((!ecommerceQueueData.value.free_queue || ecommerceQueueData.value.free_queue.length === 0) && 
                            (!newMediaQueueData.value.free_queue || newMediaQueueData.value.free_queue.length === 0) &&
                            (!newMediaQueueData.value.paid_queue || newMediaQueueData.value.paid_queue.length === 0)) {
                            queueGenerated.value = false;
                            hasExistingQueue.value = false;
                            ElementPlus.ElMessage.info(`${queueForm.value.date} 没有保存的队列数据`);
                        } else {
                            ElementPlus.ElMessage.success('已加载保存的队列数据');
                        }
                        
                    } catch (error) {
                        console.error('加载队列数据出错:', error);
                        // 发生错误也清空数据
                        queueData.value = null;
                        ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                        newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                        queueGenerated.value = false;
                        hasExistingQueue.value = false; // 设置无队列标记
                        queueStats.value = null;
                    }
                };
                
                // 修改日期或渠道类型时自动加载已保存队列和统计信息
                watch([() => queueForm.value.date, () => queueForm.value.channelType], async () => {
                    await loadSavedQueue();
                    // 移除自动刷新统计，避免频繁刷新
                    // await refreshQueueStats();
                }, { immediate: false });
                
                // 日期变化处理
                const changeDate = async (offset) => {
                    const currentDate = new Date(queueForm.value.date);
                    currentDate.setDate(currentDate.getDate() + offset);
                    queueForm.value.date = formatDate(currentDate);
                    
                    console.log(`日期已变更为: ${queueForm.value.date}`);
                    
                    // 不再更新URL参数
                    
                    // 手动触发加载已保存队列和刷新统计
                    await loadSavedQueue();
                    // 队列加载后只刷新统计数据，不再重新加载队列
                    await refreshQueueStats(false);
                };
                
                // 修改删除队列功能添加确认对话框并同时删除所有渠道数据
                const deleteQueue = async () => {
                    if (!queueForm.value.date) {
                        ElementPlus.ElMessage.warning('请先选择要删除的队列的日期');
                        return;
                    }
                    
                    // 声明倒计时变量
                    let countdown = 30;
                    let countdownTimer = null;
                    let confirmBoxInstance = null;
                    
                    try {
                        // 创建自定义确认对话框，包含倒计时功能
                        confirmBoxInstance = ElementPlus.ElMessageBox.confirm(
                            Vue.h('div', null, [
                                Vue.h('p', { style: 'color: red; font-weight: bold; font-size: 16px;' }, '该操作存在较大风险，请谨慎决定是否删除！！！'),
                                Vue.h('p', null, `确定要删除${queueForm.value.date}的所有分发队列数据吗？此操作不可恢复。`),
                                Vue.h('p', { class: 'countdown-text', style: 'margin-top: 10px;' }, `删除按钮在 ${countdown} 秒后可用`)
                            ]),
                            '危险操作警告',
                            {
                                showCancelButton: true,
                                confirmButtonText: '删除',
                                cancelButtonText: '取消',
                                type: 'warning',
                                confirmButtonClass: 'countdown-confirm-button danger-delete-button',
                                beforeClose: (action, instance, done) => {
                                    if (action === 'confirm') {
                                        // 检查是否倒计时已完成
                                        if (countdown > 0) {
                                            return false; // 阻止关闭
                                        }
                                    }
                                    
                                    // 清除倒计时
                                    if (countdownTimer) {
                                        clearInterval(countdownTimer);
                                        countdownTimer = null;
                                    }
                                    
                                    done(); // 允许关闭对话框
                                    return true;
                                }
                            }
                        );
                        
                        // 添加删除按钮的样式
                        const styleElement = document.createElement('style');
                        styleElement.textContent = `
                            .danger-delete-button {
                                background-color: #F56C6C !important;
                                border-color: #F56C6C !important;
                            }
                            .danger-delete-button:hover {
                                background-color: #f78989 !important;
                                border-color: #f78989 !important;
                            }
                        `;
                        document.head.appendChild(styleElement);
                        
                        // 禁用确认按钮
                        const confirmButton = document.querySelector('.countdown-confirm-button');
                        if (confirmButton) {
                            confirmButton.disabled = true;
                            confirmButton.style.opacity = '0.5';
                            confirmButton.style.cursor = 'not-allowed';
                        }
                        
                        // 开始倒计时
                        countdownTimer = setInterval(() => {
                            countdown--;
                            
                            // 更新倒计时文本
                            const countdownText = document.querySelector('.countdown-text');
                            if (countdownText) {
                                countdownText.textContent = `删除按钮在 ${countdown} 秒后可用`;
                            }
                            
                            // 倒计时结束
                            if (countdown <= 0) {
                                clearInterval(countdownTimer);
                                
                                // 启用确认按钮
                                if (confirmButton) {
                                    confirmButton.disabled = false;
                                    confirmButton.style.opacity = '1';
                                    confirmButton.style.cursor = 'pointer';
                                }
                                
                                if (countdownText) {
                                    countdownText.textContent = '删除按钮已可用，请谨慎操作！';
                                    countdownText.style.color = 'red';
                                }
                            }
                        }, 1000);
                        
                        // 等待用户确认
                        await confirmBoxInstance;
                        
                        // 用户点击确认并且倒计时已结束
                        deleteLoading.value = true;
                        console.log(`正在删除队列: 日期=${queueForm.value.date}`);
                        
                        // 分别删除两种渠道类型的队列
                        const formattedDate = formatDate(queueForm.value.date);
                        
                        // 删除电商渠道队列
                        const responseEcommerce = await fetch(`/api/distribution/queue/delete?queue_date=${formattedDate}&channel_type=电商渠道`, {
                            method: 'DELETE'
                        });
                        
                        // 删除新媒体渠道队列
                        const responseNewMedia = await fetch(`/api/distribution/queue/delete?queue_date=${formattedDate}&channel_type=新媒体渠道`, {
                            method: 'DELETE'
                        });
                        
                        console.log('删除队列API响应状态:', {
                            ecommerce: responseEcommerce.status,
                            newMedia: responseNewMedia.status
                        });
                        
                        // 清空所有队列数据
                        ecommerceQueueData.value = { free_queue: [], paid_queue: [] };
                        newMediaQueueData.value = { free_queue: [], paid_queue: [] };
                        queueData.value = { free_queue: [], paid_queue: [] };
                        queueGenerated.value = false;
                        hasExistingQueue.value = false;
                        queueStats.value = null;
                        
                        console.log('队列数据已成功清空');
                        ElementPlus.ElMessage.success(`删除成功: 所有分发队列数据已删除`);
                    } catch (error) {
                        // 清除倒计时
                        if (countdownTimer) {
                            clearInterval(countdownTimer);
                            countdownTimer = null;
                        }
                        
                        if (error === 'cancel') {
                            // 用户取消删除，不做任何操作
                            return;
                        }
                        console.error('删除队列出错:', error);
                        ElementPlus.ElMessage.error(`删除队列出错: ${error.message || '未知错误'}`);
                    } finally {
                        deleteLoading.value = false;
                    }
                };
                
                // 刷新队列统计信息
                const refreshQueueStats = async (shouldReloadQueue = false) => {
                    if (!queueForm.value.date) {
                        return;
                    }
                    
                    statsLoading.value = true;
                    
                    try {
                        // 获取新媒体渠道统计
                        const responseNewMedia = await fetch(`/api/distribution/queue/statistics?queue_date=${queueForm.value.date}&channel_type=新媒体渠道`);
                        const newMediaStats = responseNewMedia.ok ? await responseNewMedia.json() : null;
                        
                        // 获取电商渠道统计
                        const responseEcommerce = await fetch(`/api/distribution/queue/statistics?queue_date=${queueForm.value.date}&channel_type=电商渠道`);
                        const ecommerceStats = responseEcommerce.ok ? await responseEcommerce.json() : null;
                        
                        // 合并统计数据
                        const mergedStats = newMediaStats || {};
                        
                        // 添加电商渠道统计
                        if (ecommerceStats) {
                            mergedStats.ecommerce_total = ecommerceStats.free_total || 0;
                            mergedStats.ecommerce_distributed = ecommerceStats.free_distributed || 0;
                            
                            // 合并成员统计
                            if (ecommerceStats.member_statistics && ecommerceStats.member_statistics.length > 0) {
                                if (!mergedStats.member_statistics) {
                                    mergedStats.member_statistics = [];
                                }
                                
                                // 添加电商渠道的成员统计
                                ecommerceStats.member_statistics.forEach(ecommerceMemberStat => {
                                    const existingMemberStat = mergedStats.member_statistics.find(
                                        ms => ms.member === ecommerceMemberStat.member
                                    );
                                    
                                    if (existingMemberStat) {
                                        // 合并已有成员的统计
                                        existingMemberStat.total_distributed += ecommerceMemberStat.total_distributed;
                                    } else {
                                        // 添加新成员的统计
                                        mergedStats.member_statistics.push({ ...ecommerceMemberStat });
                                    }
                                });
                            }
                        }
                        
                        // 检查是否有实际的统计数据
                        if (mergedStats && (
                            mergedStats.free_total > 0 || 
                            mergedStats.paid_total > 0 || 
                            mergedStats.ecommerce_total > 0
                        )) {
                            queueStats.value = mergedStats;
                        } else {
                            // 没有统计数据，清空
                            queueStats.value = null;
                            console.log(`${queueForm.value.date} 没有队列统计数据`);
                        }
                    } catch (error) {
                        console.error('刷新队列统计信息出错:', error);
                        // 发生错误时清空统计数据
                        queueStats.value = null;
                    } finally {
                        statsLoading.value = false;
                    }
                    
                    // 只有在显式要求刷新队列数据时才加载
                    if (shouldReloadQueue) {
                        try {
                            await loadSavedQueue();
                            console.log('已刷新队列数据');
                        } catch (error) {
                            console.error('刷新队列数据失败:', error);
                        }
                    }
                };
                
                // 获取分组列表
                const loadGroupList = async () => {
                    try {
                        const response = await fetch('/api/presets/sales');
                        if (response.ok) {
                            const data = await response.json();
                            // 将sales数据转换为分组列表格式
                            const groups = (data.sales || []).map(sale => ({
                                id: sale.id,
                                name: sale.group
                            }));
                            groupList.value = groups;
                            console.log('获取分组列表成功:', groupList.value);
                        } else {
                            const error = await response.json();
                            ElementPlus.ElMessage.error(error.detail || '获取分组列表失败');
                            console.error('获取分组列表失败:', error);
                        }
                    } catch (error) {
                        console.error('获取分组列表出错:', error);
                        ElementPlus.ElMessage.error('获取分组列表失败');
                    }
                };
                
                // 根据分组ID获取成员列表
                const loadMemberList = async (groupId) => {
                    if (!groupId) {
                        memberList.value = [];
                        return;
                    }
                    
                    membersLoading.value = true;
                    try {
                        const response = await fetch('/api/presets/sales');
                        if (response.ok) {
                            const data = await response.json();
                            // 查找选中的分组
                            const selectedGroup = (data.sales || []).find(sale => sale.id === groupId);
                            
                            if (selectedGroup && selectedGroup.members) {
                                // 将成员字符串拆分为数组
                                const memberNames = selectedGroup.members.split(',').map(name => name.trim());
                                // 转换为所需格式
                                const members = memberNames.map((name, index) => ({
                                    id: `${groupId}-${index}`,
                                    name: name
                                }));
                                memberList.value = members;
                                console.log('获取成员列表成功:', memberList.value);
                            } else {
                                memberList.value = [];
                                console.log('未找到该分组的成员:', groupId);
                            }
                        } else {
                            const error = await response.json();
                            ElementPlus.ElMessage.error(error.detail || '获取成员列表失败');
                            console.error('获取成员列表失败:', error);
                        }
                    } catch (error) {
                        console.error('获取成员列表出错:', error);
                        ElementPlus.ElMessage.error('获取成员列表失败');
                    } finally {
                        membersLoading.value = false;
                    }
                };
                
                // 处理分组变更
                const handleGroupChange = (groupId) => {
                    console.log('分组变更为:', groupId);
                    // 清空成员选择
                    chatForm.value.member_id = '';
                    // 加载对应分组的成员
                    loadMemberList(groupId);
                };
                
                // 处理成员变更
                const handleMemberChange = (memberId) => {
                    console.log('成员变更为:', memberId);
                    // 自动设置owner_name为所选成员的名称
                    if (memberId) {
                        const selectedMember = memberList.value.find(member => member.id === memberId);
                        if (selectedMember) {
                            chatForm.value.owner_name = selectedMember.name;
                        }
                    }
                };
                
                // 根据分组ID获取分组名称
                const getGroupName = (groupId) => {
                    if (!groupId) return '未知分组';
                    
                    // 1. 尝试直接通过ID匹配分组
                    const groupById = groupList.value.find(g => g.id === groupId);
                    if (groupById) return groupById.name;
                    
                    // 2. 检查groupId本身是否就是分组名称
                    const groupByName = groupList.value.find(g => g.name === groupId);
                    if (groupByName) return groupId;
                    
                    // 3. 在API调用前显示可能是真实分组名称的groupId
                    if (typeof groupId === 'string' && groupId.trim() !== '') {
                        // 如果不像是UUID，可能是直接存储的名称
                        const uuidPattern = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
                        if (!uuidPattern.test(groupId)) {
                            return groupId;
                        }
                    }
                    
                    // 如果分组列表为空，主动加载
                    if (groupList.value.length === 0) {
                        console.log('分组列表为空，正在重新加载...');
                        loadGroupList();
                    }
                    
                    return '未知分组';
                };
                
                // 更新分发队列功能
                const updateQueue = async () => {
                    if (!queueForm.value.date) {
                        ElementPlus.ElMessage.warning('请选择要更新的队列的日期');
                        return;
                    }
                    
                    updateLoading.value = true;
                    
                    try {
                        // 首先确保已有队列数据
                        if (!hasExistingQueue.value) {
                            ElementPlus.ElMessage.warning('没有现有队列数据可以更新');
                            updateLoading.value = false;
                            return;
                        }
                        
                        // 定义一个内部函数，用于更新特定渠道的队列
                        const updateChannelQueue = async (channelType) => {
                            console.log(`正在更新${channelType}队列...`);
                            
                            // 获取当前已分发的队列项
                            let distributedItems = [];
                            let currentQueueData;
                            
                            if (channelType === '电商渠道') {
                                currentQueueData = ecommerceQueueData.value;
                            } else {
                                currentQueueData = newMediaQueueData.value;
                            }
                            
                            if (currentQueueData) {
                                // 收集已分发队列项
                                const freeDistributed = (currentQueueData.free_queue || [])
                                    .filter(item => item.status === 'distributed')
                                    .map(item => ({...item, queue_type: 'free'}));
                                
                                const paidDistributed = (currentQueueData.paid_queue || [])
                                    .filter(item => item.status === 'distributed')
                                    .map(item => ({...item, queue_type: 'paid'}));
                                
                                distributedItems = [...freeDistributed, ...paidDistributed];
                                
                                console.log(`找到${channelType}已分发队列项: ${distributedItems.length}条`);
                            }
                            
                            // 获取最新的分发规则
                            const formattedDate = formatDate(queueForm.value.date);
                            const rulesResponse = await fetch(`/api/distribution/rules?rule_date=${formattedDate}&channel_type=${channelType}`);
                            
                            // 如果API返回的是404（没有找到规则）或其他错误，也允许继续处理
                            let rules = [];
                            if (rulesResponse.ok) {
                                rules = await rulesResponse.json();
                                console.log(`获取到${channelType}分发规则:`, rules);
                            } else {
                                // 只记录日志，不再抛出错误，允许继续执行
                                console.warn(`获取${channelType}分发规则失败, 状态码:`, rulesResponse.status);
                                if (rulesResponse.status === 404) {
                                    console.warn(`${formattedDate} 的 ${channelType} 没有设置分发规则，将使用空规则列表`);
                                } else {
                                    try {
                                        const error = await rulesResponse.json();
                                        console.error(`获取分发规则错误详情:`, error);
                                    } catch (e) {
                                        console.error(`无法解析错误响应:`, e);
                                    }
                                }
                            }
                            
                            // 实现智能分发算法
                            const allMembers = {};
                            
                            // 修改：电商渠道的规则特殊处理
                            if (channelType === '电商渠道' && rules.length > 0) {
                                console.log(`处理电商渠道规则，共${rules.length}条规则`);
                                
                                // 打印一些调试信息
                                rules.forEach((rule, index) => {
                                    console.log(`规则${index + 1}:`, {
                                        member: rule.member,
                                        expected_free: rule.expected_free || rule.expected_total,
                                        expected_paid: rule.expected_paid || 0,
                                        time_range: rule.time_range
                                    });
                                });
                            }
                            
                            // 整理成员数据和容量限制
                            rules.forEach(rule => {
                                if (!rule.member) {
                                    console.warn(`发现无成员的规则:`, rule);
                                    return;
                                }
                                
                                if (!allMembers[rule.member]) {
                                    allMembers[rule.member] = {
                                        name: rule.member,
                                        group_name: rule.group_name || '',
                                        leader: rule.leader || '',
                                        time_slots: [],
                                        free_capacity: 0,
                                        paid_capacity: 0,
                                        free_distributed: 0,  // 已分发的免费数量
                                        paid_distributed: 0   // 已分发的付费数量
                                    };
                                }
                                
                                // 电商渠道特殊处理 - 假设所有接待都是免费的
                                if (channelType === '电商渠道') {
                                    const timeRange = rule.time_range || '10:00-23:59';
                                    const [start_time, end_time] = timeRange.split('-').map(t => t.trim());
                                    
                                    // 处理电商渠道的接待数
                                    const expected_free = rule.expected_free || rule.expected_total || 0;
                                    
                                    // 添加时间段
                                    const timeSlot = {
                                        start_time: start_time,
                                        end_time: end_time,
                                        free_capacity: expected_free,
                                        paid_capacity: 0 // 电商渠道默认没有付费接待
                                    };
                                    
                                    console.log(`给${rule.member}添加电商渠道时间段:`, timeSlot);
                                    
                                    allMembers[rule.member].time_slots.push(timeSlot);
                                    allMembers[rule.member].free_capacity += expected_free;
                                    // 电商渠道无付费容量
                                }
                                // 处理时间段信息
                                else if (rule.time_slots && Array.isArray(rule.time_slots)) {
                                    // 如果API直接返回time_slots数组
                                    rule.time_slots.forEach(slot => {
                                        allMembers[rule.member].time_slots.push(slot);
                                        // 累计每个成员的容量限制
                                        allMembers[rule.member].free_capacity += slot.free_capacity || 0;
                                        allMembers[rule.member].paid_capacity += slot.paid_capacity || 0;
                                    });
                                } else if (rule.time_range) {
                                    // 如果API返回time_range字段
                                    const [start_time, end_time] = rule.time_range.split('-').map(t => t.trim());
                                    
                                    const timeSlot = {
                                        start_time: start_time,
                                        end_time: end_time,
                                        free_capacity: rule.expected_free || 0,
                                        paid_capacity: rule.expected_paid || 0
                                    };
                                    
                                    allMembers[rule.member].time_slots.push(timeSlot);
                                    allMembers[rule.member].free_capacity += rule.expected_free || 0;
                                    allMembers[rule.member].paid_capacity += rule.expected_paid || 0;
                                }
                            });
                            
                            // 打印成员容量信息
                            console.log(`${channelType}成员容量信息:`, Object.entries(allMembers).map(([name, data]) => ({
                                name,
                                free_capacity: data.free_capacity,
                                paid_capacity: data.paid_capacity,
                                time_slots: data.time_slots.length
                            })));
                            
                            // 更新已分发的数量
                            distributedItems.forEach(item => {
                                if (allMembers[item.member]) {
                                    if (item.queue_type === 'free') {
                                        allMembers[item.member].free_distributed++;
                                    } else if (item.queue_type === 'paid') {
                                        allMembers[item.member].paid_distributed++;
                                    }
                                }
                            });
                            
                            // 计算剩余容量
                            Object.values(allMembers).forEach(member => {
                                // 新剩余量 = MAX(新规则预计量 - 已分发量, 0)
                                member.free_capacity = Math.max(member.free_capacity - member.free_distributed, 0);
                                member.paid_capacity = Math.max(member.paid_capacity - member.paid_distributed, 0);
                                
                                // 检查规则冲突（新规则量 < 已分发量）
                                if (member.free_capacity === 0 && member.free_distributed > 0) {
                                    console.warn(`规则冲突: ${member.name} 的免费接待数新规则量小于已分发量`);
                                }
                                if (member.paid_capacity === 0 && member.paid_distributed > 0) {
                                    console.warn(`规则冲突: ${member.name} 的付费接待数新规则量小于已分发量`);
                                }
                            });
                            
                            // 检查是否有有效的成员数据
                            const validMembers = Object.values(allMembers).filter(
                                member => member.time_slots.length > 0 && (member.free_capacity > 0 || member.paid_capacity > 0)
                            );
                            
                            // 特殊处理电商渠道 - 即使没有通过常规验证的有效成员，也尝试使用原始规则数据
                            if (validMembers.length === 0 && channelType === '电商渠道' && rules.length > 0) {
                                console.warn(`电商渠道没有通过常规验证的有效成员，将使用原始规则数据重新处理`);
                                
                                // 重新处理规则数据，强制使用简化逻辑
                                for (const rule of rules) {
                                    if (!rule.member) continue;
                                    
                                    // 清空现有数据
                                    if (allMembers[rule.member]) {
                                        allMembers[rule.member].time_slots = [];
                                        allMembers[rule.member].free_capacity = 0;
                                        allMembers[rule.member].paid_capacity = 0;
                                    } else {
                                        allMembers[rule.member] = {
                                            name: rule.member,
                                            group_name: rule.group_name || '',
                                            leader: rule.leader || '',
                                            time_slots: [],
                                            free_capacity: 0,
                                            paid_capacity: 0,
                                            free_distributed: 0,
                                            paid_distributed: 0
                                        };
                                    }
                                    
                                    // 直接获取预计接待数
                                    const expectedTotal = rule.expected_total || rule.expected_free || 20; // 默认为20
                                    
                                    // 创建默认时间段
                                    const timeSlot = {
                                        start_time: '10:00',
                                        end_time: '23:59',
                                        free_capacity: expectedTotal,
                                        paid_capacity: 0
                                    };
                                    
                                    allMembers[rule.member].time_slots.push(timeSlot);
                                    allMembers[rule.member].free_capacity = expectedTotal;
                                    
                                    console.log(`为${rule.member}强制设置电商渠道容量为${expectedTotal}`);
                                }
                                
                                // 重新计算有效成员
                                const forcedValidMembers = Object.values(allMembers).filter(
                                    member => member.time_slots.length > 0 && (member.free_capacity > 0 || member.paid_capacity > 0)
                                );
                                
                                if (forcedValidMembers.length > 0) {
                                    console.log(`成功找到${forcedValidMembers.length}个强制有效成员，将继续生成队列`);
                                } else {
                                    console.warn(`即使使用原始规则数据，仍然找不到有效成员`);
                                    
                                    // 创建一个空队列响应
                                    const emptyQueueResponse = {
                                        free_queue: distributedItems.filter(item => item.queue_type === 'free').map(item => {
                                            delete item.queue_type;
                                            return item;
                                        }),
                                        paid_queue: distributedItems.filter(item => item.queue_type === 'paid').map(item => {
                                            delete item.queue_type;
                                            return item;
                                        }),
                                        total_items: distributedItems.length,
                                        channel_type: channelType,
                                        algorithm_description: "智能时段轮序分发算法(更新版)：保留已分发记录，但没有可用成员继续分发。"
                                    };
                                    
                                    // 保存空队列
                                    const saveData = {
                                        queue_date: queueForm.value.date,
                                        channel_type: channelType,
                                        free_queue: emptyQueueResponse.free_queue,
                                        paid_queue: emptyQueueResponse.paid_queue
                                    };
                                    
                                    try {
                                        const response = await fetch('/api/distribution/queue/save', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json'
                                            },
                                            body: JSON.stringify(saveData)
                                        });
                                        
                                        if (response.ok) {
                                            console.log(`${channelType}空队列保存成功`);
                                            return emptyQueueResponse;
                                        } else {
                                            const error = await response.json();
                                            ElementPlus.ElMessage.error(error.detail || `${channelType}保存空队列失败`);
                                            return null;
                                        }
                                    } catch (saveError) {
                                        console.error(`保存${channelType}空队列失败:`, saveError);
                                        return null;
                                    }
                                }
                            }
                            // 原来的验证逻辑保持不变，但只对非电商渠道或电商渠道无规则数据的情况有效
                            else if (validMembers.length === 0) {
                                console.warn(`${channelType}没有有效的分发规则数据或所有成员剩余容量为0，将返回空队列`);
                                
                                // 创建一个空队列响应
                                const emptyQueueResponse = {
                                    free_queue: distributedItems.filter(item => item.queue_type === 'free').map(item => {
                                        delete item.queue_type;
                                        return item;
                                    }),
                                    paid_queue: distributedItems.filter(item => item.queue_type === 'paid').map(item => {
                                        delete item.queue_type;
                                        return item;
                                    }),
                                    total_items: distributedItems.length,
                                    channel_type: channelType,
                                    algorithm_description: "智能时段轮序分发算法(更新版)：保留已分发记录，但没有可用成员继续分发。"
                                };
                                
                                // 保存空队列
                                const saveData = {
                                    queue_date: queueForm.value.date,
                                    channel_type: channelType,
                                    free_queue: emptyQueueResponse.free_queue,
                                    paid_queue: emptyQueueResponse.paid_queue
                                };
                                
                                try {
                                    const response = await fetch('/api/distribution/queue/save', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify(saveData)
                                    });
                                    
                                    if (response.ok) {
                                        console.log(`${channelType}空队列保存成功`);
                                        return emptyQueueResponse;
                                    } else {
                                        const error = await response.json();
                                        ElementPlus.ElMessage.error(error.detail || `${channelType}保存空队列失败`);
                                        return null;
                                    }
                                } catch (saveError) {
                                    console.error(`保存${channelType}空队列失败:`, saveError);
                                    return null;
                                }
                            }
                            
                            // 辅助函数
                            function isInTimeRange(currentTime, startTime, endTime) {
                                const current = convertToMinutes(currentTime);
                                const start = convertToMinutes(startTime);
                                const end = convertToMinutes(endTime);
                                return current >= start && current <= end;
                            }
                            
                            function convertToMinutes(timeStr) {
                                const [hours, minutes] = timeStr.split(':').map(Number);
                                return hours * 60 + minutes;
                            }
                            
                            // 生成新队列函数
                            function generateNewQueueByType(type, startPosition) {
                                const queue = [];
                                const memberList = Object.values(allMembers).filter(member => 
                                    member[`${type}_capacity`] > 0
                                );
                                
                                if (memberList.length === 0) {
                                    console.warn(`没有${type}类型可用成员，返回空队列`);
                                    return [];
                                }
                                
                                // 针对电商渠道，使用简化的队列生成逻辑
                                if (channelType === '电商渠道') {
                                    console.log(`使用简化逻辑为电商渠道生成${type}队列`);
                                    
                                    let position = startPosition;
                                    let currentIndex = 0;
                                    
                                    // 循环分配队列项，确保每个成员的容量都用完
                                    while (memberList.some(member => member[`${type}_capacity`] > 0)) {
                                        const availableMembers = memberList.filter(member => member[`${type}_capacity`] > 0);
                                        if (availableMembers.length === 0) break;
                                        
                                        // 轮流选择成员
                                        const selectedMember = availableMembers[currentIndex % availableMembers.length];
                                        currentIndex++;
                                        
                                        // 时间段不限，选择该成员的第一个时间段
                                        if (selectedMember.time_slots.length > 0) {
                                            const timeSlot = selectedMember.time_slots[0];
                                            
                                            // 添加到队列
                                            queue.push({
                                                position: position++,
                                                group_name: selectedMember.group_name,
                                                leader: selectedMember.leader,
                                                member: selectedMember.name,
                                                time_slot: `${timeSlot.start_time}-${timeSlot.end_time}`,
                                                status: 'pending'
                                            });
                                            
                                            // 减少该成员的容量
                                            selectedMember[`${type}_capacity`]--;
                                        } else {
                                            // 如果没有时间段，创建默认时间段
                                            queue.push({
                                                position: position++,
                                                group_name: selectedMember.group_name,
                                                leader: selectedMember.leader,
                                                member: selectedMember.name,
                                                time_slot: `10:00-23:59`,
                                                status: 'pending'
                                            });
                                            
                                            // 减少该成员的容量
                                            selectedMember[`${type}_capacity`]--;
                                        }
                                    }
                                    
                                    console.log(`电商渠道${type}队列生成完成，共${queue.length}项`);
                                    return queue;
                                }
                                
                                // 原有逻辑，用于新媒体渠道
                                let position = startPosition;
                                let currentTime = "10:00"; // 初始时间
                                let availableMembers = [];
                                let currentIndex = 0;
                                
                                // 模拟时间流逝和分配过程
                                const timeSlots = ["10:00", "18:30", "23:59"];
                                let currentTimeSlotIndex = 0;
                                
                                // 获取当前系统时间所在的时段索引
                                const now = new Date();
                                const currentHour = now.getHours();
                                const currentMinute = now.getMinutes();
                                const currentSystemTime = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
                                
                                // 设置当前时段索引
                                if (convertToMinutes(currentSystemTime) >= convertToMinutes("18:30")) {
                                    currentTimeSlotIndex = 1; // 当前时间在18:30之后
                                }
                                
                                while (true) {
                                    // 更新当前时间
                                    currentTime = timeSlots[currentTimeSlotIndex];
                                    
                                    // 根据当前时间，更新可用成员列表
                                    availableMembers = memberList.filter(member => {
                                        return member[`${type}_capacity`] > 0 && member.time_slots.some(slot => {
                                            return isInTimeRange(currentTime, slot.start_time, slot.end_time)
                                                && slot[`${type}_capacity`] > 0;
                                        });
                                    });
                                    
                                    if (availableMembers.length === 0) {
                                        currentTimeSlotIndex++;
                                        if (currentTimeSlotIndex >= timeSlots.length) {
                                            break;
                                        }
                                        continue;
                                    }
                                    
                                    // 按轮序选择一个成员
                                    const selectedMember = availableMembers[currentIndex % availableMembers.length];
                                    
                                    // 找到该成员适合当前时间的时间段
                                    const suitableTimeSlot = selectedMember.time_slots.find(slot => 
                                        isInTimeRange(currentTime, slot.start_time, slot.end_time)
                                        && slot[`${type}_capacity`] > 0
                                    );
                                    
                                    if (suitableTimeSlot) {
                                        // 添加到队列
                                        queue.push({
                                            position: position++,
                                            group_name: selectedMember.group_name,
                                            leader: selectedMember.leader,
                                            member: selectedMember.name,
                                            time_slot: `${suitableTimeSlot.start_time}-${suitableTimeSlot.end_time}`,
                                            status: 'pending'
                                        });
                                        
                                        // 减少该成员的容量
                                        selectedMember[`${type}_capacity`]--;
                                        suitableTimeSlot[`${type}_capacity`]--;
                                    }
                                    
                                    // 更新索引，使下一次选择不同的成员
                                    currentIndex++;
                                    
                                    // 检查是否所有成员的容量都用完了
                                    if (memberList.every(m => m[`${type}_capacity`] <= 0)) {
                                        break;
                                    }
                                }
                                
                                return queue;
                            }
                            
                            // 获取已分发队列项的最大position
                            const maxFreePosition = Math.max(0, ...distributedItems
                                .filter(item => item.queue_type === 'free')
                                .map(item => item.position || 0));
                                
                            const maxPaidPosition = Math.max(0, ...distributedItems
                                .filter(item => item.queue_type === 'paid')
                                .map(item => item.position || 0));
                            
                            // 分别生成免费和付费队列
                            const freeQueue = generateNewQueueByType('free', maxFreePosition + 1);
                            const paidQueue = generateNewQueueByType('paid', maxPaidPosition + 1);
                            
                            // 合并已分发项和新生成的队列
                            const newFreeQueue = [
                                ...distributedItems.filter(item => item.queue_type === 'free').map(item => {
                                    delete item.queue_type;
                                    return item;
                                }),
                                ...freeQueue
                            ];
                            
                            const newPaidQueue = [
                                ...distributedItems.filter(item => item.queue_type === 'paid').map(item => {
                                    delete item.queue_type;
                                    return item;
                                }),
                                ...paidQueue
                            ];
                            
                            // 按position排序
                            newFreeQueue.sort((a, b) => a.position - b.position);
                            newPaidQueue.sort((a, b) => a.position - b.position);
                            
                            // 构建响应数据
                            const queueResponse = {
                                free_queue: newFreeQueue,
                                paid_queue: newPaidQueue,
                                total_items: newFreeQueue.length + newPaidQueue.length,
                                channel_type: channelType,
                                algorithm_description: "智能时段轮序分发算法(更新版)：保留已分发记录，根据新规则重新计算剩余容量，在当前时段开始生成新队列。"
                            };
                            
                            // 保存更新后的队列
                            if (queueResponse && (queueResponse.free_queue.length > 0 || queueResponse.paid_queue.length > 0)) {
                                // 准备保存的数据，确保保留已分发状态
                                const saveData = {
                                    queue_date: queueForm.value.date,
                                    channel_type: channelType,
                                    free_queue: queueResponse.free_queue,
                                    paid_queue: queueResponse.paid_queue
                                };
                                
                                const response = await fetch('/api/distribution/queue/save', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify(saveData)
                                });
                                
                                if (response.ok) {
                                    console.log(`${channelType}分发队列更新成功`);
                                    return queueResponse;
                                } else {
                                    const error = await response.json();
                                    ElementPlus.ElMessage.error(error.detail || `${channelType}更新队列失败`);
                                    return null;
                                }
                            } else {
                                // 即使没有生成新的队列项，也创建一个空队列并保存
                                console.warn(`${channelType}生成的更新队列为空，将保存空队列`);
                                
                                // 创建一个空队列响应
                                const emptyQueueResponse = {
                                    free_queue: [],
                                    paid_queue: [],
                                    total_items: 0,
                                    channel_type: channelType,
                                    algorithm_description: "智能时段轮序分发算法(更新版)：保留已分发记录，但没有符合条件的新队列项。"
                                };
                                
                                // 保存空队列
                                const saveData = {
                                    queue_date: queueForm.value.date,
                                    channel_type: channelType,
                                    free_queue: [],
                                    paid_queue: []
                                };
                                
                                try {
                                    const response = await fetch('/api/distribution/queue/save', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify(saveData)
                                    });
                                    
                                    if (response.ok) {
                                        console.log(`${channelType}空队列保存成功`);
                                        return emptyQueueResponse;
                                    } else {
                                        const error = await response.json();
                                        ElementPlus.ElMessage.error(error.detail || `${channelType}保存空队列失败`);
                                        return null;
                                    }
                                } catch (saveError) {
                                    console.error(`保存${channelType}空队列失败:`, saveError);
                                    return null;
                                }
                            }
                        };
                        
                        // 并行更新两个渠道的队列
                        const [ecommerceResult, newMediaResult] = await Promise.all([
                            updateChannelQueue('电商渠道'),
                            updateChannelQueue('新媒体渠道')
                        ]);
                        
                        // 更新本地缓存的队列数据
                        if (ecommerceResult) {
                            ecommerceQueueData.value = ecommerceResult;
                        }
                        
                        if (newMediaResult) {
                            newMediaQueueData.value = newMediaResult;
                        }
                        
                        // 根据当前选择的渠道类型设置主队列数据
                        if (queueForm.value.channelType === '电商渠道') {
                            queueData.value = ecommerceQueueData.value;
                        } else {
                            queueData.value = newMediaQueueData.value;
                        }
                        
                        // 显示更新结果
                        if (ecommerceResult && newMediaResult) {
                            ElementPlus.ElMessage.success('电商渠道和新媒体渠道分发队列都已成功更新');
                        } else if (ecommerceResult) {
                            ElementPlus.ElMessage.success('电商渠道分发队列已成功更新');
                            ElementPlus.ElMessage.warning('新媒体渠道分发队列更新失败');
                        } else if (newMediaResult) {
                            ElementPlus.ElMessage.success('新媒体渠道分发队列已成功更新');
                            ElementPlus.ElMessage.warning('电商渠道分发队列更新失败');
                        } else {
                            ElementPlus.ElMessage.error('所有渠道的分发队列更新都失败了');
                        }
                        
                        // 打印更新结果
                        console.log('更新队列结果:', {
                            电商渠道: ecommerceResult ? `成功 (${ecommerceResult.free_queue.length}免费/${ecommerceResult.paid_queue.length}付费)` : '失败',
                            新媒体渠道: newMediaResult ? `成功 (${newMediaResult.free_queue.length}免费/${newMediaResult.paid_queue.length}付费)` : '失败'
                        });
                        
                        // 刷新统计信息，不重复加载队列数据
                        await refreshQueueStats(false);
                    } catch (error) {
                        console.error('更新队列出错:', error);
                        ElementPlus.ElMessage.error('更新队列失败');
                    } finally {
                        updateLoading.value = false;
                    }
                };
                
                // 格式化时间显示
                const formatTime = (isoTime) => {
                    if (!isoTime) return '未知';
                    const date = new Date(isoTime);
                    return date.toLocaleString('zh-CN', { 
                        hour: '2-digit', 
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    });
                };
                
                // 组件挂载后初始化
                Vue.onMounted(async () => {
                    // 原有功能的初始化
                    console.log('分发计划页面初始化');
                    
                    // 加载数据
                    await Promise.all([
                        loadSavedQueue(),
                        loadGroupList(),
                        loadMemberList()
                    ]);
                    
                    // 检查URL参数中是否有日期
                    const urlParams = new URLSearchParams(window.location.search);
                    const dateParam = urlParams.get('date');
                    if (dateParam) {
                        queueForm.value.date = dateParam;
                    } else {
                        // 默认设置为今天
                        queueForm.value.date = formatDate(new Date());
                    }
                    
                    // 如果启用了自动生成，设置定时器
                    if (autoGenerateEnabled.value) {
                        setAutoGenerateTimer();
                    }
                });

                // 监听渠道类型变化，切换显示对应的队列数据
                const handleChannelTypeChange = () => {
                    console.log(`切换到 ${queueForm.value.channelType} 队列数据`);
                    loadSavedQueue(); // 重新加载对应渠道的队列数据
                    refreshQueueStats(false); // 更新队列统计信息
                };
                
                // 添加表格行类名函数，用于显示不同状态的行样式
                const getRowClassName = ({ row }) => {
                    if (row.status === 'distributed' || row.status === '已分发') {
                        return 'distributed-row';
                    } else if (row.status === 'cancelled' || row.status === '已取消') {
                        return 'cancelled-row';
                    } else {
                        return 'pending-row';
                    }
                };
                
                // 添加调试功能
                const debugQueueData = () => {
                    console.log('======== 调试队列数据 ========');
                    console.log('queueForm:', queueForm.value);
                    console.log('queueGenerated:', queueGenerated.value);
                    console.log('hasExistingQueue:', hasExistingQueue.value);
                    
                    console.log('电商渠道队列数据:', ecommerceQueueData.value);
                    console.log('电商渠道队列长度:', {
                        free_queue: ecommerceQueueData.value.free_queue ? ecommerceQueueData.value.free_queue.length : 0,
                        paid_queue: ecommerceQueueData.value.paid_queue ? ecommerceQueueData.value.paid_queue.length : 0
                    });
                    
                    console.log('新媒体渠道队列数据:', newMediaQueueData.value);
                    console.log('新媒体渠道队列长度:', {
                        free_queue: newMediaQueueData.value.free_queue ? newMediaQueueData.value.free_queue.length : 0,
                        paid_queue: newMediaQueueData.value.paid_queue ? newMediaQueueData.value.paid_queue.length : 0
                    });
                    
                    console.log('主队列数据:', queueData.value);
                    console.log('队列统计数据:', queueStats.value);
                    
                    // 重新加载队列数据
                    ElementPlus.ElMessage.info('正在重新加载队列数据，请查看控制台日志');
                    loadSavedQueue();
                };
                
                // 按渠道类型获取成员统计数据
                const getChannelMemberStats = (channelType) => {
                    if (!queueStats.value || !queueStats.value.member_statistics) {
                        return [];
                    }
                    
                    // 首先，确保member_statistics中的每个项都有channel_type属性
                    const statsWithChannel = queueStats.value.member_statistics.map(stat => {
                        // 如果已经有channel_type，则直接返回
                        if (stat.channel_type) {
                            return stat;
                        }
                        
                        // 否则，根据规则推断channel_type
                        // 从队列数据中查找该成员数据判断其所属渠道
                        let inferredChannelType = '未知';
                        
                        // 检查在电商渠道队列中是否存在该成员
                        const inEcommerce = ecommerceQueueData.value?.free_queue?.some(item => 
                            item.member === stat.member && item.status === 'distributed'
                        );
                        
                        // 检查在新媒体渠道队列中是否存在该成员
                        const inNewMedia = newMediaQueueData.value?.free_queue?.some(item => 
                            item.member === stat.member && item.status === 'distributed'
                        ) || newMediaQueueData.value?.paid_queue?.some(item => 
                            item.member === stat.member && item.status === 'distributed'
                        );
                        
                        // 确定渠道类型
                        if (inEcommerce && !inNewMedia) {
                            inferredChannelType = '电商渠道';
                        } else if (!inEcommerce && inNewMedia) {
                            inferredChannelType = '新媒体渠道';
                        } else if (inEcommerce && inNewMedia) {
                            // 如果两个渠道都有，默认归类为主要渠道
                            inferredChannelType = '新媒体渠道';
                        }
                        
                        return {
                            ...stat,
                            channel_type: inferredChannelType
                        };
                    });
                    
                    // 过滤出指定渠道类型的统计
                    return statsWithChannel.filter(stat => stat.channel_type === channelType);
                };
                
                return {
                    chatForm,
                    chatList,
                    filteredChatList,
                    searchQuery,
                    messageForm,
                    dialogVisible,
                    allChatsDialogVisible,
                    isEditMode,
                    showAddChatDialog,
                    editChat,
                    addChat,
                    updateChat,
                    loadChatList,
                    deleteChat,
                    sendMessage,
                    showAllChatsDialog,
                    handleSearch,
                    queueForm,
                    queueData,
                    queueLoading,
                    queueGenerated,
                    generateQueue,
                    handleDistribute,
                    formatDate,
                    distributeDialogVisible,
                    distributeLoading,
                    formattedData,
                    distributeForm,
                    distributeMessage,
                    formatDateTime,
                    changeDate,
                    saveQueue,
                    loadSavedQueue,
                    deleteQueue,
                    queueStats,
                    statsLoading,
                    refreshQueueStats,
                    autoGenerateEnabled,
                    autoGenerateTime,
                    handleAutoGenerateChange,
                    handleTimeChange,
                    groupList,
                    memberList,
                    membersLoading,
                    loadGroupList,
                    loadMemberList,
                    handleGroupChange,
                    handleMemberChange,
                    selectedGroupFilter,
                    getGroupName,
                    uniqueGroupNames,
                    checkDuplicateOwnerInGroup,
                    hasExistingQueue,
                    updateQueue,
                    deleteLoading,
                    updateLoading,
                    formatTime,
                    handleChannelTypeChange,
                    getRowClassName,
                    debugQueueData,
                    // 添加这两个缺失的变量
                    ecommerceQueueData,
                    newMediaQueueData,
                    // 添加监测相关占位变量，防止报错
                    monitoringEnabled: Vue.ref(false),
                    monitoringSwitchLoading: Vue.ref(false),
                    monitoringStats: Vue.ref({
                        todayCount: 0,
                        totalCount: 0,
                        todaySent: 0,
                        totalSent: 0,
                        waiting: 0,
                        waitingTrend: 0,
                        avgProcessTime: 0,
                        lastUpdateTime: null
                    }),
                    monitoringDateFilter: Vue.ref('today'),
                    customDateRange: Vue.ref([]),
                    handleMonitoringStatusChange: () => {},
                    handleDateFilterChange: () => {},
                    handleCustomDateRangeChange: () => {},
                    getChannelMemberStats,
                };
            }
        });
        
        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>
</html>