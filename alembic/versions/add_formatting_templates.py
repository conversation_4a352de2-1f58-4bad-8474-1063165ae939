"""add formatting templates table

Revision ID: add_formatting_templates
Create Date: 2024-03-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.mysql import JSON

# revision identifiers, used by Alembic.
revision = 'add_formatting_templates'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    op.create_table(
        'formatting_templates',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('name', sa.String(length=100), nullable=False),
        sa.<PERSON>umn('config', JSON, nullable=False),
        sa.<PERSON>umn('is_default', sa.<PERSON>(), default=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.<PERSON>onstraint('id')
    )
    op.create_index(op.f('ix_formatting_templates_id'), 'formatting_templates', ['id'], unique=False)

def downgrade():
    op.drop_index(op.f('ix_formatting_templates_id'), table_name='formatting_templates')
    op.drop_table('formatting_templates') 