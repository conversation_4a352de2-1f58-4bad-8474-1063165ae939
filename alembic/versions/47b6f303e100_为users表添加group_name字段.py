"""为users表添加group_name字段

Revision ID: 47b6f303e100
Revises: add_formatting_templates
Create Date: 2025-04-19 16:39:46.647031

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '47b6f303e100'
down_revision: Union[str, None] = 'add_formatting_templates'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('roles')
    op.drop_table('anchor_group_association')
    op.drop_table('anchor_group_members')
    op.drop_table('leads')
    op.drop_table('anchor_groups')
    op.alter_column('clue_sheet_2025', 'channel',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=100),
               nullable=True)
    op.alter_column('clue_sheet_2025', 'queue',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=100),
               type_=sa.String(length=10),
               nullable=True)
    op.alter_column('clue_sheet_2025', 'type',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=100),
               type_=sa.String(length=10),
               nullable=True)
    op.alter_column('clue_sheet_2025', 'is_read',
               existing_type=mysql.INTEGER(),
               nullable=True)
    op.alter_column('clue_sheet_2025', 'is_add',
               existing_type=mysql.INTEGER(),
               nullable=True)
    op.alter_column('clue_sheet_2025', 'reason_failure',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=255),
               type_=sa.String(length=500),
               nullable=True)
    op.drop_column('distribution_queues', 'store_name')
    op.alter_column('distribution_rules', 'shift',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=10),
               nullable=False)
    op.create_unique_constraint('unique_group_distribution', 'distribution_rules', ['date', 'channel_type', 'group_name'])
    op.drop_index('idx_rule_date', table_name='group_distribution_ratios')
    op.create_index(op.f('ix_group_distribution_ratios_rule_date'), 'group_distribution_ratios', ['rule_date'], unique=False)
    op.alter_column('schedules', 'rest_duration',
               existing_type=mysql.FLOAT(),
               nullable=True,
               comment='休息时长(小时)')
    op.alter_column('schedules', 'expected_count',
               existing_type=mysql.INTEGER(),
               nullable=True,
               comment='预计数量')
    op.alter_column('schedules', 'real_count',
               existing_type=mysql.INTEGER(),
               nullable=True,
               comment='实际数量')
    op.alter_column('schedules', 'live_count',
               existing_type=mysql.INTEGER(),
               nullable=True,
               comment='直播数量')
    # 仅为users表添加group_name字段
    op.add_column('users', sa.Column('ID', sa.String(length=14), nullable=False))
    op.alter_column('users', 'group_name',
               existing_type=mysql.TEXT(charset='utf8mb4', collation='utf8mb4_general_ci'),
               type_=sa.String(length=50),
               existing_nullable=True)
    op.drop_column('users', 'id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('id', mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=36), nullable=False, comment='用户唯一标识符'))
    op.alter_column('users', 'group_name',
               existing_type=sa.String(length=50),
               type_=mysql.TEXT(charset='utf8mb4', collation='utf8mb4_general_ci'),
               existing_nullable=True)
    op.alter_column('users', 'is_admin',
               existing_type=mysql.INTEGER(),
               comment='管理员身份',
               existing_nullable=False)
    op.alter_column('users', 'created_at',
               existing_type=mysql.DATETIME(),
               comment='创建时间',
               existing_nullable=False)
    op.alter_column('users', 'department',
               existing_type=sa.String(length=50),
               type_=mysql.TEXT(charset='utf8mb4', collation='utf8mb4_general_ci'),
               comment='部门',
               existing_nullable=False)
    op.alter_column('users', 'name',
               existing_type=sa.String(length=100),
               type_=mysql.TEXT(charset='utf8mb4', collation='utf8mb4_general_ci'),
               comment='真实姓名',
               existing_nullable=False)
    op.alter_column('users', 'contact',
               existing_type=sa.String(length=100),
               type_=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=120),
               comment='联系方式',
               existing_nullable=False)
    op.alter_column('users', 'password_hash',
               existing_type=sa.String(length=100),
               type_=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=256),
               comment='密码',
               existing_nullable=False)
    op.alter_column('users', 'account',
               existing_type=sa.String(length=100),
               type_=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_general_ci', length=80),
               comment='账号',
               existing_nullable=False)
    op.drop_column('users', 'ID')
    op.alter_column('schedules', 'live_count',
               existing_type=mysql.INTEGER(),
               nullable=False,
               comment=None,
               existing_comment='直播数量')
    op.alter_column('schedules', 'real_count',
               existing_type=mysql.INTEGER(),
               nullable=False,
               comment=None,
               existing_comment='实际数量')
    op.alter_column('schedules', 'expected_count',
               existing_type=mysql.INTEGER(),
               nullable=False,
               comment=None,
               existing_comment='预计数量')
    op.alter_column('schedules', 'rest_duration',
               existing_type=mysql.FLOAT(),
               nullable=False,
               comment=None,
               existing_comment='休息时长(小时)')
    op.drop_index(op.f('ix_group_distribution_ratios_rule_date'), table_name='group_distribution_ratios')
    op.create_index('idx_rule_date', 'group_distribution_ratios', ['rule_date'], unique=False)
    op.drop_constraint('unique_group_distribution', 'distribution_rules', type_='unique')
    op.alter_column('distribution_rules', 'shift',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=10),
               nullable=True)
    op.add_column('distribution_queues', sa.Column('store_name', mysql.VARCHAR(collation='utf8mb4_general_ci', length=200), nullable=True, comment='负责的特定店铺'))
    op.alter_column('clue_sheet_2025', 'reason_failure',
               existing_type=sa.String(length=500),
               type_=mysql.VARCHAR(collation='utf8mb4_general_ci', length=255),
               nullable=False)
    op.alter_column('clue_sheet_2025', 'is_add',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.alter_column('clue_sheet_2025', 'is_read',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.alter_column('clue_sheet_2025', 'type',
               existing_type=sa.String(length=10),
               type_=mysql.VARCHAR(collation='utf8mb4_general_ci', length=100),
               nullable=False)
    op.alter_column('clue_sheet_2025', 'queue',
               existing_type=sa.String(length=10),
               type_=mysql.VARCHAR(collation='utf8mb4_general_ci', length=100),
               nullable=False)
    op.alter_column('clue_sheet_2025', 'channel',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=100),
               nullable=False)
    op.create_table('anchor_groups',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(collation='utf8mb4_general_ci', length=50), nullable=False, comment='分组名称'),
    sa.Column('description', mysql.TEXT(collation='utf8mb4_general_ci'), nullable=True, comment='分组描述'),
    sa.Column('created_at', mysql.DATETIME(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('leads',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False, comment='线索ID'),
    sa.Column('lead_type', mysql.VARCHAR(collation='utf8mb4_general_ci', length=50), nullable=True, comment='线索类型'),
    sa.Column('contact_name', mysql.VARCHAR(collation='utf8mb4_general_ci', length=100), nullable=True, comment='联系人姓名'),
    sa.Column('phone', mysql.VARCHAR(collation='utf8mb4_general_ci', length=20), nullable=True, comment='电话号码'),
    sa.Column('wechat', mysql.VARCHAR(collation='utf8mb4_general_ci', length=100), nullable=True, comment='微信号'),
    sa.Column('source', mysql.VARCHAR(collation='utf8mb4_general_ci', length=200), nullable=True, comment='来源'),
    sa.Column('status', mysql.VARCHAR(collation='utf8mb4_general_ci', length=20), nullable=False, comment='状态'),
    sa.Column('category', mysql.VARCHAR(collation='utf8mb4_general_ci', length=50), nullable=True, comment='类别'),
    sa.Column('urgency', mysql.VARCHAR(collation='utf8mb4_general_ci', length=10), nullable=True, comment='紧急程度'),
    sa.Column('remark', mysql.TEXT(collation='utf8mb4_general_ci'), nullable=True, comment='备注'),
    sa.Column('channel_type', mysql.VARCHAR(collation='utf8mb4_general_ci', length=20), nullable=False, comment='渠道类型：电商渠道、新媒体渠道'),
    sa.Column('created_at', mysql.DATETIME(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True, comment='更新时间'),
    sa.Column('deleted_at', mysql.DATETIME(), nullable=True, comment='删除时间'),
    sa.Column('is_deleted', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True, comment='是否删除'),
    sa.Column('assigned_to', mysql.INTEGER(), autoincrement=False, nullable=True, comment='分配给'),
    sa.Column('created_by', mysql.INTEGER(), autoincrement=False, nullable=True, comment='创建人'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('anchor_group_members',
    sa.Column('anchor_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('group_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', mysql.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['anchor_id'], ['anchors.id'], name='anchor_group_members_ibfk_1'),
    sa.ForeignKeyConstraint(['group_id'], ['anchor_groups.id'], name='anchor_group_members_ibfk_2'),
    sa.PrimaryKeyConstraint('anchor_id', 'group_id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('anchor_group_association',
    sa.Column('anchor_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('group_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['anchor_id'], ['anchors.id'], name='anchor_group_association_ibfk_1'),
    sa.ForeignKeyConstraint(['group_id'], ['anchor_groups.id'], name='anchor_group_association_ibfk_2'),
    sa.PrimaryKeyConstraint('anchor_id', 'group_id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('roles',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(collation='utf8mb4_general_ci', length=100), nullable=False, comment='角色名称'),
    sa.Column('department', mysql.VARCHAR(collation='utf8mb4_general_ci', length=50), nullable=False, comment='所属部门'),
    sa.Column('permissions', mysql.VARCHAR(collation='utf8mb4_general_ci', length=500), nullable=False, comment='权限列表，逗号分隔'),
    sa.Column('created_at', mysql.DATETIME(), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
