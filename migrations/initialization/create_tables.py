from database import engine, Base
from models import ClueSheet, User, FormattingTemplate, MonitoringStatus, FormattedData
from sqlalchemy import inspect, Table, MetaData

def create_tables():
    # 获取数据库检查器
    inspector = inspect(engine)
    
    # 检查formatted_data表是否存在
    if 'formatted_data' in inspector.get_table_names():
        # 如果存在，删除它
        print("删除现有的formatted_data表...")
        metadata = MetaData()
        formatted_data = Table('formatted_data', metadata)
        metadata.reflect(bind=engine, only=['formatted_data'])
        formatted_data.drop(engine)
        print("formatted_data表已删除")
    
    # 创建所有定义的表
    Base.metadata.create_all(bind=engine)
    print("数据库表已创建或更新！")

if __name__ == "__main__":
    create_tables() 