"""
数据库迁移脚本：为线索表添加回收站相关字段
"""
import os
import sys
from datetime import datetime
from sqlalchemy import create_engine, text, Column, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 获取数据库连接信息
DB_HOST = os.getenv("DB_HOST")
DB_USER = os.getenv("DB_USER")
DB_PASS = os.getenv("DB_PASS")
DB_NAME = os.getenv("DB_NAME")
DB_PORT = os.getenv("DB_PORT", "3306")

# 构建数据库连接URL
DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# 创建数据库引擎
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def run_migration():
    """执行数据库迁移"""
    print("开始执行数据库迁移...")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 检查表是否存在
        table_name = "clue_sheet_2025"
        check_table_sql = text(f"SHOW TABLES LIKE '{table_name}'")
        result = db.execute(check_table_sql).fetchone()
        
        if not result:
            print(f"错误: 表 {table_name} 不存在!")
            return False
        
        # 检查字段是否已存在
        check_column_sql = text(f"SHOW COLUMNS FROM {table_name} LIKE 'is_deleted'")
        is_deleted_exists = db.execute(check_column_sql).fetchone()
        
        check_column_sql = text(f"SHOW COLUMNS FROM {table_name} LIKE 'delete_time'")
        delete_time_exists = db.execute(check_column_sql).fetchone()
        
        # 添加is_deleted字段（如果不存在）
        if not is_deleted_exists:
            print(f"添加 is_deleted 字段到 {table_name} 表...")
            add_column_sql = text(f"ALTER TABLE {table_name} ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE")
            db.execute(add_column_sql)
            print("is_deleted 字段添加成功!")
        else:
            print("is_deleted 字段已存在，跳过...")
        
        # 添加delete_time字段（如果不存在）
        if not delete_time_exists:
            print(f"添加 delete_time 字段到 {table_name} 表...")
            add_column_sql = text(f"ALTER TABLE {table_name} ADD COLUMN delete_time DATETIME NULL")
            db.execute(add_column_sql)
            print("delete_time 字段添加成功!")
        else:
            print("delete_time 字段已存在，跳过...")
        
        # 提交事务
        db.commit()
        print("数据库迁移成功完成!")
        return True
    
    except Exception as e:
        db.rollback()
        print(f"迁移过程中发生错误: {str(e)}")
        return False
    
    finally:
        db.close()

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1) 