from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接配置
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_USER = os.getenv("DB_USER", "root")
DB_PASSWORD = os.getenv("DB_PASSWORD", "root")
DB_NAME = os.getenv("DB_NAME", "lead_distribution")

SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"

# 创建数据库引擎
engine = create_engine(SQLALCHEMY_DATABASE_URL)

# 添加room列
try:
    with engine.connect() as connection:
        # 检查列是否已存在
        check_query = text("SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = :db_name AND TABLE_NAME = 'schedules' AND COLUMN_NAME = 'room'")
        result = connection.execute(check_query, {"db_name": DB_NAME}).scalar()
        
        if result == 0:
            # 列不存在，添加它
            alter_query = text("ALTER TABLE schedules ADD COLUMN room VARCHAR(100) NULL COMMENT '直播间' AFTER duration")
            connection.execute(alter_query)
            connection.commit()
            print("成功添加room列到schedules表")
        else:
            print("room列已存在于schedules表中")
except Exception as e:
    print(f"添加列时出错: {str(e)}") 