#!/usr/bin/env python3
"""
数据库迁移脚本：为前端权限表添加CRM权限字段

运行方式：
python add_crm_permissions.py

功能：
1. 为 user_permissions_frontend 表添加三个CRM权限字段
2. 设置默认值为1（禁止）
3. 检查字段是否已存在，避免重复添加
"""

import os
import sys
from sqlalchemy import create_engine, text, Column, Integer
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接配置
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_USER = os.getenv("DB_USER", "root")
DB_PASSWORD = os.getenv("DB_PASSWORD", "root")
DB_NAME = os.getenv("DB_NAME", "lead_distribution")

SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"

def check_column_exists(engine, table_name, column_name):
    """检查表中是否存在指定列"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text(f"""
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = '{DB_NAME}' 
                AND TABLE_NAME = '{table_name}' 
                AND COLUMN_NAME = '{column_name}'
            """))
            return result.fetchone()[0] > 0
    except Exception as e:
        print(f"检查列是否存在时出错: {e}")
        return False

def add_crm_permission_columns():
    """添加CRM权限字段到前端权限表"""
    try:
        # 创建数据库引擎
        engine = create_engine(SQLALCHEMY_DATABASE_URL)
        
        # 要添加的CRM权限字段
        crm_columns = [
            'check_person_crm',
            'check_group_crm', 
            'check_all_crm'
        ]
        
        table_name = 'user_permissions_frontend'
        
        print(f"开始为表 {table_name} 添加CRM权限字段...")
        
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                for column_name in crm_columns:
                    # 检查字段是否已存在
                    if check_column_exists(engine, table_name, column_name):
                        print(f"字段 {column_name} 已存在，跳过添加")
                        continue
                    
                    # 添加字段的SQL语句
                    sql = f"""
                        ALTER TABLE {table_name} 
                        ADD COLUMN {column_name} INT NOT NULL DEFAULT 1 
                        COMMENT 'CRM权限字段: 0=允许, 1=禁止'
                    """
                    
                    print(f"正在添加字段: {column_name}")
                    conn.execute(text(sql))
                    print(f"字段 {column_name} 添加成功")
                
                # 提交事务
                trans.commit()
                print("所有CRM权限字段添加完成！")
                
                # 验证字段是否添加成功
                print("\n验证字段添加结果:")
                for column_name in crm_columns:
                    exists = check_column_exists(engine, table_name, column_name)
                    status = "✓ 存在" if exists else "✗ 不存在"
                    print(f"  {column_name}: {status}")
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                raise e
                
    except Exception as e:
        print(f"添加CRM权限字段失败: {e}")
        return False
    
    return True

def update_existing_records():
    """更新现有记录，确保CRM权限字段有默认值"""
    try:
        engine = create_engine(SQLALCHEMY_DATABASE_URL)
        
        crm_columns = [
            'check_person_crm',
            'check_group_crm', 
            'check_all_crm'
        ]
        
        print("\n更新现有记录的CRM权限字段...")
        
        with engine.connect() as conn:
            trans = conn.begin()
            
            try:
                for column_name in crm_columns:
                    # 更新NULL值为默认值1
                    sql = f"""
                        UPDATE user_permissions_frontend 
                        SET {column_name} = 1 
                        WHERE {column_name} IS NULL
                    """
                    
                    result = conn.execute(text(sql))
                    affected_rows = result.rowcount
                    print(f"字段 {column_name}: 更新了 {affected_rows} 条记录")
                
                trans.commit()
                print("现有记录更新完成！")
                
            except Exception as e:
                trans.rollback()
                raise e
                
    except Exception as e:
        print(f"更新现有记录失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=== CRM权限字段迁移脚本 ===")
    print(f"数据库: {DB_HOST}/{DB_NAME}")
    print(f"用户: {DB_USER}")
    print()
    
    # 添加CRM权限字段
    if not add_crm_permission_columns():
        print("迁移失败！")
        sys.exit(1)
    
    # 更新现有记录
    if not update_existing_records():
        print("更新现有记录失败！")
        sys.exit(1)
    
    print("\n=== 迁移完成 ===")
    print("CRM权限字段已成功添加到数据库！")
    print("\n新增字段说明:")
    print("- check_person_crm: 查看个人线索权限")
    print("- check_group_crm: 查看组内线索权限") 
    print("- check_all_crm: 查看全部线索权限")
    print("- 默认值: 1 (禁止)")
    print("- 权限值: 0=允许, 1=禁止")

if __name__ == "__main__":
    main() 