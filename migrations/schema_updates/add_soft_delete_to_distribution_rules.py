#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为 DistributionRule 表添加软删除字段

功能：
1. 为 distribution_rules 表添加软删除相关字段
2. 为现有记录设置默认值

使用方法：
python migrations/schema_updates/add_soft_delete_to_distribution_rules.py
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from sqlalchemy import text
from database import SessionLocal, engine

def add_soft_delete_fields():
    """为 distribution_rules 表添加软删除字段"""
    db = SessionLocal()
    try:
        print("开始为 distribution_rules 表添加软删除字段...")
        
        # 检查字段是否已存在
        check_query = text("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'distribution_rules' 
            AND COLUMN_NAME IN ('is_deleted', 'delete_time', 'delete_user')
        """)
        
        existing_columns = db.execute(check_query).fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        # 添加 is_deleted 字段
        if 'is_deleted' not in existing_column_names:
            print("添加 is_deleted 字段...")
            db.execute(text("""
                ALTER TABLE distribution_rules 
                ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除'
            """))
            
            # 为现有记录设置默认值
            db.execute(text("""
                UPDATE distribution_rules 
                SET is_deleted = FALSE 
                WHERE is_deleted IS NULL
            """))
            print("✓ is_deleted 字段添加成功")
        else:
            print("✓ is_deleted 字段已存在")
        
        # 添加 delete_time 字段
        if 'delete_time' not in existing_column_names:
            print("添加 delete_time 字段...")
            db.execute(text("""
                ALTER TABLE distribution_rules 
                ADD COLUMN delete_time DATETIME NULL COMMENT '删除时间'
            """))
            print("✓ delete_time 字段添加成功")
        else:
            print("✓ delete_time 字段已存在")
        
        # 添加 delete_user 字段
        if 'delete_user' not in existing_column_names:
            print("添加 delete_user 字段...")
            db.execute(text("""
                ALTER TABLE distribution_rules 
                ADD COLUMN delete_user VARCHAR(50) NULL COMMENT '删除用户'
            """))
            print("✓ delete_user 字段添加成功")
        else:
            print("✓ delete_user 字段已存在")
        
        # 提交更改
        db.commit()
        
        print("\n✅ 软删除字段添加完成！")
        print("字段说明：")
        print("- is_deleted: 是否删除 (Boolean, 默认 FALSE)")
        print("- delete_time: 删除时间 (DateTime, 可为空)")
        print("- delete_user: 删除用户 (VARCHAR(50), 可为空)")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 迁移失败: {e}")
        raise
    finally:
        db.close()

def verify_migration():
    """验证迁移是否成功"""
    db = SessionLocal()
    try:
        print("\n验证迁移结果...")
        
        # 检查字段是否存在
        result = db.execute(text("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'distribution_rules' 
            AND COLUMN_NAME IN ('is_deleted', 'delete_time', 'delete_user')
            ORDER BY COLUMN_NAME
        """)).fetchall()
        
        if result:
            print("软删除字段详情：")
            for row in result:
                print(f"- {row[0]}: {row[1]}, 可空: {row[2]}, 默认值: {row[3]}, 注释: {row[4]}")
            
            # 检查现有记录的 is_deleted 字段值
            count_result = db.execute(text("""
                SELECT 
                    COUNT(*) as total_records,
                    SUM(CASE WHEN is_deleted = 0 THEN 1 ELSE 0 END) as active_records,
                    SUM(CASE WHEN is_deleted = 1 THEN 1 ELSE 0 END) as deleted_records
                FROM distribution_rules
            """)).fetchone()
            
            print(f"\n记录统计：")
            print(f"- 总记录数: {count_result[0]}")
            print(f"- 活跃记录数: {count_result[1]}")
            print(f"- 已删除记录数: {count_result[2]}")
            
            print("\n✅ 迁移验证成功！")
        else:
            print("❌ 软删除字段未找到，迁移可能失败")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("=" * 60)
    print("Distribution Rules 软删除字段迁移脚本")
    print("=" * 60)
    
    try:
        add_soft_delete_fields()
        verify_migration()
        
        print("\n" + "=" * 60)
        print("迁移完成！现在可以使用批量修改和批量删除功能了。")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 迁移过程中出现错误: {e}")
        sys.exit(1) 