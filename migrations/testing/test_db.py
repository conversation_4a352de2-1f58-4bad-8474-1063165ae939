from database import engine, SessionLocal
from sqlalchemy import text
from datetime import datetime

def test_insert():
    db = SessionLocal()
    try:
        # 尝试插入测试数据
        query = text("""
            INSERT INTO formatted_data (id, progress, formatted_text, created_at)
            VALUES (:id, :progress, :formatted_text, :created_at)
        """)
        
        db.execute(query, {
            "id": "test_id_123",
            "progress": 0,
            "formatted_text": "测试格式化文本",
            "created_at": datetime.now()
        })
        db.commit()
        print("测试数据插入成功")
        
        # 查询刚插入的数据
        result = db.execute(text("SELECT * FROM formatted_data WHERE id = 'test_id_123'")).fetchone()
        if result:
            print(f"查询结果: id={result.id}, progress={result.progress}, text={result.formatted_text}")
        else:
            print("未找到刚插入的数据")
    except Exception as e:
        print(f"测试插入失败: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("数据库URL:", engine.url)
    test_insert() 