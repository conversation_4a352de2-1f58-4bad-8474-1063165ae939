import requests
import json
import time
from datetime import datetime

def test_monitor():
    # 测试数据
    test_data = {
        "ID": f"test_{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "wechat_id": "test_wechat_id",
        "store": "测试店铺",
        "record_date": datetime.now().strftime("%Y-%m-%d"),
        "record_time": datetime.now().strftime("%H:%M:%S")
    }
    
    print(f"发送测试数据: {test_data}")
    
    # 发送请求
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/formatting/monitor",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("监控请求成功")
            
            # 等待数据处理完成
            print("等待5秒钟让数据处理完成...")
            time.sleep(5)
            
            # 查询是否成功保存到数据库
            check_response = requests.get("http://127.0.0.1:8000/api/formatting/formatted-data")
            if check_response.status_code == 200:
                formatted_data = check_response.json()
                print(f"获取到 {len(formatted_data)} 条格式化数据")
                
                # 检查是否包含我们刚刚发送的测试数据
                found = False
                for item in formatted_data:
                    if item["id"] == test_data["ID"]:
                        found = True
                        print(f"找到测试数据: {item}")
                        break
                
                if not found:
                    print("未找到刚刚发送的测试数据，可能未成功保存到数据库")
                    
                    # 直接查询数据库中的所有记录
                    print("尝试获取所有格式化数据记录...")
                    all_data_response = requests.get("http://127.0.0.1:8000/api/formatting/formatted-data?limit=100")
                    if all_data_response.status_code == 200:
                        all_data = all_data_response.json()
                        print(f"获取到 {len(all_data)} 条格式化数据")
                        
                        # 检查所有数据中是否包含测试数据
                        for item in all_data:
                            print(f"数据ID: {item['id']}")
                            if str(item["id"]) == test_data["ID"]:
                                print(f"在所有数据中找到测试数据: {item}")
                                found = True
                                break
                        
                        if not found:
                            print("在所有数据中也未找到测试数据")
                    else:
                        print(f"获取所有格式化数据失败: {all_data_response.status_code}")
            else:
                print(f"获取格式化数据失败: {check_response.status_code}")
        else:
            print(f"监控请求失败: {response.status_code}")
    except Exception as e:
        print(f"请求出错: {str(e)}")

if __name__ == "__main__":
    test_monitor() 