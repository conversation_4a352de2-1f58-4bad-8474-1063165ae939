CREATE TABLE user_permissions (
    id VARCHAR(36) PRIMARY KEY,
    account VARCHAR(80) NOT NULL,
    name TEXT NOT NULL,
    user_review INT NOT NULL DEFAULT 0,
    user_review_range INT NOT NULL DEFAULT 0,
    user_management INT NOT NULL DEFAULT 0,
    user_management_range INT NOT NULL DEFAULT 0,
    permission_management INT NOT NULL DEFAULT 0,
    form_preset INT NOT NULL DEFAULT 0,
    schedule_management INT NOT NULL DEFAULT 0,
    distribution_rules INT NOT NULL DEFAULT 0,
    distribution_plan INT NOT NULL DEFAULT 0,
    FOREIG<PERSON> KEY (id) REFERENCES users(ID)
);