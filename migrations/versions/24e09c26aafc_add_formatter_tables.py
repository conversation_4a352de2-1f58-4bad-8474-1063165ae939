"""add_formatter_tables

Revision ID: 24e09c26aafc
Revises: 
Create Date: 2024-03-06 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '24e09c26aafc'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('formatting_templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('template_id', sa.String(length=100), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=True),
    sa.Column('config', sa.J<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_formatting_templates_id'), 'formatting_templates', ['id'], unique=False)
    op.create_index(op.f('ix_formatting_templates_template_id'), 'formatting_templates', ['template_id'], unique=True)
    op.create_table('monitoring_status',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('is_enabled', sa.Boolean(), nullable=True, default=False),
    sa.Column('last_update', sa.DateTime(), nullable=True),
    sa.Column('data_count', sa.Integer(), nullable=True, default=0),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_monitoring_status_id'), 'monitoring_status', ['id'], unique=False)
    op.create_table('monitored_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.String(length=500), nullable=True),
    sa.Column('formatted_content', sa.String(length=1000), nullable=True),
    sa.Column('template_id', sa.String(length=100), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['template_id'], ['formatting_templates.template_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_monitored_data_id'), 'monitored_data', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_monitored_data_id'), table_name='monitored_data')
    op.drop_table('monitored_data')
    op.drop_index(op.f('ix_monitoring_status_id'), table_name='monitoring_status')
    op.drop_table('monitoring_status')
    op.drop_index(op.f('ix_formatting_templates_template_id'), table_name='formatting_templates')
    op.drop_index(op.f('ix_formatting_templates_id'), table_name='formatting_templates')
    op.drop_table('formatting_templates')
    # ### end Alembic commands ###
