from database import SessionLocal
from models import FormattedData, MonitoringStatus
from datetime import datetime, timedelta
import json
import random

def create_test_data():
    """创建测试监测数据"""
    db = SessionLocal()
    
    try:
        # 确保监测功能已启用
        status = db.query(MonitoringStatus).first()
        if not status:
            status = MonitoringStatus(is_enabled=True)
            db.add(status)
            db.commit()
        
        # 创建测试数据
        now = datetime.now()
        
        # 创建今日数据
        for i in range(10):
            # 随机创建时间
            created_time = now - timedelta(hours=random.randint(0, 5))
            
            # 构造测试数据
            test_data = {
                "ID": f"test_{created_time.strftime('%Y%m%d%H%M%S')}",
                "wechat_id": f"test_wechat_{i}",
                "store": f"测试店铺_{i % 3}",
                "record_date": created_time.strftime("%Y-%m-%d"),
                "record_time": created_time.strftime("%H:%M:%S"),
                "channel": "测试渠道",
                "remarks": "这是一条测试数据"
            }
            
            # 创建数据记录
            formatted_data = FormattedData(
                id=test_data["ID"],
                raw_data=json.dumps(test_data, ensure_ascii=False),
                formatted_text=f"这是格式化后的文本 #{i}",
                is_sent=i < 5,  # 将一半数据标记为已发送
                process_time=random.randint(10, 100),
                created_at=created_time
            )
            
            # 如果标记为已发送，设置发送时间
            if formatted_data.is_sent:
                formatted_data.sent_at = created_time + timedelta(minutes=random.randint(1, 10))
                formatted_data.sent_to = f"测试接收人_{i}"
            
            db.add(formatted_data)
        
        # 创建历史数据
        for i in range(20):
            # 随机创建时间（过去7天内）
            created_time = now - timedelta(days=random.randint(1, 7), hours=random.randint(0, 23))
            
            # 构造测试数据
            test_data = {
                "ID": f"history_{created_time.strftime('%Y%m%d%H%M%S')}",
                "wechat_id": f"history_wechat_{i}",
                "store": f"历史店铺_{i % 5}",
                "record_date": created_time.strftime("%Y-%m-%d"),
                "record_time": created_time.strftime("%H:%M:%S"),
                "channel": "历史渠道",
                "remarks": "这是一条历史数据"
            }
            
            # 创建数据记录
            formatted_data = FormattedData(
                id=test_data["ID"],
                raw_data=json.dumps(test_data, ensure_ascii=False),
                formatted_text=f"这是格式化后的历史文本 #{i}",
                is_sent=True,  # 历史数据都标记为已发送
                process_time=random.randint(10, 100),
                created_at=created_time,
                sent_at=created_time + timedelta(minutes=random.randint(1, 10)),
                sent_to=f"历史接收人_{i}"
            )
            
            db.add(formatted_data)
        
        # 保存数据
        db.commit()
        print(f"成功创建了 30 条测试数据：10条今日数据，20条历史数据")
        
    except Exception as e:
        db.rollback()
        print(f"创建测试数据出错: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    create_test_data() 