# 线索管理三表联查接口优化需求文档

## 项目背景

`/api/leads_sala_crm2` 接口是线索分发系统中的核心查询接口，负责整合线索基础信息、CRM数据和撤回记录三张表的数据。原有实现存在逻辑错误、权限控制缺失、过滤条件不完善等问题，需要进行全面优化。

## 优化目标

1. **修正查询逻辑**：将查询主表从 `ClueWithdraw` 修正为 `ClueSheet`
2. **完善权限控制**：添加基于用户部门和身份的数据访问权限控制
3. **增强过滤功能**：支持多种过滤条件和跨表搜索
4. **优化数据结构**：提供清晰的结构化响应数据
5. **提升错误处理**：完善参数验证和异常处理机制

## 技术实现

### 数据库表结构

涉及的三张核心表：

1. **clue_sheet_2025** (线索基础信息表) - 主表
   - 存储线索完整信息
   - 包含加密的敏感数据(手机号、微信号)
   - 通过 ID 字段与其他表关联

2. **sala_crm** (销售CRM数据表) - 关联表
   - 存储线索的销售跟进信息
   - 包含分配日期、跟进状态等业务数据
   - 与线索表一对一关联

3. **clue_withdraw** (线索撤回记录表) - 关联表
   - 记录线索的撤回申请和审批信息
   - 包含申请原因、审批状态等流程数据
   - 与线索表一对一关联(可选)

### 查询逻辑优化

#### 原有问题
- 以 `ClueWithdraw` 为主表，导致只能查询到有撤回记录的线索
- 查询字段不完整，缺少关键业务字段
- 权限控制缺失，存在数据安全风险

#### 优化方案
- **左外连接查询**：以 `ClueSheet` 为主表，确保所有线索都能被查询
- **字段优化**：选择完整的业务相关字段
- **权限控制**：根据用户部门和权限级别限制数据访问

```sql
-- 优化后的查询逻辑（伪代码）
SELECT 
    ClueSheet.*, 
    SalaCrm.*, 
    ClueWithdraw.*
FROM clue_sheet_2025 AS ClueSheet
LEFT JOIN sala_crm AS SalaCrm ON ClueSheet.ID = SalaCrm.ID
LEFT JOIN clue_withdraw AS ClueWithdraw ON ClueSheet.ID = ClueWithdraw.ID
WHERE ClueSheet.is_deleted = False
  AND [权限过滤条件]
  AND [业务过滤条件]
```

### 权限控制机制

基于用户的部门(`department`)和身份(`identity`)实现分级权限控制：

#### 销售部权限
- **个人权限**：只能查看 `contact_person` 为自己的线索
- **组内权限**：可以查看同 `group_name` 所有成员的线索
- **全部权限**：可以查看销售部所有线索

#### 新媒体部权限
- **个人权限**：只能查看 `registrar` 为自己的线索

#### 超级管理员
- **无限制访问**：可以查看所有数据

### 过滤条件支持

#### 1. 基础过滤
- **分配日期过滤**：支持 `YYYY-MM-DD` 和 `YYYY/MM/DD` 格式
- **关键词搜索**：跨三表多字段模糊搜索

#### 2. 高级过滤
- **多选过滤**：支持标签、阶段等字段的多值过滤
- **日期范围过滤**：支持开始日期和结束日期范围查询
- **单值过滤**：支持状态等字段的精确匹配

#### 3. 排序功能
- **智能字段识别**：自动识别排序字段所属表
- **跨表排序**：支持对任意表的字段进行排序
- **默认排序**：按记录时间倒序排列

### 数据安全处理

#### 敏感数据解密
自动解密存储在数据库中的加密字段：
- `phone_number`：手机号码
- `wechat_id`：微信号

#### 数据脱敏
根据用户权限级别，对敏感数据进行适当脱敏处理。

### 响应数据结构

采用分组结构，便于前端处理：

```json
{
    "success": true,
    "message": "查询成功", 
    "data": {
        "list": [
            {
                "clue_sheet": {
                    // 线索基础信息
                },
                "sala_crm": {
                    // CRM数据 (可能为null)
                },
                "clue_withdraw": {
                    // 撤回信息 (可能为null)
                }
            }
        ],
        "total": 100,
        "page": 1,
        "pageSize": 20,
        "totalPages": 5
    }
}
```

## 实现步骤

### 第一阶段：核心逻辑优化
1. ✅ 修正查询主表逻辑
2. ✅ 完善字段选择
3. ✅ 添加权限控制
4. ✅ 优化过滤条件处理

### 第二阶段：功能增强  
1. ✅ 实现智能排序
2. ✅ 添加数据验证
3. ✅ 完善错误处理
4. ✅ 优化响应结构

### 第三阶段：文档更新
1. ✅ 更新 API 接口文档
2. ✅ 创建需求文档
3. ✅ 添加使用示例

## 测试验证

### 功能测试点
1. **查询功能**：验证三表联查的正确性
2. **权限控制**：验证不同用户的数据访问权限
3. **过滤条件**：验证各种过滤条件的有效性
4. **排序功能**：验证跨表排序的准确性
5. **数据安全**：验证敏感数据的解密和脱敏

### 性能测试
1. **查询性能**：测试大数据量下的查询响应时间
2. **并发测试**：测试多用户同时访问的性能表现
3. **数据库负载**：监控数据库查询的执行计划和资源消耗

## 预期效果

### 功能改进
- ✅ 查询逻辑正确，以线索表为主表确保数据完整性
- ✅ 权限控制完善，保障数据安全
- ✅ 过滤功能强大，支持复杂查询需求
- ✅ 响应结构清晰，便于前端数据处理

### 性能提升
- 优化查询逻辑，减少不必要的数据传输
- 合理的索引使用，提升查询效率
- 分页查询，避免大数据量查询对系统的影响

### 可维护性
- 代码结构清晰，易于理解和维护
- 完善的错误处理，提高系统稳定性
- 详细的文档说明，便于后续开发

## 风险控制

### 数据安全风险
- ✅ 实施严格的权限控制机制
- ✅ 敏感数据加密存储和传输
- ✅ 访问日志记录和监控

### 系统稳定性风险
- ✅ 完善的异常处理机制
- ✅ 数据库连接池管理
- ✅ 请求参数验证

### 兼容性风险
- 保持接口路径不变，确保前端兼容
- 响应数据结构向后兼容
- 渐进式部署，降低升级风险

## 总结

通过本次优化，`/api/leads_sala_crm2` 接口的功能性、安全性和可维护性得到全面提升。接口现在能够：

1. **正确执行三表联查**：以线索表为主表，确保查询结果的完整性
2. **提供强大的过滤功能**：支持多种过滤条件和跨表搜索
3. **实现精细的权限控制**：根据用户角色限制数据访问范围
4. **保障数据安全**：自动处理敏感数据的加密和解密
5. **提供清晰的数据结构**：便于前端开发和数据处理

该优化方案在保持接口向后兼容的同时，显著提升了接口的功能性和安全性，为线索管理系统的后续发展奠定了坚实基础。

---

**文档版本**：v1.0  
**创建时间**：2025-01-15  
**作者**：AI Assistant  
**最后更新**：2025-01-15 