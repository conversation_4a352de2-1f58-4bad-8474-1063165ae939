# 分发规则查询接口优化需求文档

## 文档信息
- **文档版本**: 1.0
- **创建日期**: 2024-01-20
- **最后更新**: 2024-01-20
- **作者**: 系统开发团队

## 概述
本文档描述了对现有查询接口 `/api/distribution/rules2` 的优化需求，主要针对实际接待数量统计字段的计算和数据库更新功能。

## 接口信息
- **接口路径**: `/api/distribution/rules2`
- **请求方式**: `GET`
- **涉及数据表**: `ClueSheet`, `DistributionRule`, `SalaCrm`
- **功能描述**: 获取分发规则列表并实时更新实际接待数量统计信息

## 优化需求

### 核心功能
在不改变原有接口功能的前提下，增加对以下字段的计算与数据库更新操作：
- `actual_total`: 实际接待总数
- `actual_free`: 实际免费接待数
- `actual_paid`: 实际付费接待数

### 表关联关系
```sql
-- 主要关联关系
ClueSheet.ID = SalaCrm.ClueID
DistributionRule.member = ClueSheet.contact_person
DistributionRule.date = SalaCrm.allocation_date
```

### 字段计算规则

#### 1. 实际接待总数（actual_total）
- **查询条件**:
  - `ClueSheet.status IN (3, 4)`
  - `DistributionRule.date = SalaCrm.allocation_date`
  - `DistributionRule.member = ClueSheet.contact_person`
  - `ClueSheet.is_deleted = False`

#### 2. 实际免费接待数（actual_free）
- **查询条件**: 与实际接待总数相同，额外添加：
  - `ClueSheet.queue = '0'`

#### 3. 实际付费接待数（actual_paid）
- **查询条件**: 与实际接待总数相同，额外添加：
  - `ClueSheet.queue = '1'`

## 技术实现

### 实现方案
1. **保持原有功能不变**: 原有的查询、过滤、排序功能保持不变
2. **新增统计更新**: 在返回结果前，批量计算和更新实际接待数统计
3. **事务处理**: 使用数据库事务确保数据一致性
4. **性能优化**: 通过批量处理和联表查询提高效率

### 核心函数

#### 主接口函数
```python
@router.get("/api/distribution/rules2", response_model=List[DistributionRuleResponse])
def get_distribution_rules(
    rule_date: date = Query(None),
    channel_type: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
```

#### 统计更新函数
```python
def update_actual_reception_statistics(db: Session, rules: List[DistributionRule]):
```

### 执行流程
1. 根据查询条件获取分发规则列表
2. 调用统计更新函数批量计算实际接待数
3. 提交数据库事务
4. 重新查询获取更新后的数据
5. 返回包含最新统计数据的结果

## 数据库操作

### 查询示例
```sql
-- 实际接待总数统计
SELECT COUNT(*) 
FROM ClueSheet cs
JOIN SalaCrm sc ON cs.ID = sc.ClueID
WHERE cs.status IN (3, 4)
  AND sc.allocation_date = :rule_date
  AND cs.contact_person = :member
  AND cs.is_deleted = 0;

-- 免费接待数统计
SELECT COUNT(*) 
FROM ClueSheet cs
JOIN SalaCrm sc ON cs.ID = sc.ClueID
WHERE cs.status IN (3, 4)
  AND sc.allocation_date = :rule_date
  AND cs.contact_person = :member
  AND cs.queue = '0'
  AND cs.is_deleted = 0;

-- 付费接待数统计
SELECT COUNT(*) 
FROM ClueSheet cs
JOIN SalaCrm sc ON cs.ID = sc.ClueID
WHERE cs.status IN (3, 4)
  AND sc.allocation_date = :rule_date
  AND cs.contact_person = :member
  AND cs.queue = '1'
  AND cs.is_deleted = 0;
```

### 更新操作
```sql
UPDATE DistributionRule 
SET actual_total = :actual_total,
    actual_free = :actual_free,
    actual_paid = :actual_paid,
    updated_at = NOW()
WHERE id = :rule_id;
```

## 接口响应格式

### 请求参数
- `rule_date` (可选): 规则日期，格式为 YYYY-MM-DD
- `channel_type` (可选): 渠道类型，如 "电商渠道", "新媒体渠道"

### 响应数据
返回 `List[DistributionRuleResponse]`，包含以下关键字段：

```json
{
  "id": 1,
  "date": "2024-01-20",
  "channel_type": "电商渠道",
  "group_name": "销售一组",
  "member": "张三",
  "expected_total": 10,
  "expected_free": 6,
  "expected_paid": 4,
  "actual_total": 8,
  "actual_free": 5,
  "actual_paid": 3,
  "updated_at": "2024-01-20T10:30:00"
}
```

## 特性说明

### 新增功能（v2.1）
- ✅ 实时计算并更新实际接待数量统计
- ✅ 支持批量处理提高性能
- ✅ 事务处理确保数据一致性
- ✅ 详细的日志记录便于调试
- ✅ 完善的错误处理和回滚机制

### 兼容性
- ✅ 完全兼容原有接口功能
- ✅ 不影响现有客户端调用
- ✅ 保持相同的请求参数和响应格式

## 测试用例

### 基本功能测试
1. **无参数查询**: 获取所有分发规则并更新统计
2. **按日期查询**: 根据指定日期过滤规则
3. **按渠道查询**: 根据渠道类型过滤规则
4. **复合查询**: 同时使用日期和渠道类型过滤

### 数据验证测试
1. **统计准确性**: 验证计算结果与实际数据一致
2. **数据更新**: 确认统计数据正确更新到数据库
3. **事务处理**: 验证异常情况下的数据回滚

## 注意事项

### 性能考虑
- 批量处理减少数据库交互次数
- 联表查询优化查询效率
- 事务处理确保数据一致性但可能影响并发性能

### 数据一致性
- 使用数据库事务确保统计更新的原子性
- 异常情况下自动回滚避免数据不一致
- 详细的日志记录便于问题追踪

### 扩展性
- 统计逻辑独立封装，便于后续扩展
- 支持新增其他统计字段
- 可配置的统计规则

## 版本历史

### v2.1 (2024-01-20)
- 新增实际接待数量统计功能
- 实现批量统计计算和数据库更新
- 添加事务处理和错误处理机制
- 完善日志记录和调试信息

### v2.0 (之前版本)
- 基础的分发规则查询功能
- 支持按日期和渠道类型过滤
- 基本的排序和响应格式 