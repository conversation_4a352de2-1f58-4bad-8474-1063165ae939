# 线索提取三表联查功能需求文档

## 项目背景
在线索管理系统中，需要对三个关联表进行联查以获取完整的线索提取数据：
- `clue_withdraw`：线索提取记录表（主表）
- `clue_sheet_2025`：线索基础信息表
- `sala_crm`：销售CRM数据表  

这些表通过相同的ID字段关联，现在以 `clue_withdraw` 为主表，确保所有线索提取记录都能被返回，即使相关的线索基础信息或CRM数据不存在。

## 功能目标
1. **以ClueWithdraw为主表的三表联查**：确保所有提取记录都能被查询到
2. **数据完整性**：使用左外连接确保即使关联表缺少数据也能正常返回结果
3. **灵活过滤**：支持多种过滤条件和搜索方式
4. **分页排序**：支持分页查询和自定义排序
5. **数据安全**：对敏感数据进行解密处理

## 技术实现

### API接口
- **路径**：`POST /api/leads_withdraw`
- **描述**：以ClueWithdraw为主表的三表联查获取线索提取数据
- **请求参数**：
  ```python
  class LeadsWithdraw(BaseModel):
      page: Optional[int] = 1              # 页码，默认第1页
      pageSize: Optional[int] = 20         # 页面大小，默认20条
      allocation_date: Optional[str] = None # 分配日期过滤（针对SalaCrm表）
      keyword: Optional[str] = None        # 关键字搜索
      filters: Optional[str] = None        # 高级过滤条件（暂时简化）
      sortField: Optional[str] = None      # 排序字段
      sortOrder: Optional[str] = None      # 排序顺序
  ```

### 核心特性

#### 1. 三表联查策略优化
- **主表**：`clue_withdraw`（线索提取记录表）
- **左外连接**：`clue_sheet_2025` 和 `sala_crm`
- **连接条件**：通过 `ID` 字段关联
- **数据完整性**：确保即使关联表没有数据，也能返回 `ClueWithdraw` 的所有记录

#### 2. 查询逻辑
```sql
SELECT clue_withdraw.*, clue_sheet.*, sala_crm.*
FROM clue_withdraw
LEFT OUTER JOIN clue_sheet_2025 AS clue_sheet ON clue_withdraw.ID = clue_sheet.ID
LEFT OUTER JOIN sala_crm ON clue_withdraw.ID = sala_crm.ID
WHERE [过滤条件]
ORDER BY clue_withdraw.apply_time DESC
```

#### 3. 过滤功能
- **日期过滤**：支持对SalaCrm表的allocation_date进行过滤（考虑NULL值情况）
- **关键字搜索**：支持在所有三个表的相关字段中模糊搜索
  - **ClueWithdraw表**：ID、申请人、撤回原因、审批人
  - **SalaCrm表**：微信昵称、客户称呼、开单编号、关键词提示、线索标签
  - **ClueSheet表**：对接人、登记人、渠道、店铺
- **NULL值处理**：左外连接中的NULL值会被SQLAlchemy自动处理

#### 4. 排序功能
- **默认排序**：按 `clue_withdraw.apply_time` 降序排列
- **自定义排序**：支持对任意表的字段进行排序
- **智能字段识别**：自动识别排序字段属于哪个表

#### 5. 数据处理
- **日期格式化**：
  - ClueWithdraw表：`apply_time`、`shenpi_time`
  - SalaCrm表：`allocation_date`、`last_followup_time`、`add_date`
- **敏感数据解密**：对手机号和微信号进行解密（如果ClueSheet记录存在）
- **结构化返回**：按主表优先的顺序返回数据

### 返回数据格式
```json
{
    "success": true,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "clue_withdraw": {          // 主表数据（必定存在）
                    "ID": "线索ID",
                    "contact_person": "申请人",
                    "withdraw_reason": "撤回原因",
                    "withdraw_status": 0,
                    "apply_time": "2025-01-15 10:30:00",
                    // ... 其他提取字段
                },
                "clue_sheet": {             // 可能为null
                    "ID": "线索ID",
                    "phone_number": "手机号码（已解密）",
                    "wechat_id": "微信号（已解密）",
                    // ... 其他线索字段
                },
                "sala_crm": {               // 可能为null
                    "ID": "线索ID",
                    "allocation_date": "2025-01-15",
                    "customer_name": "客户姓名",
                    // ... 其他CRM字段
                }
            }
        ],
        "total": 100,        // 总记录数
        "page": 1,           // 当前页码
        "pageSize": 20,      // 页面大小
        "totalPages": 5      // 总页数
    }
}
```

## 主要改进点

### 查询策略优化
1. **主表变更**：从SalaCrm改为ClueWithdraw，确保所有提取记录都能被查询
2. **连接方式优化**：使用左外连接确保数据完整性
3. **排序逻辑**：默认按提取申请时间排序，更符合业务需求
4. **字段搜索**：支持跨三个表的字段搜索，提高查询灵活性

### 错误处理改进
1. **NULL值处理**：正确处理左外连接中的NULL值
2. **字段存在性检查**：动态检查排序字段是否存在
3. **异常捕获**：完整的异常处理和错误信息返回

### 数据结构优化
1. **主表优先**：返回数据结构中，主表数据放在第一位
2. **NULL标识**：明确标识哪些关联数据可能为null
3. **日期格式统一**：所有日期字段统一格式化

## 使用示例

### 基础查询
```python
data = {
    "page": 1,
    "pageSize": 20
}
```

### 带关键字搜索的查询
```python
data = {
    "page": 1,
    "pageSize": 20,
    "keyword": "张三",  # 会在所有表的相关字段中搜索
    "allocation_date": "2025-01-15"  # 过滤SalaCrm表的分配日期
}
```

### 自定义排序查询
```python
data = {
    "page": 1,
    "pageSize": 20,
    "sortField": "apply_time",  # 按申请时间排序
    "sortOrder": "asc"          # 升序排列
}
```

## 业务场景适配

### 适用场景
1. **线索提取审批管理**：查看所有线索提取申请记录
2. **提取记录追踪**：即使原始线索被删除，提取记录仍然保留
3. **审批状态监控**：监控所有提取申请的处理状态
4. **历史记录查询**：查询历史提取记录，不受原始数据影响

### 数据完整性保证
- 所有ClueWithdraw记录都会被返回
- 关联数据缺失时显示为null，不影响主要数据展示
- 支持单独基于提取记录的筛选和排序

## 注意事项
1. 左外连接查询性能考虑：在大数据量时注意索引优化
2. NULL值处理：前端需要正确处理可能为null的关联数据
3. 字段搜索范围：关键字搜索覆盖三个表，可能返回更多结果
4. 排序字段验证：确保排序字段在指定表中存在

## API接口更新

### PUT /api/shenpi_cehui

#### 功能描述
审核员进行撤回申请审批的接口。

#### 新增功能（版本1.2）
当 `withdraw_status = 1`（审核通过）时，新增以下业务逻辑：
- 将 `ClueWithdraw.contact_person` 的值追加到 `ClueSheet.not_admin` 字段中
- 多个联系人之间以英文逗号 `,` 分隔
- 若 `not_admin` 已有内容，则新内容接在其后，格式为：`原有内容,contact_person`

#### 实现逻辑
```python
# 在审核通过时执行
if data.withdraw_status == 1:
    # 将contact_person追加到ClueSheet的not_admin字段中
    if db_clue_withdraw.contact_person:
        if ClueSheetData.not_admin:
            # 如果not_admin已有内容，追加新内容
            ClueSheetData.not_admin = f"{ClueSheetData.not_admin},{db_clue_withdraw.contact_person}"
        else:
            # 如果not_admin为空，直接设置
            ClueSheetData.not_admin = db_clue_withdraw.contact_person
```

#### 注意事项
- 仅在审核通过（`withdraw_status = 1`）时执行此逻辑
- 确保字段拼接时不出现多余的逗号
- 涉及的数据表：`ClueWithdraw` 和 `ClueSheet`
- 保持最小改动原则，其他功能不受影响

### POST /api/distribution/media/auto-dispatch

#### 功能描述
线索自动分发接口，支持电商渠道和新媒体渠道的智能分发。

#### 新增功能（版本1.3）
当 `ClueSheet.not_admin` 字段存在值时（多个值以英文逗号 `,` 分隔）：
- 在所有分发场景中排除 `not_admin` 列表中的成员
- 确保撤回审批通过的成员不再接收相同线索的分发

#### 分发场景覆盖
1. **店铺匹配分发**：新媒体渠道店铺验证成功时，排除 `not_admin` 中的成员
2. **常规队列分发**：按队列顺序分发时，排除 `not_admin` 中的成员  
3. **队列转移分发**：当原队列已满转移到备用队列时，同样排除 `not_admin` 中的成员

#### 实现逻辑
```python
# 处理 not_admin 字段：将逗号分隔的字符串转换为列表
excluded_members = []
if lead.not_admin:
    # 拆分字符串并清理空格
    excluded_members = [member.strip() for member in lead.not_admin.split(',') if member.strip()]

# 在所有分发查询中添加排除条件
if excluded_members:
    query = query.filter(~DistributionQueue.member.in_(excluded_members))
```

#### 业务价值
- **避免重复分发**：确保已撤回线索的申请人不会再次接收到相同线索
- **提升分发效率**：减少无效分发，提高分发质量
- **完善审批流程**：与撤回审批功能形成完整的业务闭环

#### 注意事项
- 排除逻辑适用于所有分发场景，确保一致性
- 正确处理空值、空格等边界情况
- 涉及的数据表：`ClueSheet` 和 `DistributionQueue`
- 与 `/api/shenpi_cehui` 接口形成功能联动

## 版本更新
- **版本1.0** (2025-01-15)：完成三表联查基础功能
- **版本1.1** (2025-01-15)：优化为以ClueWithdraw为主表的左外连接查询
  - 支持分页、过滤、排序
  - 实现完整的数据完整性保证
  - 添加数据加密解密处理
- **版本1.2** (2025-01-16)：优化审批逻辑
  - `/api/shenpi_cehui` 接口新增联系人追加功能
  - 审核通过时将 `contact_person` 追加到 `not_admin` 字段
  - 更新 README.md 文档和需求文档
- **版本1.3** (2025-01-16)：增强线索分发逻辑
  - `/api/distribution/media/auto-dispatch` 接口优化分发机制
  - 在分发时排除 `ClueSheet.not_admin` 中列出的成员
  - 实现与撤回审批功能的联动 