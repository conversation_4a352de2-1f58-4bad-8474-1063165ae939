#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建分发队列数据表
"""

import sys
import os
import logging
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# 确保当前脚本所在目录在 Python 模块搜索路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from models import DistributionQueue
from database import Base, engine, SessionLocal

def create_tables():
    """创建分发队列数据表"""
    
    try:
        # 创建表
        logging.info("正在创建分发队列数据表...")
        Base.metadata.create_all(bind=engine, tables=[DistributionQueue.__table__])
        logging.info("分发队列数据表创建成功！")
        
        # 检查表是否创建成功
        meta = MetaData()
        meta.reflect(bind=engine)
        
        if DistributionQueue.__tablename__ in meta.tables:
            logging.info(f"确认表 {DistributionQueue.__tablename__} 已成功创建。")
        else:
            logging.error(f"表 {DistributionQueue.__tablename__} 创建失败！")
            
    except Exception as e:
        logging.error(f"创建表时出错: {e}")
        raise

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logging.info("开始创建分发队列数据表...")
    create_tables()
    logging.info("数据表创建过程完成。") 