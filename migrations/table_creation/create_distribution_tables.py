from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv
import uuid
from datetime import datetime

# 加载环境变量
load_dotenv()

# 数据库连接配置
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT")
DB_NAME = os.getenv("DB_NAME")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")

# 创建数据库连接
DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(DATABASE_URL)

def create_tables():
    """创建分发规则相关的数据表"""
    with engine.connect() as connection:
        # 创建分发规则表
        connection.execute(text("""
        CREATE TABLE IF NOT EXISTS distribution_rules (
            ID VARCHAR(36) PRIMARY KEY,
            rule_date DATE NOT NULL,
            channel_type VARCHAR(20) NOT NULL,
            group_name VARCHAR(100) NOT NULL,
            leader VARCHAR(100) NOT NULL,
            member VARCHAR(100) NOT NULL,
            staff_count INT NOT NULL DEFAULT 0,
            shift VARCHAR(20) NOT NULL,
            expected_reception INT NOT NULL DEFAULT 0,
            expected_free_reception INT NOT NULL DEFAULT 0,
            expected_paid_reception INT NOT NULL DEFAULT 0,
            actual_reception INT NULL,
            actual_free_reception INT NULL,
            actual_paid_reception INT NULL,
            store VARCHAR(200) NOT NULL,
            paid_value FLOAT NULL,
            time_range_start TIME NOT NULL,
            time_range_end TIME NOT NULL,
            remarks VARCHAR(500) NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_rule_date (rule_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """))
        
        # 创建渠道接待数表
        connection.execute(text("""
        CREATE TABLE IF NOT EXISTS channel_receptions (
            ID VARCHAR(36) PRIMARY KEY,
            rule_date DATE NOT NULL,
            channel_type VARCHAR(20) NOT NULL,
            expected_reception INT NOT NULL DEFAULT 0,
            expected_free_reception INT NOT NULL DEFAULT 0,
            expected_paid_reception INT NOT NULL DEFAULT 0,
            actual_reception INT NULL,
            actual_free_reception INT NULL,
            actual_paid_reception INT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_rule_date (rule_date),
            UNIQUE KEY uk_date_channel (rule_date, channel_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """))
        
        print("分发规则相关的数据表创建成功！")

if __name__ == "__main__":
    create_tables() 