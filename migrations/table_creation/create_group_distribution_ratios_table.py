from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv
from datetime import datetime

# 加载环境变量
load_dotenv()

# 数据库连接配置
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT")
DB_NAME = os.getenv("DB_NAME")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")

# 创建数据库连接
DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(DATABASE_URL)

def create_table():
    """创建分组分配数量表"""
    with engine.connect() as connection:
        # 创建分组分配数量表
        connection.execute(text("""
        CREATE TABLE IF NOT EXISTS group_distribution_ratios (
            id INT AUTO_INCREMENT PRIMARY KEY,
            rule_date DATE NOT NULL,
            channel_type VARCHAR(20) NOT NULL,
            group_name VARCHAR(50) NOT NULL,
            distribution_ratio INT NOT NULL DEFAULT 0,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY uix_group_distribution_ratio (rule_date, channel_type, group_name),
            INDEX idx_rule_date (rule_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """))
        
        print("分组分配数量表创建成功！")

if __name__ == "__main__":
    create_table() 