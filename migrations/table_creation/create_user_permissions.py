from database import SessionLocal, engine
from sqlalchemy import text

def create_user_permissions():
    sql = """
    CREATE TABLE user_permissions (
        id VARCHAR(36) PRIMARY KEY, 
        account VARCHAR(80) NOT NULL, 
        name TEXT NOT NULL, 
        user_review INT NOT NULL DEFAULT 0, 
        user_review_range INT NOT NULL DEFAULT 0, 
        user_management INT NOT NULL DEFAULT 0, 
        user_management_range INT NOT NULL DEFAULT 0, 
        permission_management INT NOT NULL DEFAULT 0, 
        form_preset INT NOT NULL DEFAULT 0, 
        schedule_management INT NOT NULL DEFAULT 0, 
        distribution_rules INT NOT NULL DEFAULT 0, 
        `distribution-plan` INT NOT NULL DEFAULT 0, 
        FOREIGN KEY (id) REFERENCES users(ID)
    );
    """
    
    try:
        with engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()
            print("用户权限表创建成功")
            
            # 初始化现有用户的权限记录
            insert_sql = """
            INSERT INTO user_permissions (id, account, name)
            SELECT ID, account, name FROM users;
            """
            conn.execute(text(insert_sql))
            conn.commit()
            print("现有用户权限记录初始化成功")
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    create_user_permissions()