from database import SessionLocal, engine
from models import ChannelReception, Base
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_channel_receptions_table():
    """创建channel_receptions表"""
    try:
        # 创建表
        ChannelReception.__table__.create(engine, checkfirst=True)
        logger.info("channel_receptions表创建成功")
        return True
    except Exception as e:
        logger.error(f"创建channel_receptions表失败: {e}")
        return False

if __name__ == "__main__":
    create_channel_receptions_table() 