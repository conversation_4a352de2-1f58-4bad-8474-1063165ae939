/* 排班管理页面样式 */
.schedule-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
}

.page-title {
    font-size: 22px;
    font-weight: bold;
    color: #303133;
}

.control-panel {
    margin-bottom: 20px;
}

/* 排班表格样式 */
.schedule-calendar {
    margin-bottom: 20px;
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
}

.schedule-table th, .schedule-table td {
    border: 1px solid #ebeef5;
    padding: 12px;
    text-align: center;
}

.schedule-table th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: bold;
}

.schedule-table tr:hover {
    background-color: #f5f7fa;
}

.schedule-item {
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 4px;
    background-color: #ecf5ff;
    border-left: 3px solid #409eff;
    color: #303133;
    font-size: 14px;
}

.schedule-item.morning {
    background-color: #f0f9eb;
    border-left: 3px solid #67c23a;
}

.schedule-item.afternoon {
    background-color: #fdf6ec;
    border-left: 3px solid #e6a23c;
}

.schedule-item.evening {
    background-color: #fef0f0;
    border-left: 3px solid #f56c6c;
}

.schedule-item.fullday {
    background-color: #ecf5ff;
    border-left: 3px solid #409eff;
}

.form-section {
    margin-bottom: 20px;
}

.form-section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #303133;
}

.form-row {
    margin-bottom: 15px;
}

.form-actions {
    margin-top: 20px;
    text-align: right;
}

.stats-card {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.stats-title {
    font-size: 16px;
    color: #606266;
    margin-bottom: 10px;
}

.stats-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .page-title {
        margin-bottom: 10px;
    }
} 