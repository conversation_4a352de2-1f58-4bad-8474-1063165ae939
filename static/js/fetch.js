// 保存原生的 fetch 方法
const originalFetch = window.fetch;

// 覆盖 window.fetch
window.fetch = async function (resource, config) {
  // 1. 合并请求配置
  config = config || {}; // 确保 config 不为空
  config.headers = config.headers || {}; // 初始化 headers

  // 2. 读取本地存储
  const key = localStorage.getItem('secure_key');
  
  // 3. 注入 key 头
  if (key) {
    config.headers.key = key;
  }

  // 4. 调用原生 fetch 发送修改后的请求
  return originalFetch(resource, config);
};