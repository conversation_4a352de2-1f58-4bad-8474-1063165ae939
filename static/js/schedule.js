// 排班管理页面脚本
const { createApp, ref, reactive, onMounted, computed, watch, nextTick } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

// 为主播分组创建渐变色的函数
const generateGroupColor = (groupName) => {
    // 提取分组中的字母部分
    const groupKey = groupName.replace(/主播(.*?)组/, '$1');
    
    // 预定义颜色映射 (已有的分组颜色)
    const predefinedColors = {
        'A': { start: '#4bc6ff', end: '#5d92b1' },
        'A1': { start: '#8c3ff8', end: '#cca0f9' },
        'A2': { start: '#5c6ef4', end: '#97a1ce' },
        'B': { start: '#f87b98', end: '#a97381' },
        'C': { start: '#28f7dc', end: '#4981b5' },
        'D': { start: '#f89c3a', end: '#ac8349' }
    };
    
    // 如果是已知分组，返回预定义颜色
    if (predefinedColors[groupKey]) {
        return predefinedColors[groupKey];
    }
    
    // 额外的鲜明渐变色，保持与早班和晚班不冲突
    const additionalColors = [
        { start: '#6edea5', end: '#45a97d' },  // 青绿色
        { start: '#c191ff', end: '#8769a5' },  // 淡紫色
        { start: '#47c1ff', end: '#2f7da5' },  // 蓝色
        { start: '#ffcd4a', end: '#d99c28' },  // 鲜黄色
        { start: '#66d8c0', end: '#41867a' },  // 青色
        { start: '#ff87c7', end: '#c76399' },  // 粉色
        { start: '#6ac9ff', end: '#4281a6' },  // 淡蓝色
        { start: '#d971ff', end: '#a04fc0' },  // 紫色
        { start: '#ff9b5e', end: '#c26e3c' },  // 橙色
        { start: '#70e0a3', end: '#4d996e' },  // 嫩绿色
        { start: '#aab9ff', end: '#6870c1' },  // 淡蓝紫色
        { start: '#fcb671', end: '#c08253' }   // 浅橙色
    ];
    
    // 根据分组名计算一个唯一索引，确保同一分组总是得到相同的颜色
    let hash = 0;
    for (let i = 0; i < groupKey.length; i++) {
        hash = ((hash << 5) - hash) + groupKey.charCodeAt(i);
        hash = hash & hash; // 转为32位整数
    }
    
    // 使用hash值选择一个额外颜色
    const colorIndex = Math.abs(hash) % additionalColors.length;
    return additionalColors[colorIndex];
};

// 动态生成CSS样式
const generateGroupColorStyles = (groupsInfo) => {
    // 创建一个Set存储已有的分组名称
    const groupSet = new Set();
    
    // 从主播分组信息中提取分组名称
    Object.values(groupsInfo).forEach(info => {
        if (info && info.group) {
            groupSet.add(info.group);
        }
    });
    
    // 生成CSS样式
    let styles = '';
    groupSet.forEach(groupName => {
        // 提取分组的关键部分作为CSS类名
        const groupKey = groupName.replace(/主播(.*?)组/, '$1');
        const color = generateGroupColor(groupName);
        
        // 使用简化的类名，确保与HTML中的类名匹配
        styles += `.anchor-name-group-${groupKey} {
            background: linear-gradient(135deg, ${color.start}, ${color.end}) !important;
        }\n`;
    });
    
    console.log('生成的样式:', styles);
    return styles;
};

// 应用动态样式到文档
const applyGroupColorStyles = (groupsInfo, isTableView = false) => {
    // 如果没有分组信息或为空对象，不应用样式
    if (!groupsInfo || Object.keys(groupsInfo).length === 0) {
        console.log('没有分组信息，不应用样式');
        return;
    }
    
    // 生成样式
    const styles = generateGroupColorStyles(groupsInfo);
    
    if (isTableView) {
        // 为表格视图创建单独的样式元素
        let tableStyleElement = document.getElementById('table-group-styles');
        
        if (!tableStyleElement) {
            tableStyleElement = document.createElement('style');
            tableStyleElement.id = 'table-group-styles';
            document.head.appendChild(tableStyleElement);
        }
        
        // 只修改表格视图的样式，不影响主视图
        tableStyleElement.textContent = styles;
        console.log('应用表格视图颜色样式成功');
    } else {
        // 检查是否已经存在主视图样式表
        let mainStyleElement = document.getElementById('main-group-styles');
        
        // 如果不存在则创建一个新的样式元素
        if (!mainStyleElement) {
            mainStyleElement = document.createElement('style');
            mainStyleElement.id = 'main-group-styles';
            document.head.appendChild(mainStyleElement);
        }
        
        // 应用到主视图
        mainStyleElement.textContent = styles;
        console.log('应用主视图颜色样式成功');
    }
    
    // 确保DOM更新以显示新样式
    setTimeout(() => {
        console.log('强制重新应用样式');
        // 触发一个微小的DOM变化以强制浏览器重新应用样式
        document.body.style.zoom = "0.99999";
        setTimeout(() => {
            document.body.style.zoom = "1";
        }, 10);
    }, 100);
};

const app = createApp({
    setup() {
        // 数据状态
        const loading = ref(false);
        const currentDate = ref(new Date());
        const schedules = ref([]);
        const channels = ref([]);
        const stores = ref([]);
        const anchors = ref([]);
        const rooms = ref([]); // 添加直播间列表
        const selectedAnchor = ref(null);
        const selectedStore = ref(null);
        const anchorGroups = ref({}); // 添加主播分组信息数据结构
        
        // 搜索相关
        const tableSearchKeyword = ref('');
        const filteredScheduleTableData = ref([]);
        const originalScheduleTableData = ref([]);
        
        // 店铺选择对话框相关
        const storeDetails = ref([]);
        const storeSearchKeyword = ref('');
        const currentEditingAnchorIndex = ref(-1);
        const currentEditingSubRowIndex = ref(-1);
        
        // 过滤后的店铺列表
        const filteredStores = computed(() => {
            const keyword = storeSearchKeyword.value.toLowerCase();
            
            // 检查当前是否正在编辑，且班次是否为"全天"
            const currentAnchorIndex = currentEditingAnchorIndex.value;
            const currentSubRowIndex = currentEditingSubRowIndex.value;
            let isFullDay = false;
            let currentShift = null;
            
            if (currentAnchorIndex >= 0 && currentSubRowIndex >= 0) {
                const subRow = scheduleForm.anchorGroups[currentAnchorIndex].subRows[currentSubRowIndex];
                if (subRow && subRow.shift) {
                    currentShift = subRow.shift;
                    isFullDay = currentShift === 'fullday';
                }
            }
            
            // 获取初始过滤结果
            let filtered = [...storeDetails.value];
            
            // 应用关键词过滤
            if (keyword) {
                filtered = filtered.filter(store => 
                    store.name.toLowerCase().includes(keyword) || 
                    (store.manager && store.manager.toLowerCase().includes(keyword))
                );
            }
            
            // 根据班次类型筛选店铺
            if (currentShift) {
                if (isFullDay) {
                    // 全天班次只显示渠道缩写包含"F"的店铺
                    filtered = filtered.filter(store => 
                        (store.douyin_abbr && store.douyin_abbr.includes('F')) || 
                        (store.video_abbr && store.video_abbr.includes('F')) || 
                        (store.xiaohongshu_abbr && store.xiaohongshu_abbr.includes('F')) || 
                        (store.kuaishou_abbr && store.kuaishou_abbr.includes('F'))
                    );
                } else {
                    // 非全天班次(早班和晚班)只显示不包含"F"的店铺
                    filtered = filtered.filter(store => 
                        (!store.douyin_abbr || !store.douyin_abbr.includes('F')) && 
                        (!store.video_abbr || !store.video_abbr.includes('F')) && 
                        (!store.xiaohongshu_abbr || !store.xiaohongshu_abbr.includes('F')) && 
                        (!store.kuaishou_abbr || !store.kuaishou_abbr.includes('F'))
                    );
                }
            }
            
            return filtered;
        });
        
        // 表单数据
        const scheduleForm = reactive({
            date: '',
            anchorGroups: [
                {
                    anchor_id: null,
                    subRows: [
                        {
                            store_id: null,
                            channel_id: null,
                            shift: null,
                            start_time: '',
                            end_time: '',
                            duration: 0,
                            rest_duration: 1.5, // 添加休息时长字段，默认1.5小时
                            room: null, // 添加直播间字段
                            expected_count: 0, // 添加预计字段
                            real_count: 0, // 添加实际字段
                            live_count: 0, // 添加直播字段
                            notes: ''
                        }
                    ]
                }
            ]
        });
        
        // 对话框控制
        const dialogVisible = reactive({
            schedule: false,
            store: false,
            copySchedule: false
        });
        
        // 复制排班相关
        const sourceDateForCopy = ref('');
        const targetDatesForCopy = ref([]);
        
        // 计算属性
        const formattedDate = computed(() => {
            const year = currentDate.value.getFullYear();
            const month = currentDate.value.getMonth() + 1;
            const day = currentDate.value.getDate();
            return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        });
        
        const weekDays = computed(() => {
            const days = [];
            const date = new Date(currentDate.value);
            date.setDate(date.getDate() - date.getDay()); // 设置为本周的周日
            
            for (let i = 0; i < 7; i++) {
                const day = new Date(date);
                day.setDate(date.getDate() + i);
                days.push({
                    date: day,
                    formatted: formatDate(day),
                    isToday: isSameDay(day, new Date())
                });
            }
            
            return days;
        });
        
        const schedulesByDay = computed(() => {
            const result = {};
            
            weekDays.value.forEach(day => {
                result[day.formatted] = schedules.value.filter(
                    schedule => schedule.date === day.formatted
                );
            });
            
            return result;
        });
        
        // 按主播分组排班数据
        const getGroupedSchedules = (date) => {
            console.log(`获取日期 ${date} 的分组排班`);
            const daySchedules = schedules.value.filter(s => s.date === date);
            if (!daySchedules.length) return {};
            
            // 按主播分组
            const groupedSchedules = {};
            
            daySchedules.forEach(schedule => {
                const anchorName = getAnchorName(schedule.anchor_id);
                console.log(`处理主播 ${anchorName} 的排班`);
                
                // 构建包含分组信息的显示名称
                let displayName = anchorName;
                
                // 添加分组信息到排班对象
                if (anchorGroups.value && anchorGroups.value[anchorName]) {
                    const groupInfo = anchorGroups.value[anchorName];
                    console.log(`找到主播 ${anchorName} 的分组信息:`, groupInfo);
                    const groupName = groupInfo.group || '';
                    
                    // 设置排班对象的分组信息
                    schedule.groupInfo = {
                        ...groupInfo,
                        // 确保group属性存在
                        group: groupName
                    };
                    
                    if (groupName) {
                        displayName = `[${groupName}] ${anchorName}`;
                        console.log(`生成带分组的显示名称: ${displayName}`);
                    }
                } else {
                    console.log(`主播 ${anchorName} 没有找到分组信息`);
                }
                
                if (!groupedSchedules[displayName]) {
                    groupedSchedules[displayName] = [];
                }
                
                groupedSchedules[displayName].push(schedule);
            });
            
            console.log(`日期 ${date} 的分组结果:`, Object.keys(groupedSchedules));
            return groupedSchedules;
        };
        
        // 方法
        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        };
        
        const isSameDay = (date1, date2) => {
            return date1.getFullYear() === date2.getFullYear() &&
                   date1.getMonth() === date2.getMonth() &&
                   date1.getDate() === date2.getDate();
        };
        
        const formatTime = (timeStr) => {
            return timeStr.substring(0, 5); // 只显示小时和分钟
        };
        
        // 计算时长
        const calculateDuration = (anchorIndex, subIndex) => {
            const subRow = scheduleForm.anchorGroups[anchorIndex].subRows[subIndex];
            if (!subRow.start_time || !subRow.end_time) return;
            
            const start = new Date(`2000-01-01T${subRow.start_time}`);
            const end = new Date(`2000-01-01T${subRow.end_time}`);
            
            // 处理跨天情况：如果结束时间小于开始时间，认为是跨天排班，结束时间加一天
            // 例如：开始时间20:00，结束时间03:00，表示从当天20:00到次日03:00
            if (end < start) {
                const endNextDay = new Date(`2000-01-02T${subRow.end_time}`);
                const diffMinutes = (endNextDay - start) / (1000 * 60); // 转换为分钟
                
                // 减去休息时长（将小时转换为分钟）
                const restMinutes = subRow.rest_duration ? subRow.rest_duration * 60 : 0;
                const actualMinutes = diffMinutes - restMinutes;
                
                subRow.duration = (actualMinutes / 60).toFixed(1); // 转换为小时，保留一位小数
                return;
            }
            
            const diffMinutes = (end - start) / (1000 * 60); // 转换为分钟
            
            // 减去休息时长（将小时转换为分钟）
            const restMinutes = subRow.rest_duration ? subRow.rest_duration * 60 : 0;
            const actualMinutes = diffMinutes - restMinutes;
            
            subRow.duration = (actualMinutes / 60).toFixed(1); // 转换为小时，保留一位小数
        };
        
        // 根据开始时间自动填充班次
        const autoFillShift = (anchorIndex, subIndex) => {
            const subRow = scheduleForm.anchorGroups[anchorIndex].subRows[subIndex];
            if (!subRow.start_time) return;
            
            // 解析时间
            const timeStr = subRow.start_time;
            const [hours, minutes] = timeStr.split(':').map(Number);
            
            // 计算总分钟数
            const totalMinutes = hours * 60 + minutes;
            
            // 判断班次
            // 16:00-次日凌晨4:59 (960-1739分钟) 为晚班
            // 5:00-15:59 (300-959分钟) 为早班
            if ((totalMinutes >= 960 && totalMinutes <= 1439) || (totalMinutes >= 0 && totalMinutes <= 299)) {
                subRow.shift = 'evening'; // 晚班
            } else if (totalMinutes >= 300 && totalMinutes <= 959) {
                subRow.shift = 'morning'; // 早班
            }
        };
        
        // 根据班次自动设置时间
        const autoSetTimeByShift = (anchorIndex, subIndex) => {
            const subRow = scheduleForm.anchorGroups[anchorIndex].subRows[subIndex];
            if (!subRow.shift) return;
            
            if (subRow.shift === 'morning') {
                // 早班: 7:00 - 15:30
                subRow.start_time = '07:00:00';
                subRow.end_time = '15:30:00';
            } else if (subRow.shift === 'evening') {
                // 晚班: 16:00 - 00:30
                subRow.start_time = '16:00:00';
                subRow.end_time = '00:30:00';
            } else if (subRow.shift === 'fullday') {
                // 全天: 00:00 - 23:59
                subRow.start_time = '00:00:00';
                subRow.end_time = '23:59:00';
                // 全天班次时，将直播间设置为"-"
                subRow.room = '-';
                // 全天班次时，将休息时长设置为0
                subRow.rest_duration = 0;
            }
            
            // 计算时长
            calculateDuration(anchorIndex, subIndex);
        };
        
        // 验证数值为非负整数
        const validateNumber = (value, anchorIndex, subIndex, field) => {
            const subRow = scheduleForm.anchorGroups[anchorIndex].subRows[subIndex];
            
            // 如果输入为空或非数字，设置为0
            if (value === '' || isNaN(value)) {
                subRow[field] = 0;
                return;
            }
            
            // 将输入值转换为整数
            let intValue = parseInt(value);
            
            // 如果转换结果为NaN或负数，设置为0
            if (isNaN(intValue) || intValue < 0) {
                subRow[field] = 0;
                return;
            }
            
            // 更新为有效的非负整数
            subRow[field] = intValue;
        };
        
        // 返回首页
        const goToHome = () => {
            window.location.href = '/home';
        };
        
        // 日期导航
        const previousWeek = () => {
            const date = new Date(currentDate.value);
            date.setDate(date.getDate() - 7);
            currentDate.value = date;
        };
        
        const nextWeek = () => {
            const date = new Date(currentDate.value);
            date.setDate(date.getDate() + 7);
            currentDate.value = date;
        };
        
        const goToToday = () => {
            currentDate.value = new Date();
        };
        
        // 数据获取方法
        const fetchSchedules = async () => {
            loading.value = true;
            try {
                const startDate = weekDays.value[0].formatted;
                const endDate = weekDays.value[6].formatted;
                
                const response = await fetch(`/api/schedule/schedules?start_date=${startDate}&end_date=${endDate}`);
                if (!response.ok) throw new Error('获取排班数据失败');
                
                // 获取排班数据但先不赋值
                const data = await response.json();
                
                // 获取本周所有排班中的主播名称
                const anchorNames = [...new Set(data.map(schedule => getAnchorName(schedule.anchor_id)))];
                if (anchorNames.length > 0) {
                    // 先获取主播分组信息
                    await fetchAnchorGroups(anchorNames.join(','));
                }
                
                // 在获取完主播分组信息后再设置排班数据
                schedules.value = data;
                
                // 确保重新分组
                weekDays.value.forEach(day => {
                    getGroupedSchedules(day.formatted);
                });
            } catch (error) {
                console.error('获取排班数据出错:', error);
                ElMessage.error('获取排班数据失败');
            } finally {
                loading.value = false;
            }
        };
        
        const fetchChannels = async () => {
            try {
                const response = await fetch('/api/schedule/channels');
                if (!response.ok) throw new Error('获取渠道数据失败');
                
                const data = await response.json();
                channels.value = data;
            } catch (error) {
                console.error('获取渠道数据出错:', error);
            }
        };
        
        const fetchStores = async (channelId = null) => {
            try {
                const url = channelId ? `/api/schedule/stores?channel_id=${channelId}` : '/api/schedule/stores';
                const response = await fetch(url);
                if (!response.ok) throw new Error('获取店铺数据失败');
                
                const data = await response.json();
                stores.value = data;
            } catch (error) {
                console.error('获取店铺数据出错:', error);
            }
        };
        
        const fetchAnchors = async (status = null) => {
            try {
                // 首先尝试从预设值API获取主播列表
                const presetResponse = await fetch('/api/presets');
                if (presetResponse.ok) {
                    const presetData = await presetResponse.json();
                    if (presetData && presetData.hosts && Array.isArray(presetData.hosts) && presetData.hosts.length > 0) {
                        console.log('从预设值获取主播列表成功:', presetData.hosts);
                        // 将预设值转换为主播对象数组
                        anchors.value = presetData.hosts.map(name => ({
                            id: name,
                            name: name
                        }));
                        
                        // 获取这些主播的分组信息
                        if (presetData.hosts.length > 0) {
                            await fetchAnchorGroups(presetData.hosts.join(','));
                        }
                        
                        // 同时获取直播间列表
                        if (presetData && presetData.rooms && Array.isArray(presetData.rooms)) {
                            console.log('从预设值获取直播间列表成功:', presetData.rooms);
                            rooms.value = presetData.rooms;
                        }
                        
                        return;
                    }
                }
                
                // 如果预设值获取失败，则回退到原有的API
                const url = status ? `/api/schedule/anchors?status=${status}` : '/api/schedule/anchors';
                const response = await fetch(url);
                if (!response.ok) throw new Error('获取主播数据失败');
                
                const data = await response.json();
                anchors.value = data;
                
                // 获取这些主播的分组信息
                if (data.length > 0) {
                    const anchorNames = data.map(anchor => anchor.name).join(',');
                    await fetchAnchorGroups(anchorNames);
                }
                
                return true; // 添加返回值以便Promise链式调用
            } catch (error) {
                console.error('获取主播数据出错:', error);
                return false; // 添加返回值以便Promise链式调用
            }
        };
        
        // 获取主播分组信息
        const fetchAnchorGroups = async (anchorNames) => {
            try {
                if (!anchorNames) return;
                
                console.log('开始获取主播分组信息，主播列表:', anchorNames);
                const response = await fetch(`/api/schedule/anchors/groups?anchor_names=${encodeURIComponent(anchorNames)}`);
                if (!response.ok) throw new Error('获取主播分组信息失败');
                
                const data = await response.json();
                console.log('获取主播分组信息成功:', data);
                
                // 更新主播分组信息
                anchorGroups.value = data;
                
                // 动态生成并应用颜色样式
                applyGroupColorStyles(data, false); // 确保这是主视图样式
                
                console.log('主播分组信息已更新');
            } catch (error) {
                console.error('获取主播分组信息出错:', error);
            }
        };
        
        // 全局变量，用于跟踪已删除的排班ID
        let deletedScheduleIds = [];
        
        // 表单操作
        const openScheduleDialog = (date, scheduleData = null) => {
            // 重置已删除排班ID列表
            deletedScheduleIds = [];
            
            // 清空临时选择的店铺
            tempSelectedStores.morning = {};
            tempSelectedStores.evening = {};
            
            // 重置表单
            scheduleForm.date = date;
            scheduleForm.anchorGroups = [
                {
                    anchor_id: null,
                    subRows: [
                        {
                            store_id: null,
                            channel_id: null,
                            shift: null,
                            start_time: '',
                            end_time: '',
                            duration: 0,
                            rest_duration: 1.5, // 添加休息时长字段，默认1.5小时
                            room: null, // 添加直播间字段
                            expected_count: 0, // 添加预计字段
                            real_count: 0, // 添加实际字段
                            live_count: 0, // 添加直播字段
                            notes: ''
                        }
                    ]
                }
            ];
            
            // 如果是编辑现有排班
            if (scheduleData) {
                // 获取当前日期该主播的所有排班
                const anchorName = getAnchorName(scheduleData.anchor_id);
                const anchorSchedules = schedulesByDay.value[date].filter(s => 
                    getAnchorName(s.anchor_id) === anchorName
                );
                
                // 设置表单日期
                scheduleForm.date = date;
                
                // 创建主播组
                const anchorGroup = {
                    anchor_id: scheduleData.anchor_id,
                    subRows: []
                };
                
                // 添加该主播的所有排班记录
                anchorSchedules.forEach(s => {
                    anchorGroup.subRows.push({
                        id: s.id, // 保存原始排班ID，用于标识编辑操作
                        store_id: s.store_id,
                        channel_id: s.channel_id,
                        shift: s.shift || 'morning',
                        start_time: s.start_time,
                        end_time: s.end_time,
                        duration: s.duration,
                        rest_duration: s.rest_duration || 1.5, // 添加休息时长，如果没有默认为1.5
                        room: s.room,
                        expected_count: s.expected_count,
                        real_count: s.real_count,
                        live_count: s.live_count,
                        notes: s.notes
                    });
                });
                
                // 如果没有排班记录，添加一个空行
                if (anchorGroup.subRows.length === 0) {
                    anchorGroup.subRows.push({
                        id: null,
                        store_id: null,
                        channel_id: null,
                        shift: null,
                        start_time: '',
                        end_time: '',
                        duration: 0,
                        rest_duration: 1.5, // 添加休息时长默认值
                        room: null,
                        expected_count: 0,
                        real_count: 0,
                        live_count: 0,
                        notes: ''
                    });
                }
                
                // 设置表单数据
                scheduleForm.anchorGroups = [anchorGroup];
            }
            
            // 检查当前日期班次冲突
            checkShiftConflicts(date, null);
            
            dialogVisible.schedule = true;
        };
        
        // 添加子行
        const addSubRow = (anchorIndex) => {
            // 获取第一行数据作为模板
            const firstRow = scheduleForm.anchorGroups[anchorIndex].subRows[0];
            
            // 复制第一行数据（除主播字段外）到新行
            scheduleForm.anchorGroups[anchorIndex].subRows.push({
                id: null, // 新行没有ID
                store_id: firstRow.store_id,
                channel_id: firstRow.channel_id,
                shift: firstRow.shift,
                start_time: firstRow.start_time,
                end_time: firstRow.end_time,
                duration: firstRow.duration,
                rest_duration: firstRow.rest_duration || 1.5, // 添加休息时长，如果没有默认为1.5
                room: firstRow.room,
                expected_count: firstRow.expected_count,
                real_count: firstRow.real_count,
                live_count: firstRow.live_count,
                notes: firstRow.notes
            });
            
            console.log(`添加了新行，复制自第一行数据，当前子行数量: ${scheduleForm.anchorGroups[anchorIndex].subRows.length}`);
        };
        
        // 删除子行
        const deleteSubRow = (anchorIndex, subIndex) => {
            console.log(`删除主播组 ${anchorIndex} 的子行，子行索引: ${subIndex}`);
            console.log(`当前主播组子行数量: ${scheduleForm.anchorGroups[anchorIndex].subRows.length}`);
            console.log(`要删除的子行数据:`, scheduleForm.anchorGroups[anchorIndex].subRows[subIndex]);
            
            const subRows = scheduleForm.anchorGroups[anchorIndex].subRows;
            if (subRows.length > 1) {
                // 如果要删除的行有ID（已存在的排班），记录下来以便后续删除
                const rowToDelete = subRows[subIndex];
                if (rowToDelete.id) {
                    console.log(`记录要删除的排班ID: ${rowToDelete.id}`);
                    deletedScheduleIds.push(rowToDelete.id);
                }
                
                // 确保删除的是正确的索引
                subRows.splice(subIndex, 1);
                
                // 记录删除操作，防止保存时重新添加
                console.log(`已删除主播组 ${anchorIndex} 的子行 ${subIndex}，当前剩余子行数量: ${subRows.length}`);
            } else {
                ElMessage.warning('每个主播至少需要一条排班记录');
            }
        };
        
        // 添加检查重复排班的函数
        const checkDuplicateSchedules = (schedulesToSave) => {
            // 创建映射表，用于快速检查重复
            const scheduleMap = {};
            const duplicates = [];
            
            // 遍历所有待保存的排班
            for (const schedule of schedulesToSave) {
                // 创建唯一键：日期_班次_渠道
                const key = `${schedule.date}_${schedule.shift}_${schedule.channel_id}`;
                
                // 检查是否已经存在相同键
                if (scheduleMap[key]) {
                    // 找到重复项
                    const existing = scheduleMap[key];
                    
                    // 获取班次的显示名称
                    let shiftDisplay = '未知班次';
                    if (schedule.shift === 'morning') {
                        shiftDisplay = '早班';
                    } else if (schedule.shift === 'evening') {
                        shiftDisplay = '晚班';
                    } else if (schedule.shift === 'fullday') {
                        shiftDisplay = '全天';
                    }
                    
                    duplicates.push({
                        date: schedule.date,
                        shift: shiftDisplay,
                        channel: schedule.channel_id,
                        anchor1: getAnchorName(existing.anchor_id),
                        anchor2: getAnchorName(schedule.anchor_id)
                    });
                } else {
                    // 记录该排班
                    scheduleMap[key] = schedule;
                }
            }
            
            return duplicates;
        };
        
        // 保存排班
        const saveSchedule = async () => {
            try {
                loading.value = true;
                
                // 验证表单
                for (const anchorGroup of scheduleForm.anchorGroups) {
                    if (!anchorGroup.anchor_id) {
                        throw new Error('请选择主播');
                    }
                    
                    for (const subRow of anchorGroup.subRows) {
                        if (!subRow.store_id) throw new Error('请选择店铺');
                        if (!subRow.channel_id) throw new Error('请选择渠道');
                        if (!subRow.shift) throw new Error('请选择班次');
                        if (!subRow.start_time) throw new Error('请选择开始时间');
                        if (!subRow.end_time) throw new Error('请选择结束时间');
                        // 当班次不是全天时才验证直播间
                        if (subRow.shift !== 'fullday' && !subRow.room) throw new Error('请选择直播间');
                    }
                }
                
                // 获取当前编辑的主播ID列表
                const anchorIds = scheduleForm.anchorGroups.map(group => group.anchor_id).filter(id => id);
                
                // 获取当前日期
                const currentDate = scheduleForm.date;
                
                // 构建保存数据
                const schedulesToSave = [];
                
                // 记录表单中的排班数据
                console.log('准备保存的排班数据:');
                for (const [anchorIndex, anchorGroup] of scheduleForm.anchorGroups.entries()) {
                    if (!anchorGroup.anchor_id) continue;
                    
                    console.log(`主播组 ${anchorIndex}: 主播=${anchorGroup.anchor_id}`);
                    
                    for (const [subIndex, subRow] of anchorGroup.subRows.entries()) {
                        if (!subRow.store_id || !subRow.channel_id || !subRow.shift || 
                            !subRow.start_time || !subRow.end_time) {
                            console.warn(`跳过不完整的排班记录: 主播组 ${anchorIndex}, 子行 ${subIndex}`, subRow);
                            continue; // 跳过不完整的记录
                        }
                        
                        console.log(`添加排班: 主播组 ${anchorIndex}, 子行 ${subIndex}, 店铺: ${subRow.store_id}, 渠道: ${subRow.channel_id}`);
                        
                        // 确保全天班次的休息时长为0
                        const restDuration = subRow.shift === 'fullday' ? 0 : (subRow.rest_duration || 1.5);
                        
                        schedulesToSave.push({
                            id: subRow.id || null, // 添加ID字段，用于标识编辑操作
                            date: scheduleForm.date,
                            anchor_id: anchorGroup.anchor_id,
                            store_id: subRow.store_id,
                            channel_id: subRow.channel_id,
                            shift: subRow.shift,
                            start_time: subRow.start_time,
                            end_time: subRow.end_time,
                            duration: subRow.duration,
                            rest_duration: restDuration, // 使用根据班次决定的休息时长
                            room: subRow.shift === 'fullday' ? '-' : (subRow.room || '-'), // 全天班次使用默认值
                            expected_count: subRow.expected_count || 0,
                            real_count: subRow.real_count || 0,
                            live_count: subRow.live_count || 0,
                            notes: subRow.notes
                        });
                    }
                }
                
                console.log(`准备保存 ${schedulesToSave.length} 条排班记录`);
                
                // 检查是否有重复的排班（相同日期、班次、渠道）
                const duplicates = checkDuplicateSchedules(schedulesToSave);
                if (duplicates.length > 0) {
                    let errorMsg = '检测到重复排班，无法保存：\n';
                    duplicates.forEach((dup, index) => {
                        errorMsg += `${index + 1}. 日期: ${dup.date}, 班次: ${dup.shift}, 渠道: ${dup.channel}\n`;
                        errorMsg += `   主播冲突: ${dup.anchor1} 与 ${dup.anchor2}\n`;
                    });
                    throw new Error(errorMsg);
                }
                
                // 处理已删除的排班
                if (deletedScheduleIds.length > 0) {
                    console.log(`需要删除 ${deletedScheduleIds.length} 条排班记录: ${deletedScheduleIds.join(', ')}`);
                    
                    // 逐个删除排班记录
                    for (const scheduleId of deletedScheduleIds) {
                        try {
                            const deleteResponse = await fetch(`/api/schedule/schedules/${scheduleId}`, {
                                method: 'DELETE',
                                headers: {
                                    'Content-Type': 'application/json'
                                }
                            });
                            
                            if (!deleteResponse.ok) {
                                console.error(`删除排班ID ${scheduleId} 失败:`, await deleteResponse.text());
                            } else {
                                console.log(`成功删除排班ID ${scheduleId}`);
                            }
                        } catch (deleteError) {
                            console.error(`删除排班ID ${scheduleId} 出错:`, deleteError);
                        }
                    }
                    
                    // 清空已删除ID列表
                    deletedScheduleIds = [];
                }
                
                // 如果没有排班记录要保存，直接返回成功
                if (schedulesToSave.length === 0) {
                    ElMessage.success('保存成功');
                    dialogVisible.schedule = false;
                    
                    // 清空临时选择的店铺
                    tempSelectedStores.morning = {};
                    tempSelectedStores.evening = {};
                    
                    // 刷新数据
                    await fetchSchedules();
                    // 刷新统计数据
                    await fetchAnchorStats();
                    return;
                }
                
                // 发送保存请求
                const response = await fetch('/api/schedule/schedules', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(schedulesToSave)
                });
                
                if (!response.ok) {
                    // 尝试解析错误信息
                    try {
                        const errorData = await response.json();
                        throw new Error(errorData.detail || '监测到排班数据冲突，请重新调整排班信息!');
                    } catch (e) {
                        throw new Error('监测到排班数据冲突，请重新调整排班信息!');
                    }
                }
                
                ElMessage.success('保存成功');
                dialogVisible.schedule = false;
                
                // 清空临时选择的店铺
                tempSelectedStores.morning = {};
                tempSelectedStores.evening = {};
                
                // 刷新数据
                await fetchSchedules();
                
                // 如果排班表格视图是打开的，刷新表格数据
                if (scheduleTableVisible.value) {
                    await loadScheduleTableData(scheduleTableDate.value, scheduleTableViewType.value);
                    ElMessage.success('排班表格数据已更新');
                }
                
            } catch (error) {
                console.error('保存出错:', error);
                // 使用 ElMessageBox 而不是 ElMessage，以便显示多行错误信息
                if (error.message && error.message.includes('检测到重复排班')) {
                    ElMessageBox.alert(error.message, '重复排班错误', {
                        confirmButtonText: '确定',
                        type: 'error'
                    });
                } else {
                    ElMessage.error(error.message || '保存失败');
                }
            } finally {
                loading.value = false;
            }
        };
        
        // 辅助方法
        const getStoreName = (id) => {
            if (!id) return '未知店铺';
            // 尝试从stores数组中查找
            const store = stores.value.find(s => String(s.id) === String(id));
            if (store) return store.name;
            
            // 如果在stores中找不到，尝试从storeDetails中查找
            const storeDetail = storeDetails.value.find(s => 
                s.name === id || 
                (s.id && String(s.id) === String(id))
            );
            
            // 如果在storeDetails中找到，返回店铺名称
            if (storeDetail) return storeDetail.name;
            
            // 如果是直接的店铺名称，则直接返回
            if (typeof id === 'string' && id.length > 0) return id;
            
            // 都找不到则返回未知店铺
            return '未知店铺';
        };
        
        // 获取主播名称
        const getAnchorName = (id) => {
            if (!id) return '';
            const anchor = anchors.value.find(a => a.id === id);
            return anchor ? anchor.name : id;
        };
        
        // 从后端获取店铺详情数据
        const fetchStoreDetails = async () => {
            try {
                console.log('开始获取店铺详情数据...');
                
                // 原来的代码:
                //const response = await fetch('http://127.0.0.1:8000/api/presets/shops/detail');

                // 使前后端域名相同
                const response = await fetch(`/api/presets/shops/detail`);

                console.log('店铺详情API响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('获取到的店铺详情数据:', data);
                    storeDetails.value = data;
                    return true;
                } else {
                    console.error('获取店铺详情失败:', response.status);
                    // 如果API调用失败，使用默认数据
                    storeDetails.value = getDefaultStoreDetails();
                    return false;
                }
            } catch (error) {
                console.error('获取店铺详情出错:', error);
                // 如果发生错误，使用默认数据
                storeDetails.value = getDefaultStoreDetails();
                return false;
            }
        };
        
        // 获取默认的店铺详情数据
        const getDefaultStoreDetails = () => {
            return [
                {
                    name: "小甜建房设计",
                    manager: "李泽浩",
                    douyin_abbr: "抖-小甜",
                    video_abbr: "视-小甜",
                    xiaohongshu_abbr: "红-小甜",
                    kuaishou_abbr: "快-小甜"
                },
                {
                    name: "自建房秉祥设计",
                    manager: "李泽浩",
                    douyin_abbr: "抖-秉祥",
                    video_abbr: "视-秉祥",
                    xiaohongshu_abbr: "红-秉祥",
                    kuaishou_abbr: "快-秉祥"
                },
                {
                    name: "万家乡墅设计",
                    manager: "李泽浩",
                    douyin_abbr: "抖-万家",
                    video_abbr: "视-万家",
                    xiaohongshu_abbr: "红-万家",
                    kuaishou_abbr: "快-万家"
                }
            ];
        };
        
        // 店铺选择对话框
        const openStoreDialog = (anchorIndex, subRowIndex) => {
            // 检查是否已选择班次
            const subRow = scheduleForm.anchorGroups[anchorIndex].subRows[subRowIndex];
            if (!subRow.shift) {
                ElMessage.warning('请先选择班次');
                return;
            }
            
            console.log('打开店铺选择对话框', anchorIndex, subRowIndex);
            currentEditingAnchorIndex.value = anchorIndex;
            currentEditingSubRowIndex.value = subRowIndex;
            
            // 检查当前日期班次冲突
            checkShiftConflicts(scheduleForm.date, subRow.shift);
            
            // 输出调试信息
            console.log(`当前班次: ${subRow.shift}`);
            console.log(`早班已占用店铺:`, usedStoresInCurrentDay.morning);
            console.log(`晚班已占用店铺:`, usedStoresInCurrentDay.evening);
            
            // 打开对话框
            dialogVisible.store = true;
            storeSearchKeyword.value = '';
            
            // 每次打开对话框时都重新获取数据，确保数据同步
            fetchStoreDetails().then(success => {
                if (success) {
                    ElMessage.success('店铺数据已更新');
                } else {
                    ElMessage.warning('使用本地店铺数据');
                }
            });
        };
        
        // 选择店铺
        const selectStore = (store) => {
            const currentAnchorIndex = currentEditingAnchorIndex.value;
            const currentSubRowIndex = currentEditingSubRowIndex.value;
            
            if (currentAnchorIndex >= 0 && currentSubRowIndex >= 0) {
                const subRow = scheduleForm.anchorGroups[currentAnchorIndex].subRows[currentSubRowIndex];
                const shift = subRow.shift;
                
                // 如果是全天班次，不允许直接选择店铺
                if (shift === 'fullday') {
                    ElMessage.error({
                        message: `全天班次只能选择包含"F"的渠道缩写，请点击带有"F"的渠道缩写`,
                        duration: 5000,
                        showClose: true
                    });
                    return;
                }
                
                // 检查当前日期是否已有相同店铺的排班
                const existingSchedules = schedules.value.filter(s => 
                    s.date === scheduleForm.date && 
                    s.store_id === store.name && 
                    s.shift === shift
                );
                
                if (existingSchedules.length > 0) {
                    // 获取已排班的主播名称
                    const existingAnchorName = getAnchorName(existingSchedules[0].anchor_id);
                    
                    // 显示详细错误信息
                    ElMessage.error({
                        message: `此店铺在当日${shift === 'morning' ? '早班' : (shift === 'evening' ? '晚班' : '全天')}已被主播 ${existingAnchorName} 排班`,
                        duration: 5000,
                        showClose: true
                    });
                    return;
                }
                
                // 检查是否已被占用
                if (isAnyChannelOccupied(store)) {
                    // 被占用的店铺不能选择
                    ElMessage.error({
                        message: `此店铺有渠道在当日${shift === 'morning' ? '早班' : (shift === 'evening' ? '晚班' : '全天')}已被排班，请选择其他店铺`,
                        duration: 5000,
                        showClose: true
                    });
                    return;
                }
                
                // 设置店铺和渠道
                subRow.store_id = store.name;
                subRow.channel_id = store.name;
                
                // 添加到临时选择列表
                if (shift === 'morning') {
                    tempSelectedStores.morning[store.name] = store.name;
                } else if (shift === 'evening') {
                    tempSelectedStores.evening[store.name] = store.name;
                }
                
                // 更新已占用店铺列表
                checkShiftConflicts(scheduleForm.date, shift);
            }
            
            dialogVisible.store = false;
            currentEditingAnchorIndex.value = -1;
            currentEditingSubRowIndex.value = -1;
        };
        
        // 选择店铺缩写
        const selectStoreAbbr = (abbr, storeName) => {
            const currentAnchorIndex = currentEditingAnchorIndex.value;
            const currentSubRowIndex = currentEditingSubRowIndex.value;
            
            if (currentAnchorIndex >= 0 && currentSubRowIndex >= 0) {
                const subRow = scheduleForm.anchorGroups[currentAnchorIndex].subRows[currentSubRowIndex];
                const shift = subRow.shift;
                
                // 如果是全天班次，检查渠道缩写是否包含"F"
                if (shift === 'fullday' && !abbr.includes('F')) {
                    ElMessage.error({
                        message: `全天班次只能选择包含"F"的渠道`,
                        duration: 5000,
                        showClose: true
                    });
                    return;
                }
                
                // 如果是早班或晚班，检查渠道缩写是否不包含"F"
                if ((shift === 'morning' || shift === 'evening') && abbr.includes('F')) {
                    ElMessage.error({
                        message: `早班/晚班只能选择不包含"F"的渠道`,
                        duration: 5000,
                        showClose: true
                    });
                    return;
                }
                
                // 检查当前日期是否已有相同渠道的排班
                const existingSchedules = schedules.value.filter(s => 
                    s.date === scheduleForm.date && 
                    s.channel_id === abbr && 
                    s.shift === shift
                );
                
                if (existingSchedules.length > 0) {
                    // 获取已排班的主播名称
                    const existingAnchorName = getAnchorName(existingSchedules[0].anchor_id);
                    
                    // 显示详细错误信息
                    ElMessage.error({
                        message: `此渠道在当日${shift === 'morning' ? '早班' : (shift === 'evening' ? '晚班' : '全天')}已被主播 ${existingAnchorName} 排班`,
                        duration: 5000,
                        showClose: true
                    });
                    return;
                }
                
                // 检查是否已被占用
                if (isChannelOccupied(abbr)) {
                    // 被占用的渠道不能选择
                    ElMessage.error({
                        message: `此渠道在当日${shift === 'morning' ? '早班' : (shift === 'evening' ? '晚班' : '全天')}已被排班，请选择其他渠道`,
                        duration: 5000,
                        showClose: true
                    });
                    return;
                }
                
                // 设置渠道ID
                subRow.channel_id = abbr;
                // 填充店铺名称到store_id字段
                subRow.store_id = storeName;
                
                // 添加到临时选择列表
                if (shift === 'morning') {
                    tempSelectedStores.morning[storeName] = abbr;
                } else if (shift === 'evening') {
                    tempSelectedStores.evening[storeName] = abbr;
                } else if (shift === 'fullday') {
                    // 全天班次同时添加到早班和晚班的临时列表
                    tempSelectedStores.morning[storeName] = abbr;
                    tempSelectedStores.evening[storeName] = abbr;
                }
                
                // 更新已占用店铺列表
                checkShiftConflicts(scheduleForm.date, shift);
            }
            
            dialogVisible.store = false;
            currentEditingAnchorIndex.value = -1;
            currentEditingSubRowIndex.value = -1;
        };
        
        // 确认店铺选择
        const confirmStoreSelection = () => {
            dialogVisible.store = false;
            currentEditingAnchorIndex.value = -1;
            currentEditingSubRowIndex.value = -1;
        };
        
        // 删除排班
        const deleteSchedule = async (schedule) => {
            try {
                if (!schedule || !schedule.id) {
                    ElMessage.error('无法删除排班：未找到排班ID');
                    return;
                }
                
                // 确认删除
                await ElMessageBox.confirm(
                    `确定要删除 ${getAnchorName(schedule.anchor_id)} 在 ${schedule.date} 的排班吗？`,
                    '删除确认',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                
                loading.value = true;
                
                // 发送删除请求
                const response = await fetch(`/api/schedule/schedules/${schedule.id}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) throw new Error('删除失败');
                
                ElMessage.success('删除成功');
                
                // 刷新数据
                await fetchSchedules();
                
                // 如果当前在排班表格视图，重新加载表格数据
                if (scheduleTableVisible.value) {
                    await loadScheduleTableData(scheduleTableDate.value, scheduleTableViewType.value);
                }
            } catch (error) {
                if (error.toString().includes('cancel')) {
                    // 用户取消删除，不做处理
                    return;
                }
                console.error('删除出错:', error);
                ElMessage.error(error.message || '删除失败');
            } finally {
                loading.value = false;
            }
        };
        
        // 删除主播所有排班
        const deleteAnchorSchedules = async (schedules) => {
            if (!schedules || schedules.length === 0) return;
            
            try {
                if (confirm(`确定要删除这些排班吗？此操作不可撤销。`)) {
                    loading.value = true;
                    
                    // 收集所有需要删除的排班ID
                    const scheduleIds = schedules.map(s => s.id);
                    
                    // 逐个删除排班
                    for (const id of scheduleIds) {
                        await fetch(`/api/schedule/schedules/${id}`, {
                            method: 'DELETE'
                        });
                    }
                    
                    // 重新加载排班数据
                    await fetchSchedules();
                    
                    // 提示删除成功
                    ElMessage.success(`成功删除${scheduleIds.length}条排班记录`);
                }
            } catch (error) {
                console.error('删除排班失败:', error);
                ElMessage.error('删除排班失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };
        
        // 打开复制排班对话框
        const openCopyScheduleDialog = (date) => {
            sourceDateForCopy.value = date;
            targetDatesForCopy.value = [];
            dialogVisible.copySchedule = true;
        };
        
        // 复制排班到目标日期
        const copySchedulesToDates = async () => {
            try {
                if (targetDatesForCopy.value.length === 0) {
                    ElMessage.warning('请选择至少一个目标日期');
                    return;
                }
                
                loading.value = true;
                
                // 获取源日期的排班
                const sourceSchedules = schedules.value.filter(s => s.date === sourceDateForCopy.value);
                
                if (sourceSchedules.length === 0) {
                    ElMessage.warning('源日期没有排班数据可复制');
                    loading.value = false;
                    return;
                }
                
                // 确认复制
                await ElMessageBox.confirm(
                    `确定要将 ${sourceDateForCopy.value} 的排班复制到 ${targetDatesForCopy.value.join(', ')} 吗？`,
                    '复制确认',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                
                // 处理每个目标日期
                for (const targetDate of targetDatesForCopy.value) {
                    // 跳过源日期
                    if (targetDate === sourceDateForCopy.value) continue;
                    
                    // 获取目标日期的现有排班
                    const existingSchedules = schedules.value.filter(s => s.date === targetDate);
                    
                    // 如果目标日期有排班，先删除
                    if (existingSchedules.length > 0) {
                        console.log(`删除目标日期 ${targetDate} 的 ${existingSchedules.length} 条现有排班`);
                        
                        const deletePromises = existingSchedules.map(schedule => 
                            fetch(`/api/schedule/schedules/${schedule.id}`, {
                                method: 'DELETE'
                            })
                        );
                        
                        await Promise.all(deletePromises);
                    }
                    
                    // 创建新的排班数据
                    const newSchedules = sourceSchedules.map(schedule => ({
                        date: targetDate,
                        anchor_id: schedule.anchor_id,
                        store_id: schedule.store_id,
                        channel_id: schedule.channel_id,
                        shift: schedule.shift,
                        start_time: schedule.start_time,
                        end_time: schedule.end_time,
                        duration: schedule.duration,
                        rest_duration: schedule.rest_duration || 1.5, // 添加休息时长，如果没有默认为1.5
                        room: schedule.room,
                        expected_count: schedule.expected_count,
                        real_count: schedule.real_count,
                        live_count: schedule.live_count,
                        notes: schedule.notes
                    }));
                    
                    // 保存新排班
                    const response = await fetch('/api/schedule/schedules', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(newSchedules)
                    });
                    
                    if (!response.ok) {
                        throw new Error(`复制到 ${targetDate} 失败`);
                    }
                }
                
                ElMessage.success('排班复制成功');
                dialogVisible.copySchedule = false;
                
                // 刷新数据
                await fetchSchedules();
                
            } catch (error) {
                if (error.toString().includes('cancel')) {
                    // 用户取消操作，不做处理
                    return;
                }
                console.error('复制排班出错:', error);
                ElMessage.error(error.message || '复制排班失败');
            } finally {
                loading.value = false;
            }
        };
        
        // 确认删除当天所有排班
        const confirmDeleteDaySchedules = async (date) => {
            try {
                // 获取当天的所有排班
                const daySchedules = schedules.value.filter(s => s.date === date);
                
                if (daySchedules.length === 0) {
                    ElMessage.warning('当天没有排班数据可删除');
                    return;
                }
                
                // 确认删除
                await ElMessageBox.confirm(
                    `确定要删除 ${date} 的所有排班吗？此操作不可恢复！`,
                    '删除确认',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                
                loading.value = true;
                
                // 逐个删除排班
                const deletePromises = daySchedules.map(schedule => 
                    fetch(`/api/schedule/schedules/${schedule.id}`, {
                        method: 'DELETE'
                    })
                );
                
                const results = await Promise.all(deletePromises);
                
                // 检查是否所有删除都成功
                const allSuccess = results.every(response => response.ok);
                
                if (allSuccess) {
                    ElMessage.success(`成功删除 ${date} 的 ${daySchedules.length} 条排班`);
                } else {
                    ElMessage.warning('部分排班删除失败');
                }
                
                // 刷新数据
                await fetchSchedules();
                
            } catch (error) {
                if (error.toString().includes('cancel')) {
                    // 用户取消删除，不做处理
                    return;
                }
                console.error('删除排班出错:', error);
                ElMessage.error(error.message || '删除排班失败');
            } finally {
                loading.value = false;
            }
        };
        
        // 监听器
        watch(currentDate, () => {
            fetchSchedules();
        });
        
        // 初始化
        onMounted(() => {
            console.log('组件挂载，开始初始化数据');
            
            // 加载渠道、店铺和主播数据，用于下拉选择
            fetchChannels();
            console.log('开始获取店铺数据');
            fetchStores();
            console.log('开始获取主播数据');
            fetchAnchors().then(() => {
                // 确保主播分组信息加载完后再加载排班数据
                // 这样可以确保排班卡片展示时分组颜色已经准备好
                fetchSchedules();
            });
            
            // 加载店铺详情数据
            fetchStoreDetails();
            
            // 加载统计数据
            fetchAnchorStats();
        });
        
        const selectedDate = ref(formatDate(new Date()));
        const anchorStats = ref({
            total_hours: 0,
            anchor_count: 0,
            store_count: 0,
            expected_count: 0, // 添加总预计数量字段
            real_count: 0, // 添加总实际数量字段
            fullday: {         // 添加全天班次统计
                store_count: 0,
                expected_count: 0, 
                real_count: 0
            },
            morning: {
                total_hours: 0,
                anchor_count: 0,
                store_count: 0,
                expected_count: 0, // 添加早班预计数量字段
                real_count: 0 // 添加早班实际数量字段
            },
            evening: {
                total_hours: 0,
                anchor_count: 0,
                store_count: 0,
                expected_count: 0, // 添加晚班预计数量字段
                real_count: 0 // 添加晚班实际数量字段
            }
        });

        const fetchAnchorStats = async () => {
            try {
                const response = await fetch(`/api/schedule/schedules/anchor-stats?start_date=${selectedDate.value}`);
                if (!response.ok) throw new Error('获取统计数据失败');
                
                const data = await response.json();
                anchorStats.value = data;
            } catch (error) {
                console.error('获取统计数据出错:', error);
                ElMessage.error('获取统计数据失败');
            }
        };

        onMounted(() => {
            fetchAnchorStats();
        });
        
        // 日期导航
        const previousDay = () => {
            const date = new Date(selectedDate.value);
            date.setDate(date.getDate() - 1);
            selectedDate.value = formatDate(date);
            fetchAnchorStats();
        };

        const nextDay = () => {
            const date = new Date(selectedDate.value);
            date.setDate(date.getDate() + 1);
            selectedDate.value = formatDate(date);
            fetchAnchorStats();
        };
        
        // 跟踪当前日期已占用的店铺
        const usedStoresInCurrentDay = reactive({
            morning: [], // 早班已占用的店铺名称列表
            evening: []  // 晚班已占用的店铺名称列表
        });

        // 跟踪临时选择的店铺（当前表单中选择但尚未保存的店铺）
        const tempSelectedStores = reactive({
            morning: {}, // 早班临时选择的店铺 {store_id: channel_id}
            evening: {}  // 晚班临时选择的店铺 {store_id: channel_id}
        });
        
        // 检查班次是否已占用特定店铺
        const checkShiftConflicts = (date, shift) => {
            // 获取当前日期的所有排班
            const daySchedules = schedules.value.filter(schedule => schedule.date === date);
            
            // 重置已占用店铺列表
            usedStoresInCurrentDay.morning = [];
            usedStoresInCurrentDay.evening = [];
            
            // 收集所有已被占用的店铺
            daySchedules.forEach(schedule => {
                if (schedule.shift === 'morning') {
                    // 确保不重复添加
                    if (!usedStoresInCurrentDay.morning.includes(schedule.store_id)) {
                        usedStoresInCurrentDay.morning.push(schedule.store_id);
                    }
                } else if (schedule.shift === 'evening') {
                    // 确保不重复添加
                    if (!usedStoresInCurrentDay.evening.includes(schedule.store_id)) {
                        usedStoresInCurrentDay.evening.push(schedule.store_id);
                    }
                } else if (schedule.shift === 'fullday') {
                    // 全天班次同时占用早班和晚班
                    if (!usedStoresInCurrentDay.morning.includes(schedule.store_id)) {
                        usedStoresInCurrentDay.morning.push(schedule.store_id);
                    }
                    if (!usedStoresInCurrentDay.evening.includes(schedule.store_id)) {
                        usedStoresInCurrentDay.evening.push(schedule.store_id);
                    }
                }
            });
            
            // 排除当前正在编辑的排班（如果是编辑模式）
            const currentAnchorIndex = currentEditingAnchorIndex.value;
            const currentSubRowIndex = currentEditingSubRowIndex.value;
            
            if (currentAnchorIndex >= 0 && currentSubRowIndex >= 0) {
                const subRow = scheduleForm.anchorGroups[currentAnchorIndex].subRows[currentSubRowIndex];
                
                // 如果是编辑现有排班，从占用列表中移除自己的店铺
                if (subRow.id) {
                    if (subRow.shift === 'morning') {
                        usedStoresInCurrentDay.morning = usedStoresInCurrentDay.morning.filter(
                            store => store !== subRow.store_id
                        );
                    } else if (subRow.shift === 'evening') {
                        usedStoresInCurrentDay.evening = usedStoresInCurrentDay.evening.filter(
                            store => store !== subRow.store_id
                        );
                    } else if (subRow.shift === 'fullday') {
                        // 如果是全天班次，需要同时从早班和晚班列表中移除
                        usedStoresInCurrentDay.morning = usedStoresInCurrentDay.morning.filter(
                            store => store !== subRow.store_id
                        );
                        usedStoresInCurrentDay.evening = usedStoresInCurrentDay.evening.filter(
                            store => store !== subRow.store_id
                        );
                    }
                }
            }
            
            // 添加临时选择的店铺到已占用列表
            // 将当前表单中所有已选择的店铺添加到占用列表中
            if (scheduleForm.date === date) {
                scheduleForm.anchorGroups.forEach(group => {
                    group.subRows.forEach(row => {
                        if (row.store_id && row.shift) {
                            if (row.shift === 'morning' && !usedStoresInCurrentDay.morning.includes(row.store_id)) {
                                usedStoresInCurrentDay.morning.push(row.store_id);
                                tempSelectedStores.morning[row.store_id] = row.channel_id;
                            } else if (row.shift === 'evening' && !usedStoresInCurrentDay.evening.includes(row.store_id)) {
                                usedStoresInCurrentDay.evening.push(row.store_id);
                                tempSelectedStores.evening[row.store_id] = row.channel_id;
                            } else if (row.shift === 'fullday') {
                                // 全天班次同时添加到早班和晚班的占用列表
                                if (!usedStoresInCurrentDay.morning.includes(row.store_id)) {
                                    usedStoresInCurrentDay.morning.push(row.store_id);
                                    tempSelectedStores.morning[row.store_id] = row.channel_id;
                                }
                                if (!usedStoresInCurrentDay.evening.includes(row.store_id)) {
                                    usedStoresInCurrentDay.evening.push(row.store_id);
                                    tempSelectedStores.evening[row.store_id] = row.channel_id;
                                }
                            }
                        }
                    });
                });
            }
            
            console.log(`当前日期[${date}]已占用店铺 - 早班:`, usedStoresInCurrentDay.morning);
            console.log(`当前日期[${date}]已占用店铺 - 晚班:`, usedStoresInCurrentDay.evening);
            console.log(`临时选择店铺 - 早班:`, tempSelectedStores.morning);
            console.log(`临时选择店铺 - 晚班:`, tempSelectedStores.evening);
        };
        
        // 班次变更处理
        const handleShiftChange = (anchorIndex, subIndex) => {
            const subRow = scheduleForm.anchorGroups[anchorIndex].subRows[subIndex];
            const oldShift = subRow.shift; // 保存旧的班次值作为比较
            
            // 调用自动设置时间函数
            autoSetTimeByShift(anchorIndex, subIndex);
            
            // 当班次为全天时
            if (subRow.shift === 'fullday') {
                // 设置主播为"捡漏"
                scheduleForm.anchorGroups[anchorIndex].anchor_id = "捡漏";
                
                // 如果有直播间字段，清空它
                if (subRow.room) {
                    subRow.room = null;
                }
                
                // 设置休息时长为0
                subRow.rest_duration = 0;
            } 
            // 当班次从全天变更为早班或晚班时
            else if ((subRow.shift === 'morning' || subRow.shift === 'evening') && 
                     scheduleForm.anchorGroups[anchorIndex].anchor_id === "捡漏") {
                // 清空主播字段中的"捡漏"
                scheduleForm.anchorGroups[anchorIndex].anchor_id = null;
                
                // 恢复默认休息时长为1.5小时
                subRow.rest_duration = 1.5;
            }
            
            // 重新计算时长
            calculateDuration(anchorIndex, subIndex);
            
            // 检查当前日期班次冲突
            checkShiftConflicts(scheduleForm.date, scheduleForm.anchorGroups[anchorIndex].subRows[subIndex].shift);
        };
        
        // 检查店铺是否被占用
        const isStoreOccupied = (storeName) => {
            const currentShift = scheduleForm.anchorGroups[currentEditingAnchorIndex.value]?.subRows[currentEditingSubRowIndex.value]?.shift;
            
            if (!currentShift) return false;
            
            if (currentShift === 'morning') {
                return usedStoresInCurrentDay.morning.includes(storeName);
            } else if (currentShift === 'evening') {
                return usedStoresInCurrentDay.evening.includes(storeName);
            } else if (currentShift === 'fullday') {
                // 全天班次需要检查店铺是否在早班或晚班已被占用
                return usedStoresInCurrentDay.morning.includes(storeName) || 
                       usedStoresInCurrentDay.evening.includes(storeName);
            }
            
            return false;
        };
        
        // 检查特定渠道缩写是否被占用
        const isChannelOccupied = (channelAbbr) => {
            const currentShift = scheduleForm.anchorGroups[currentEditingAnchorIndex.value]?.subRows[currentEditingSubRowIndex.value]?.shift;
            const currentDate = scheduleForm.date;
            
            if (!currentShift || !currentDate) return false;
            
            // 获取已占用的渠道列表 - 只考虑当前正在编辑的日期
            let occupiedChannels = [];
            if (currentShift === 'morning') {
                occupiedChannels = schedules.value
                    .filter(s => s.date === currentDate && s.shift === 'morning')
                    .map(s => s.channel_id);
            } else if (currentShift === 'evening') {
                occupiedChannels = schedules.value
                    .filter(s => s.date === currentDate && s.shift === 'evening')
                    .map(s => s.channel_id);
            } else if (currentShift === 'fullday') {
                // 全天班次需要检查所有班次的排班
                occupiedChannels = schedules.value
                    .filter(s => s.date === currentDate)
                    .map(s => s.channel_id);
            }
            
            // 检查是否在已保存的渠道中
            if (occupiedChannels.includes(channelAbbr)) {
                return true;
            }
            
            // 检查是否在临时选择的渠道中
            let tempSelectedMap = {};
            if (currentShift === 'morning') {
                tempSelectedMap = tempSelectedStores.morning;
            } else if (currentShift === 'evening') {
                tempSelectedMap = tempSelectedStores.evening;
            } else if (currentShift === 'fullday') {
                // 全天班次需要检查早班和晚班的临时选择
                tempSelectedMap = {...tempSelectedStores.morning, ...tempSelectedStores.evening};
            }
            
            return Object.values(tempSelectedMap).includes(channelAbbr);
        };
        
        // 检查店铺的任意渠道缩写是否被占用
        const isAnyChannelOccupied = (store) => {
            return isChannelOccupied(store.douyin_abbr) || 
                   isChannelOccupied(store.video_abbr) || 
                   isChannelOccupied(store.xiaohongshu_abbr) || 
                   isChannelOccupied(store.kuaishou_abbr);
        };
        
        // 取消排班表单
        const cancelScheduleForm = () => {
            // 清空临时选择的店铺
            tempSelectedStores.morning = {};
            tempSelectedStores.evening = {};
            
            // 关闭对话框
            dialogVisible.schedule = false;
        };
        
        // 获取主播名称及其分组信息的组合显示
        const getAnchorNameWithGroup = (id) => {
            if (!id) return '';
            const anchorName = getAnchorName(id);
            
            // 如果有分组信息，添加分组前缀
            if (anchorGroups.value && anchorGroups.value[anchorName]) {
                const groupName = anchorGroups.value[anchorName].group || '';
                if (groupName) {
                    return `[${groupName}] ${anchorName}`;
                }
            }
            
            return anchorName;
        };
        
        // 添加排班表格视图控制变量
        const scheduleTableVisible = ref(false);
        const scheduleTableDate = ref(formatDate(new Date())); // 默认当天
        const scheduleTableViewType = ref('anchor'); // 默认按主播查看，可选值：'anchor'/'store'
        const scheduleTableData = ref([]);
        
        /**
         * 按主播或店铺加载排班表格数据
         * @param {string} date - 日期字符串，格式为YYYY-MM-DD
         * @param {string} viewType - 查看类型，'anchor'或'store'
         */
        const loadScheduleTableData = async (date, viewType) => {
            loading.value = true;
            try {
                // 先获取店铺信息
                await fetchStores();
                await fetchStoreDetails();
                
                // 加载指定日期的排班数据
                const response = await fetch(`/api/schedule/schedules/day/${date}`);
                if (!response.ok) throw new Error('获取排班数据失败');
                const data = await response.json();
                
                // 如果没有数据，显示提示
                if (data.length === 0) {
                    ElMessage.warning(`${date} 没有排班数据`);
                    scheduleTableData.value = [];
                    loading.value = false;
                    return;
                }
                
                // 获取主播名称列表，用于获取分组信息
                const anchorNames = [...new Set(data.map(schedule => getAnchorName(schedule.anchor_id)))];
                
                // 获取主播分组信息
                await fetchAnchorGroups(anchorNames.join(','));
                
                // 构建主播分组信息对象用于样式生成
                const groupsInfo = {};
                for (const anchorName in anchorGroups.value) {
                    if (anchorGroups.value[anchorName] && anchorGroups.value[anchorName].group) {
                        const groupName = anchorGroups.value[anchorName].group;
                        if (!groupsInfo[groupName]) {
                            groupsInfo[groupName] = {
                                name: groupName,
                                anchors: []
                            };
                        }
                        groupsInfo[groupName].anchors.push(anchorName);
                    }
                }
                
                // 应用分组颜色样式
                applyGroupColorStyles(groupsInfo, true); // 传入true表示这是表格视图
                
                // 根据查看类型进行数据处理
                if (viewType === 'anchor') {
                    // 按主播分组数据
                    const groupedByAnchor = {};
                    data.forEach(schedule => {
                        const anchorId = schedule.anchor_id;
                        const anchorName = getAnchorName(anchorId);
                        
                        if (!groupedByAnchor[anchorId]) {
                            // 获取主播分组和负责人信息
                            let groupName = '';
                            let leader = '';
                            
                            if (anchorGroups.value && anchorGroups.value[anchorName]) {
                                groupName = anchorGroups.value[anchorName].group || '';
                                leader = anchorGroups.value[anchorName].leader || '';
                            }
                            
                            groupedByAnchor[anchorId] = {
                                anchor_id: anchorId,
                                anchor_name: anchorName,
                                group: groupName,
                                leader: leader,
                                schedules: []
                            };
                        }
                        
                        groupedByAnchor[anchorId].schedules.push(schedule);
                    });
                    
                    // 转换为数组格式
                    const anchorArray = Object.values(groupedByAnchor);
                    
                    // 按分组名称排序
                    anchorArray.sort((a, b) => {
                        // 处理无分组的情况：无分组的排在最后
                        if (!a.group && b.group) return 1;
                        if (a.group && !b.group) return -1;
                        if (!a.group && !b.group) return a.anchor_name.localeCompare(b.anchor_name);
                        
                        // 提取分组名称中的字母和数字部分
                        const extractGroupKey = (group) => {
                            if (!group) return { letter: '', number: 0 };
                            
                            // 移除"主播"和"组"等词
                            const groupKey = group.replace(/主播|组/g, '').trim();
                            
                            // 提取字母部分和数字部分
                            const match = groupKey.match(/([A-Za-z]+)(\d*)/);
                            if (!match) return { letter: groupKey, number: 0 };
                            
                            const letter = match[1] || '';
                            const number = match[2] ? parseInt(match[2]) : 0;
                            
                            return { letter, number };
                        };
                        
                        const aGroup = extractGroupKey(a.group);
                        const bGroup = extractGroupKey(b.group);
                        
                        // 首先按字母部分排序
                        if (aGroup.letter < bGroup.letter) return -1;
                        if (aGroup.letter > bGroup.letter) return 1;
                        
                        // 如果字母相同，按数字部分排序
                        if (aGroup.number < bGroup.number) return -1;
                        if (aGroup.number > bGroup.number) return 1;
                        
                        // 如果分组完全相同，按主播名称排序
                        return a.anchor_name.localeCompare(b.anchor_name);
                    });
                    
                    scheduleTableData.value = anchorArray;
                    
                    // 保存原始数据用于搜索
                    originalScheduleTableData.value = JSON.parse(JSON.stringify(anchorArray));
                    
                } else if (viewType === 'store') {
                    // 按店铺分组数据
                    const groupedByStore = {};
                    data.forEach(schedule => {
                        const storeId = schedule.store_id;
                        const storeName = getStoreName(storeId);
                        
                        if (!groupedByStore[storeId]) {
                            // 获取负责人信息 - 从storeDetails中通过名称查找店铺信息
                            const storeInfo = storeDetails.value.find(s => 
                                s.name === storeId || 
                                s.name === storeName
                            );
                            const manager = storeInfo ? storeInfo.manager || '' : '';
                            
                            groupedByStore[storeId] = {
                                store_id: storeId,
                                store_name: storeName,
                                manager: manager,
                                channel_id: schedule.channel_id, // 存储渠道
                                schedules: []
                            };
                        }
                        
                        groupedByStore[storeId].schedules.push(schedule);
                    });
                    
                    // 转换为数组格式
                    const storeArray = Object.values(groupedByStore);
                    
                    // 按负责人排序，以便相同负责人的店铺排在一起
                    storeArray.sort((a, b) => {
                        // 首先按负责人排序
                        if (a.manager < b.manager) return -1;
                        if (a.manager > b.manager) return 1;
                        // 如果负责人相同，则按店铺名称排序
                        return a.store_name.localeCompare(b.store_name);
                    });
                    
                    // 计算每个店铺在表格中的负责人信息
                    let currentManager = '';
                    let managerGroupStartIndex = 0;
                    
                    storeArray.forEach((store, index) => {
                        if (index === 0 || store.manager !== currentManager) {
                            // 新的负责人分组开始
                            currentManager = store.manager;
                            managerGroupStartIndex = index;
                            // 标记该店铺为负责人分组的第一个
                            store.isFirstInManagerGroup = true;
                            
                            // 计算当前负责人的所有店铺及其排班行数总和
                            let totalRows = 0;
                            const managerStoreCount = storeArray.slice(index).findIndex(s => s.manager !== currentManager);
                            const managerStores = managerStoreCount === -1 
                                ? storeArray.slice(index) 
                                : storeArray.slice(index, index + managerStoreCount);
                            
                            // 计算所有店铺的排班行数总和
                            totalRows = managerStores.reduce((sum, s) => sum + s.schedules.length, 0);
                            store.managerRowSpan = totalRows;
                        } else {
                            // 同一负责人的后续店铺
                            store.isFirstInManagerGroup = false;
                            store.managerRowSpan = 0;
                        }
                    });
                    
                    scheduleTableData.value = storeArray;
                    
                    // 保存原始数据用于搜索
                    originalScheduleTableData.value = JSON.parse(JSON.stringify(storeArray));
                }
                
            } catch (error) {
                console.error('加载排班表格数据出错:', error);
                ElMessage.error('获取排班数据失败');
                scheduleTableData.value = [];
            } finally {
                loading.value = false;
            }
        };
        
        /**
         * 打开排班表格对话框
         */
        const openScheduleTable = () => {
            scheduleTableDate.value = formatDate(new Date()); // 默认显示当天
            scheduleTableViewType.value = 'anchor'; // 默认按主播查看
            scheduleTableVisible.value = true;
            
            // 加载数据
            loadScheduleTableData(scheduleTableDate.value, scheduleTableViewType.value);
        };
        
        /**
         * 切换排班表格视图类型
         * @param {string} viewType - 查看类型，'anchor'或'store'
         */
        const changeScheduleTableViewType = (viewType) => {
            scheduleTableViewType.value = viewType;
            // 清空搜索关键词
            tableSearchKeyword.value = '';
            loadScheduleTableData(scheduleTableDate.value, viewType);
        };
        
        /**
         * 修改排班表格日期
         * @param {string} date - 日期字符串，格式为YYYY-MM-DD
         */
        const changeScheduleTableDate = (date) => {
            console.log('日期选择变更:', date);
            if (!date) return;
            
            // 如果日期没有变化，不执行加载操作
            if (date === scheduleTableDate.value) return;
            
            // 清空搜索关键词
            tableSearchKeyword.value = '';
            
            // 更新日期值并加载数据
            scheduleTableDate.value = date;
            loadScheduleTableData(date, scheduleTableViewType.value);
            
            // 告知用户已切换日期
            ElMessage.success(`已切换到 ${date}`);
        };
        
        /**
         * 切换到前一天
         */
        const changeToYesterday = () => {
            try {
                // 获取当前日期并转为Date对象
                let currentDate;
                try {
                    currentDate = new Date(scheduleTableDate.value);
                    if (isNaN(currentDate.getTime())) {
                        // 如果日期无效，使用今天的日期
                        currentDate = new Date();
                        console.warn('当前日期无效，使用今天:', formatDate(currentDate));
                    }
                } catch (e) {
                    // 出错时使用今天的日期
                    currentDate = new Date();
                    console.warn('解析当前日期出错，使用今天:', formatDate(currentDate));
                }
                
                // 计算前一天
                currentDate.setDate(currentDate.getDate() - 1);
                const yesterdayStr = formatDate(currentDate);
                console.log('切换到上一天:', yesterdayStr);
                
                // 更新日期并加载数据
                scheduleTableDate.value = yesterdayStr;
                // 清空搜索关键词
                tableSearchKeyword.value = '';
                loadScheduleTableData(yesterdayStr, scheduleTableViewType.value);
                
                // 显示成功消息
                ElMessage.success(`已切换到 ${yesterdayStr}`);
            } catch (error) {
                console.error('切换到上一天出错:', error);
                ElMessage.error('切换日期失败');
            }
        };
        
        /**
         * 切换到后一天
         */
        const changeToTomorrow = () => {
            try {
                // 获取当前日期并转为Date对象
                let currentDate;
                try {
                    currentDate = new Date(scheduleTableDate.value);
                    if (isNaN(currentDate.getTime())) {
                        // 如果日期无效，使用今天的日期
                        currentDate = new Date();
                        console.warn('当前日期无效，使用今天:', formatDate(currentDate));
                    }
                } catch (e) {
                    // 出错时使用今天的日期
                    currentDate = new Date();
                    console.warn('解析当前日期出错，使用今天:', formatDate(currentDate));
                }
                
                // 计算后一天
                currentDate.setDate(currentDate.getDate() + 1);
                const tomorrowStr = formatDate(currentDate);
                console.log('切换到下一天:', tomorrowStr);
                
                // 更新日期并加载数据
                scheduleTableDate.value = tomorrowStr;
                // 清空搜索关键词
                tableSearchKeyword.value = '';
                loadScheduleTableData(tomorrowStr, scheduleTableViewType.value);
                
                // 显示成功消息
                ElMessage.success(`已切换到 ${tomorrowStr}`);
            } catch (error) {
                console.error('切换到下一天出错:', error);
                ElMessage.error('切换日期失败');
            }
        };
        
        /**
         * 处理表格搜索
         */
        const handleTableSearch = () => {
            const keyword = tableSearchKeyword.value.toLowerCase().trim();
            
            if (!keyword) {
                // 如果搜索关键词为空，显示全部数据
                scheduleTableData.value = originalScheduleTableData.value;
                return;
            }
            
            // 根据当前查看模式进行搜索
            if (scheduleTableViewType.value === 'anchor') {
                // 搜索按主播查看模式的数据
                const searchResult = originalScheduleTableData.value.filter(anchor => {
                    // 检查主播相关字段
                    if (
                        (anchor.anchor_name && anchor.anchor_name.toLowerCase().includes(keyword)) ||
                        (anchor.group && anchor.group.toLowerCase().includes(keyword)) ||
                        (anchor.leader && anchor.leader.toLowerCase().includes(keyword))
                    ) {
                        return true;
                    }
                    
                    // 搜索排班记录中的字段
                    return anchor.schedules.some(schedule => {
                        return (
                            // 检查各个字段是否包含关键词
                            (getStoreName(schedule.store_id) && getStoreName(schedule.store_id).toLowerCase().includes(keyword)) ||
                            (schedule.channel_id && schedule.channel_id.toLowerCase().includes(keyword)) ||
                            (schedule.shift && (schedule.shift === 'morning' ? '早班' : (schedule.shift === 'evening' ? '晚班' : '全天')).includes(keyword)) ||
                            (schedule.start_time && schedule.start_time.includes(keyword)) ||
                            (schedule.end_time && schedule.end_time.includes(keyword)) ||
                            (schedule.duration && schedule.duration.toString().includes(keyword)) ||
                            (schedule.expected_count && schedule.expected_count.toString().includes(keyword)) ||
                            (schedule.real_count && schedule.real_count.toString().includes(keyword)) ||
                            (schedule.live_count && schedule.live_count.toString().includes(keyword)) ||
                            (schedule.notes && schedule.notes.toLowerCase().includes(keyword))
                        );
                    });
                });
                
                scheduleTableData.value = searchResult;
                
            } else if (scheduleTableViewType.value === 'store') {
                // 搜索按店铺查看模式的数据
                const searchResult = originalScheduleTableData.value.filter(store => {
                    // 检查店铺相关字段
                    if (
                        (store.store_name && store.store_name.toLowerCase().includes(keyword)) ||
                        (store.manager && store.manager.toLowerCase().includes(keyword))
                    ) {
                        return true;
                    }
                    
                    // 搜索排班记录中的字段
                    return store.schedules.some(schedule => {
                        return (
                            // 检查各个字段是否包含关键词
                            (getAnchorName(schedule.anchor_id) && getAnchorName(schedule.anchor_id).toLowerCase().includes(keyword)) ||
                            (schedule.channel_id && schedule.channel_id.toLowerCase().includes(keyword)) ||
                            (schedule.shift && (schedule.shift === 'morning' ? '早班' : (schedule.shift === 'evening' ? '晚班' : '全天')).includes(keyword)) ||
                            (schedule.start_time && schedule.start_time.includes(keyword)) ||
                            (schedule.end_time && schedule.end_time.includes(keyword)) ||
                            (schedule.duration && schedule.duration.toString().includes(keyword)) ||
                            (schedule.expected_count && schedule.expected_count.toString().includes(keyword)) ||
                            (schedule.real_count && schedule.real_count.toString().includes(keyword)) ||
                            (schedule.live_count && schedule.live_count.toString().includes(keyword)) ||
                            (schedule.notes && schedule.notes.toLowerCase().includes(keyword))
                        );
                    });
                });
                
                scheduleTableData.value = searchResult;
            }
        };
        
        // 过滤器相关
        const filterDialogVisible = ref(false);
        const filters = reactive({
            anchor: {
                leader: [],
                group: [],
                anchor_name: [],
                store_name: [],
                channel_id: [],
                shift: []
            },
            store: {
                manager: [],
                store_name: [],
                anchor_name: [],
                channel_id: [],
                shift: []
            }
        });
        
        // 过滤器选项
        const leaderOptions = ref([]);
        const groupOptions = ref([]);
        const anchorOptions = ref([]);
        const storeOptions = ref([]);
        const channelOptions = ref([]);
        const managerOptions = ref([]);
        
        // 是否有激活的过滤器
        const hasActiveFilters = computed(() => {
            const currentFilters = scheduleTableViewType.value === 'anchor' ? filters.anchor : filters.store;
            return Object.values(currentFilters).some(filter => 
                Array.isArray(filter) && filter.length > 0
            );
        });
        
        /**
         * 打开过滤器对话框
         */
        const openFilterDialog = () => {
            // 准备过滤器选项
            prepareFilterOptions();
            filterDialogVisible.value = true;
        };
        
        /**
         * 准备过滤器下拉选项
         */
        const prepareFilterOptions = () => {
            // 根据当前数据准备选项
            const allData = originalScheduleTableData.value;
            
            if (scheduleTableViewType.value === 'anchor') {
                // 主播查看模式
                
                // 负责人选项
                leaderOptions.value = [...new Set(allData
                    .map(item => item.leader)
                    .filter(leader => leader)
                )];
                
                // 分组选项
                groupOptions.value = [...new Set(allData
                    .map(item => item.group)
                    .filter(group => group)
                )];
                
                // 主播选项
                anchorOptions.value = [...new Set(allData
                    .map(item => item.anchor_name)
                    .filter(name => name)
                )];
                
                // 店铺选项
                const allStores = [];
                allData.forEach(anchor => {
                    anchor.schedules.forEach(schedule => {
                        const storeName = getStoreName(schedule.store_id);
                        if (storeName) allStores.push(storeName);
                    });
                });
                storeOptions.value = [...new Set(allStores)];
                
                // 渠道选项
                const allChannels = [];
                allData.forEach(anchor => {
                    anchor.schedules.forEach(schedule => {
                        if (schedule.channel_id) allChannels.push(schedule.channel_id);
                    });
                });
                channelOptions.value = [...new Set(allChannels)];
                
            } else {
                // 店铺查看模式
                
                // 负责人选项
                managerOptions.value = [...new Set(allData
                    .map(item => item.manager)
                    .filter(manager => manager)
                )];
                
                // 店铺选项
                storeOptions.value = [...new Set(allData
                    .map(item => item.store_name)
                    .filter(name => name)
                )];
                
                // 主播选项
                const allAnchors = [];
                allData.forEach(store => {
                    store.schedules.forEach(schedule => {
                        const anchorName = getAnchorName(schedule.anchor_id);
                        if (anchorName) allAnchors.push(anchorName);
                    });
                });
                anchorOptions.value = [...new Set(allAnchors)];
                
                // 渠道选项
                const allChannels = [];
                allData.forEach(store => {
                    store.schedules.forEach(schedule => {
                        if (schedule.channel_id) allChannels.push(schedule.channel_id);
                    });
                });
                channelOptions.value = [...new Set(allChannels)];
            }
        };
        
        /**
         * 应用过滤器
         */
        const applyFilter = () => {
            applyFilters();
            filterDialogVisible.value = false;
            ElMessage.success('已应用过滤器');
        };
        
        /**
         * 取消过滤器
         */
        const cancelFilter = () => {
            filterDialogVisible.value = false;
        };
        
        /**
         * 清除所有过滤器
         */
        const clearAllFilters = () => {
            // 清空当前查看模式的过滤器
            const currentFilters = scheduleTableViewType.value === 'anchor' ? filters.anchor : filters.store;
            Object.keys(currentFilters).forEach(key => {
                currentFilters[key] = [];
            });
            
            // 重置显示数据
            scheduleTableData.value = JSON.parse(JSON.stringify(originalScheduleTableData.value));
            
            ElMessage.success('已清除所有过滤器');
        };
        
        /**
         * 应用过滤器到表格数据
         */
        const applyFilters = () => {
            if (scheduleTableViewType.value === 'anchor') {
                // 应用主播视图过滤器
                applyAnchorFilters();
            } else {
                // 应用店铺视图过滤器
                applyStoreFilters();
            }
        };
        
        /**
         * 应用主播视图过滤器
         */
        const applyAnchorFilters = () => {
            const anchorFilters = filters.anchor;
            
            // 如果所有过滤器都为空，返回原始数据
            if (Object.values(anchorFilters).every(filter => !filter || filter.length === 0)) {
                scheduleTableData.value = JSON.parse(JSON.stringify(originalScheduleTableData.value));
                return;
            }
            
            // 应用过滤器
            const filteredData = originalScheduleTableData.value.filter(anchor => {
                // 检查主播级别的过滤条件
                if (anchorFilters.leader.length > 0 && !anchorFilters.leader.includes(anchor.leader)) {
                    return false;
                }
                
                if (anchorFilters.group.length > 0 && !anchorFilters.group.includes(anchor.group)) {
                    return false;
                }
                
                if (anchorFilters.anchor_name.length > 0 && !anchorFilters.anchor_name.includes(anchor.anchor_name)) {
                    return false;
                }
                
                // 检查排班记录级别的过滤条件
                if (anchorFilters.store_name.length > 0 || 
                    anchorFilters.channel_id.length > 0 || 
                    anchorFilters.shift.length > 0) {
                    
                    // 过滤排班记录
                    const filteredSchedules = anchor.schedules.filter(schedule => {
                        const storeName = getStoreName(schedule.store_id);
                        
                        // 店铺过滤
                        if (anchorFilters.store_name.length > 0 && 
                            !anchorFilters.store_name.includes(storeName)) {
                            return false;
                        }
                        
                        // 渠道过滤
                        if (anchorFilters.channel_id.length > 0 && 
                            !anchorFilters.channel_id.includes(schedule.channel_id)) {
                            return false;
                        }
                        
                        // 班次过滤
                        if (anchorFilters.shift.length > 0 && 
                            !anchorFilters.shift.includes(schedule.shift)) {
                            return false;
                        }
                        
                        return true;
                    });
                    
                    // 如果没有符合条件的排班记录，则排除该主播
                    if (filteredSchedules.length === 0) {
                        return false;
                    }
                    
                    // 更新主播的排班记录为过滤后的记录
                    anchor.schedules = filteredSchedules;
                }
                
                return true;
            });
            
            scheduleTableData.value = filteredData;
        };
        
        /**
         * 应用店铺视图过滤器
         */
        const applyStoreFilters = () => {
            const storeFilters = filters.store;
            
            // 如果所有过滤器都为空，返回原始数据
            if (Object.values(storeFilters).every(filter => !filter || filter.length === 0)) {
                scheduleTableData.value = JSON.parse(JSON.stringify(originalScheduleTableData.value));
                return;
            }
            
            // 应用过滤器
            const filteredData = originalScheduleTableData.value.filter(store => {
                // 检查店铺级别的过滤条件
                if (storeFilters.manager.length > 0 && !storeFilters.manager.includes(store.manager)) {
                    return false;
                }
                
                if (storeFilters.store_name.length > 0 && !storeFilters.store_name.includes(store.store_name)) {
                    return false;
                }
                
                // 检查排班记录级别的过滤条件
                if (storeFilters.anchor_name.length > 0 || 
                    storeFilters.channel_id.length > 0 || 
                    storeFilters.shift.length > 0) {
                    
                    // 过滤排班记录
                    const filteredSchedules = store.schedules.filter(schedule => {
                        const anchorName = getAnchorName(schedule.anchor_id);
                        
                        // 主播过滤
                        if (storeFilters.anchor_name.length > 0 && 
                            !storeFilters.anchor_name.includes(anchorName)) {
                            return false;
                        }
                        
                        // 渠道过滤
                        if (storeFilters.channel_id.length > 0 && 
                            !storeFilters.channel_id.includes(schedule.channel_id)) {
                            return false;
                        }
                        
                        // 班次过滤
                        if (storeFilters.shift.length > 0 && 
                            !storeFilters.shift.includes(schedule.shift)) {
                            return false;
                        }
                        
                        return true;
                    });
                    
                    // 如果没有符合条件的排班记录，则排除该店铺
                    if (filteredSchedules.length === 0) {
                        return false;
                    }
                    
                    // 更新店铺的排班记录为过滤后的记录
                    store.schedules = filteredSchedules;
                }
                
                return true;
            });
            
            scheduleTableData.value = filteredData;
        };
        
        /**
         * 关闭排班表格对话框，重新应用主视图样式
         */
        const closeScheduleTable = () => {
            scheduleTableVisible.value = false;
            
            // 清空搜索框文字
            tableSearchKeyword.value = '';
            
            // 恢复原始表格数据(取消过滤效果)
            if (originalScheduleTableData.value.length > 0) {
                scheduleTableData.value = JSON.parse(JSON.stringify(originalScheduleTableData.value));
            }
            
            // 获取所有日期的主播名称，用于恢复分组颜色
            const allAnchorNames = new Set();
            
            // 遍历所有排班数据，收集主播名称
            schedules.value.forEach(schedule => {
                const anchorName = getAnchorName(schedule.anchor_id);
                if (anchorName) {
                    allAnchorNames.add(anchorName);
                }
            });
            
            // 如果有主播数据，重新获取并应用分组颜色
            if (allAnchorNames.size > 0) {
                fetchAnchorGroups([...allAnchorNames].join(','));
            }
            
            console.log('排班表格已关闭，重新应用主视图样式');
        };
        
        // 监听排班表格对话框的可见性变化
        watch(scheduleTableVisible, (newValue) => {
            if (!newValue) { // 当对话框关闭时
                closeScheduleTable();
            }
        });
        
        /**
         * 导出当前表格数据为Excel
         */
        const exportToExcel = () => {
            try {
                loading.value = true;
                
                // 创建一个工作簿
                const wb = XLSX.utils.book_new();
                
                // 准备数据
                const data = [];
                
                // 添加表头
                const headers = ['序号', '负责人', '分组', '主播', '店铺', '渠道', '班次', 
                    '开始时间', '结束时间', '时长', '预计', '实际', '直播', '备注'];
                data.push(headers);
                
                // 根据当前视图模式添加数据
                if (scheduleTableViewType.value === 'anchor') {
                    // 按主播视图导出
                    let rowIndex = 1;
                    scheduleTableData.value.forEach(anchor => {
                        anchor.schedules.forEach(schedule => {
                            const row = [
                                rowIndex++,
                                anchor.leader || '',
                                anchor.group || '',
                                anchor.anchor_name || '',
                                getStoreName(schedule.store_id) || '未知店铺',
                                schedule.channel_id || '',
                                schedule.shift === 'morning' ? '早班' : (schedule.shift === 'evening' ? '晚班' : '全天'),
                                formatTime(schedule.start_time),
                                formatTime(schedule.end_time),
                                schedule.duration || '',
                                schedule.expected_count || '',
                                schedule.real_count || '',
                                schedule.live_count || '',
                                schedule.notes || ''
                            ];
                            data.push(row);
                        });
                    });
                } else {
                    // 按店铺视图导出
                    let rowIndex = 1;
                    scheduleTableData.value.forEach(store => {
                        store.schedules.forEach(schedule => {
                            const row = [
                                rowIndex++,
                                store.manager || '',
                                '', // 分组在店铺视图中不显示
                                getAnchorName(schedule.anchor_id) || '',
                                store.store_name || '未知店铺',
                                schedule.channel_id || '',
                                schedule.shift === 'morning' ? '早班' : (schedule.shift === 'evening' ? '晚班' : '全天'),
                                formatTime(schedule.start_time),
                                formatTime(schedule.end_time),
                                schedule.duration || '',
                                schedule.expected_count || '',
                                schedule.real_count || '',
                                schedule.live_count || '',
                                schedule.notes || ''
                            ];
                            data.push(row);
                        });
                    });
                }
                
                // 创建工作表
                const ws = XLSX.utils.aoa_to_sheet(data);
                
                // 添加到工作簿
                XLSX.utils.book_append_sheet(wb, ws, '排班数据');
                
                // 生成文件名
                const fileName = `排班表_${scheduleTableDate.value}_${scheduleTableViewType.value === 'anchor' ? '按主播' : '按店铺'}.xlsx`;
                
                // 导出Excel
                XLSX.writeFile(wb, fileName);
                
                ElMessage.success('导出成功');
            } catch (error) {
                console.error('导出Excel失败:', error);
                ElMessage.error('导出Excel失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };
        
        return {
            loading,
            currentDate,
            weekDays,
            schedules,
            schedulesByDay,
            channels,
            stores,
            anchors,
            rooms,
            selectedAnchor,
            selectedStore,
            dialogVisible,
            scheduleForm,
            calculateDuration,
            autoFillShift,
            autoSetTimeByShift,
            validateNumber,
            formatTime,
            previousWeek,
            nextWeek,
            goToHome,
            goToToday,
            openScheduleDialog,
            addSubRow,
            deleteSubRow,
            saveSchedule,
            deleteSchedule,
            deleteAnchorSchedules,
            getStoreName,
            getAnchorName,
            getAnchorNameWithGroup,
            getGroupedSchedules,
            // 店铺选择对话框相关
            storeDetails,
            storeSearchKeyword,
            filteredStores,
            openStoreDialog,
            selectStore,
            selectStoreAbbr,
            confirmStoreSelection,
            // 复制排班相关
            sourceDateForCopy,
            targetDatesForCopy,
            openCopyScheduleDialog,
            copySchedulesToDates,
            confirmDeleteDaySchedules,
            selectedDate,
            anchorStats,
            fetchAnchorStats,
            previousDay,
            nextDay,
            usedStoresInCurrentDay,
            checkShiftConflicts,
            handleShiftChange,
            isStoreOccupied,
            isChannelOccupied,
            isAnyChannelOccupied,
            cancelScheduleForm,
            anchorGroups,
            scheduleTableVisible,
            scheduleTableDate,
            scheduleTableViewType,
            scheduleTableData,
            openScheduleTable,
            changeScheduleTableViewType,
            changeScheduleTableDate,
            changeToYesterday,
            changeToTomorrow,
            // 搜索相关
            tableSearchKeyword,
            filteredScheduleTableData,
            originalScheduleTableData,
            handleTableSearch,
            // 过滤器相关
            filterDialogVisible,
            filters,
            leaderOptions,
            groupOptions,
            anchorOptions,
            storeOptions,
            channelOptions,
            managerOptions,
            hasActiveFilters,
            openFilterDialog,
            prepareFilterOptions,
            applyFilter,
            cancelFilter,
            clearAllFilters,
            applyFilters,
            // 关闭排班表格
            closeScheduleTable,
            exportToExcel
        };
    }
});

// 挂载应用
app.use(ElementPlus);
app.mount('#app'); 