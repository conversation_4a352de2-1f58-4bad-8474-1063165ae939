const { ref, onMounted, onUnmounted } = Vue

export default {
    name: 'DataFormatter',
    setup() {
        // 数据监测状态
        const isMonitoring = ref(false)
        const stats = ref({
            dataCount: 0,
            lastUpdateTime: '-'
        })

        // 排版配置
        const formattingConfig = ref({
            template: '',
            prefix: '',
            suffix: '',
            lineBreak: '\n'
        })

        // 模板列表
        const templates = ref([])

        // 预览数据
        const previewData = ref([])
        const formattedPreview = ref('')

        // 定时器
        let statsTimer = null

        // API 调用函数
        const api = {
            getMonitoringStatus: () => axios.get('/api/monitoring/status'),
            updateMonitoringStatus: (isEnabled) => axios.post('/api/monitoring/status', { isEnabled }),
            getMonitoringStats: () => axios.get('/api/monitoring/stats'),
            getFormattingTemplates: () => axios.get('/api/formatting/templates'),
            saveFormattingTemplate: (template) => axios.post('/api/formatting/templates', template),
            formatData: (data, templateId) => axios.post('/api/formatting/format', { data, templateId })
        }

        // 初始化
        onMounted(async () => {
            try {
                // 获取监测状态
                const statusRes = await api.getMonitoringStatus()
                isMonitoring.value = statusRes.data.isEnabled

                // 获取模板列表
                const templatesRes = await api.getFormattingTemplates()
                templates.value = templatesRes.data

                // 启动定时器
                if (isMonitoring.value) {
                    startStatsTimer()
                }
            } catch (error) {
                ElementPlus.ElMessage.error('初始化失败')
            }
        })

        // 清理
        onUnmounted(() => {
            if (statsTimer) {
                clearInterval(statsTimer)
            }
        })

        // 监测开关变化处理
        const handleMonitoringChange = async (value) => {
            if (!value) {
                try {
                    await ElementPlus.ElMessageBox.confirm(
                        '关闭数据监测可能会导致数据丢失，是否确认关闭？',
                        '警告',
                        {
                            confirmButtonText: '确认',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    )
                } catch (e) {
                    isMonitoring.value = true
                    return
                }
            }

            try {
                await api.updateMonitoringStatus(value)
                ElementPlus.ElMessage({
                    type: value ? 'success' : 'info',
                    message: value ? '数据监测已开启' : '数据监测已关闭'
                })

                if (value) {
                    startStatsTimer()
                } else if (statsTimer) {
                    clearInterval(statsTimer)
                }
            } catch (error) {
                ElementPlus.ElMessage.error('更新监测状态失败')
                isMonitoring.value = !value
            }
        }

        // 启动统计数据定时器
        const startStatsTimer = () => {
            updateStats()
            statsTimer = setInterval(updateStats, 5000)
        }

        // 更新统计数据
        const updateStats = async () => {
            try {
                const res = await api.getMonitoringStats()
                stats.value = res.data
            } catch (error) {
                console.error('获取统计数据失败:', error)
            }
        }

        // 模板变更处理
        const handleTemplateChange = async (templateId) => {
            const template = templates.value.find(t => t.id === templateId)
            if (template) {
                formattingConfig.value = { ...formattingConfig.value, ...template.config }
                await updatePreview()
            }
        }

        // 更新预览
        const updatePreview = async () => {
            try {
                if (!previewData.value.length) {
                    previewData.value = ['示例数据1', '示例数据2', '示例数据3']
                }
                const res = await api.formatData(previewData.value, formattingConfig.value.template)
                formattedPreview.value = res.data
            } catch (error) {
                console.error('更新预览失败:', error)
            }
        }

        // 刷新预览
        const refreshPreview = async () => {
            await updatePreview()
        }

        // 保存模板
        const saveTemplate = async () => {
            try {
                const template = {
                    id: formattingConfig.value.template,
                    name: templates.value.find(t => t.id === formattingConfig.value.template)?.name || '新模板',
                    config: {
                        prefix: formattingConfig.value.prefix,
                        suffix: formattingConfig.value.suffix,
                        lineBreak: formattingConfig.value.lineBreak
                    }
                }
                await api.saveFormattingTemplate(template)
                ElementPlus.ElMessage.success('保存成功')
            } catch (error) {
                ElementPlus.ElMessage.error('保存失败')
            }
        }

        // 重置配置
        const resetConfig = () => {
            formattingConfig.value = {
                template: '',
                prefix: '',
                suffix: '',
                lineBreak: '\n'
            }
            updatePreview()
        }

        return {
            isMonitoring,
            stats,
            formattingConfig,
            templates,
            formattedPreview,
            handleMonitoringChange,
            handleTemplateChange,
            updatePreview,
            refreshPreview,
            saveTemplate,
            resetConfig
        }
    },
    template: `
        <div class="data-formatter">
            <div class="control-panel">
                <div class="monitor-switch">
                    <el-switch
                        v-model="isMonitoring"
                        active-text="数据监测开启"
                        inactive-text="数据监测关闭"
                        @change="handleMonitoringChange"
                    />
                </div>
                
                <div class="data-stats">
                    <el-card class="stats-card">
                        <template #header>
                            <div class="card-header">
                                <span>数据监测面板</span>
                                <el-tag :type="isMonitoring ? 'success' : 'info'">
                                    {{ isMonitoring ? '监测中' : '已停止' }}
                                </el-tag>
                            </div>
                        </template>
                        <div class="stats-content">
                            <div class="stat-item">
                                <span class="label">当前数据量：</span>
                                <span class="value">{{ stats.dataCount }}</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">最后更新：</span>
                                <span class="value">{{ stats.lastUpdateTime }}</span>
                            </div>
                        </div>
                    </el-card>
                </div>
            </div>

            <div class="formatting-panel">
                <el-form :model="formattingConfig" label-width="120px">
                    <el-form-item label="排版方案">
                        <el-select 
                            v-model="formattingConfig.template" 
                            placeholder="请选择排版方案"
                            @change="handleTemplateChange"
                        >
                            <el-option
                                v-for="template in templates"
                                :key="template.id"
                                :label="template.name"
                                :value="template.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="前缀">
                        <el-input 
                            v-model="formattingConfig.prefix" 
                            placeholder="请输入前缀"
                            @input="updatePreview" 
                        />
                    </el-form-item>

                    <el-form-item label="后缀">
                        <el-input 
                            v-model="formattingConfig.suffix" 
                            placeholder="请输入后缀"
                            @input="updatePreview" 
                        />
                    </el-form-item>

                    <el-form-item label="换行符">
                        <el-input 
                            v-model="formattingConfig.lineBreak" 
                            placeholder="请输入换行符"
                            @input="updatePreview" 
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="saveTemplate">保存方案</el-button>
                        <el-button @click="resetConfig">重置</el-button>
                    </el-form-item>
                </el-form>

                <div class="preview-panel">
                    <el-card class="preview-card">
                        <template #header>
                            <div class="card-header">
                                <span>预览效果</span>
                                <el-button type="text" @click="refreshPreview">刷新预览</el-button>
                            </div>
                        </template>
                        <div class="preview-content">
                            <pre>{{ formattedPreview }}</pre>
                        </div>
                    </el-card>
                </div>
            </div>
        </div>
    `
} 