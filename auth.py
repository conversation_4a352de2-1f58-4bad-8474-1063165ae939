from fastapi import Request, HTTPException
from fastapi.responses import RedirectResponse, JSONResponse
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from sqlalchemy.orm import Session
from models import User, UserPermissionBackend
from database import get_db
import os
from cryptography.fernet import Fernet
import requests

EMAIl_SECRET_KEY = os.getenv("Email_SECRET_KEY")

# 邮箱加密（可逆向）生成密钥（仅需一次）
cipher_suite = Fernet(EMAIl_SECRET_KEY)

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import base64

app = FastAPI()

# Base64 编码接口
def base64_encrypt_data(data:str)->str:
    # 将明文转换为 bytes
    data_bytes = data.encode('utf-8')
    # Base64 编码
    encoded_bytes = base64.b64encode(data_bytes)
    encoded_str = encoded_bytes.decode('utf-8')
    return encoded_str

# Base64 解码接口
def base64_decrypt_data(data:str)->str:
    try:
        # 如果数据为空或None，直接返回
        if not data:
            return data
        # Base64 解码
        decoded_bytes = base64.b64decode(data)
        decoded_str = decoded_bytes.decode('utf-8')
        return decoded_str
    except Exception as e:
        # 如果解码失败，返回原始数据（可能本身就不是加密的）
        return data

def aoksend_send_code_email(toemail: str,code:int)-> str:


    url = 'https://www.aoksend.com/index/api/send_email'
    # 设置POST请求的数据
    data = {
        'app_key': '0e8cf39be3a366ad08b58c5bca83da72',
        'to': toemail,
        'template_id': 'E_119793341512',
        'data': f'{{"code": "{code}"}}'
    }
    response = requests.post(url, data=data)
    if response.status_code == 200:
        return 1
    else:
        # 请求失败，打印错误信息
        return 0

def aoksend_send_email(toemail: str)-> str:
    url = 'https://www.aoksend.com/index/api/send_email'
    # 设置POST请求的数据
    data = {
        'app_key': '0e8cf39be3a366ad08b58c5bca83da72',
        'to': toemail,
        'template_id': 'E_119798514027',
        'data': '{}'
    }
    #return data
    # 发送POST请求
    response = requests.post(url, data=data)
    if response.status_code == 200:
        return response.text
    else:
        # 请求失败，打印错误信息
        print(f'Error: {response.status_code}, {response.text}')


    return f"email {len(toemail)} items"

def get_current_user(request: Request, db: Session) -> Optional[User]:
    # 首先尝试从Authorization header获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header.split(" ")[1]
    else:
        # 如果header中没有token，则尝试从cookie中获取
        token = request.query_params.get("token") or request.cookies.get("token")

    if not token:
        return None
        
    try:
        if token.startswith("user_"):
            user_id = token.split("_")[1]  # 移除int()转换，因为ID可能不是数字
            user = db.query(User).filter(User.ID == user_id).first()
            return user
    except Exception as e:
        print(f"获取用户出错: {e}")
        return None
    
    return None

async def auth_middleware(request: Request, call_next):
    # 不需要验证的路径列表
    public_paths = [
        "/",
        "/api/login",
        "/static",
        "/api/register",
        "/api/presets",
        "/api/presets/test",
        "/api/presets/shops/detail",
        "/api/presets/shops",
        "/home",
        "/api/leads",
        "/api/forgetpass",
        "/api/secure_key",
        "/api/secure_route",
        "/api/secure_key_yz",
        "/api/send_email",
        "/api/sala_crm",
        "/api/websocket/status",
        "/api/websocket/xiansuo",
        "/api/login_websocket",
        "/api/websocket/sala_crm_follow_tz",
        "/api/leads/export_error_data",
    ]
    
    # 检查当前路径是否需要验证
    if (request.url.path in public_paths or
        request.url.path.startswith("/static/") or
        request.url.path.startswith("/api/presets") or
        request.url.path.startswith("/api/presets/shops/") or
        request.url.path.startswith("/api/websocket/")):
        response = await call_next(request)
        return response
    
    # 获取当前用户
    db = next(get_db())
    user = get_current_user(request, db)
    
    # 如果用户未登录，重定向到登录页面
    if not user:
        if request.url.path.startswith("/api/"):
            return JSONResponse(
                status_code=401,
                content={"detail": "未登录"}
            )
        return RedirectResponse(url="/", status_code=303)
    
    # 检查用户状态
    # 未审核用户不允许访问任何系统
    if user.is_admin == 3:
        if request.url.path.startswith("/api/"):
            return JSONResponse(
                status_code=403,
                content={"detail": "账号正在审核中，请等待管理员审核"}
            )
        return RedirectResponse(url="/", status_code=303)
    
    # 普通用户只能访问前端系统
    if user.is_admin == 2:
        # 检查是否为后台管理系统的请求
        is_backend_request = not request.headers.get("referer", "").startswith("http://localhost:5173")  # 假设前端系统地址
        
        # 如果是访问后台管理系统，则拒绝访问
        if is_backend_request and not request.url.path.startswith("/api/"):
            return RedirectResponse(url="/", status_code=303)
        
        # 如果是API请求，也需要检查是否为后台管理API
        if request.url.path.startswith("/api/") and is_backend_request:
            # 允许访问部分API
            allowed_apis = [
                "/api/leads",
                "/api/presets",
                "/api/users/info",
                "/api/upload/withdraw_evidence",
                "/api/send_email",
                "/api/distribution_send_email",
                "/api/outlook_send_email",
                
                "/api/user/profile",
                "/api/user-permissions-frontend",
                "/api/permissions/frontend",
                "/api/user/permissions/recycle-bin",
                "/api/recycle-bin",
                "/api/distribution/media/auto-dispatch",
                "/api/formatting/monitor",
                "/api/user/permissions/lead-table",
                "/api/distribution/rules",
                "/api/process",
                "/api/upuseremail",
                "/api/upuserpass",
                "/api/sala_crm",
                "/api/cehui_clue_withdraw",
                "/api/get_bill_number",
                "/api/categorydata",
                "/api/category",
                "/api/tongzhi_leads_withdraw",
                "/api/schedule/schedules/get-anchor",
                "/api/user_config",
            ]
           
            is_allowed = any(request.url.path.startswith(api) for api in allowed_apis)
            if not is_allowed:
                return JSONResponse(
                    status_code=403,
                    content={"detail": "无权访问该API"}
                )
    
    # 只对管理员API进行权限检查，不再限制用户审核页面的访问
    if (request.url.path == "/api/pending-users" or 
        request.url.path.startswith("/api/approve-user/") or 
        request.url.path.startswith("/api/reject-user/")):
        
        if not user or user.is_admin > 1:  # 非管理员
            return JSONResponse(
                status_code=403,
                content={"detail": "无权限访问此功能"}
            )
    #return JSONResponse({"error": "Client disconnected"}, status_code=499)
    response = await call_next(request)
    return response

async def check_page_permission(request: Request, permission_field: str) -> Tuple[bool, Optional[User]]:
    """
    检查用户是否有权限访问特定页面
    """
    db = next(get_db())
    user = get_current_user(request, db)
    
    # 未登录用户无权访问
    if not user:
        return False, None
        
    # 超级管理员(is_admin=0)拥有所有权限
    if user.is_admin == 0:
        return True, user
    
    # 前端用户(is_admin=2)没有后端权限
    if user.is_admin == 2:
        return False, user
    
    try:
        # 根据用户的部门和身份查询权限
        from models import UserPermissionBackend
        
        # 打印查询条件，便于调试
        print(f"检查权限: 用户={user.name}, 部门={user.department}, 身份={user.identity}, 权限={permission_field}")
        
        permission = db.query(UserPermissionBackend).filter(
            UserPermissionBackend.department == user.department,
            UserPermissionBackend.identity == user.identity
        ).first()
        
        # 如果没有找到权限记录，默认无权访问
        if not permission:
            print(f"未找到部门[{user.department}]身份[{user.identity}]的权限记录")
            return False, user
        
        # 检查对应字段的权限值是否为0（0表示有权限）
        has_permission = getattr(permission, permission_field, 1) == 0
        
        # 记录权限检查结果，方便调试
        print(f"用户 {user.account} ({user.ID}) 访问 {permission_field} 页面: {'允许' if has_permission else '拒绝'}")
        
        return has_permission, user
    except Exception as e:
        print(f"权限检查出错: {str(e)}")
        return False, user

async def check_backend_permission(request: Request, permission_field: str, db: Session) -> Tuple[bool, str]:
    """
    检查用户是否有权限访问后端页面，基于部门和身份
    
    Args:
        request: HTTP请求对象
        permission_field: 权限表中对应页面的字段名，如'user_review', 'user_management'等
        db: 数据库会话
    
    Returns:
        tuple: (是否有权访问, 提示消息)
    """
    user = get_current_user(request, db)
    
    # 未登录用户无权访问
    if not user:
        return False, "用户未登录"
        
    # 超级管理员(is_admin=0)拥有所有权限
    if user.is_admin == 0:
        return True, "权限验证通过"
    
    # 前端用户(is_admin=2)没有后端权限
    if user.is_admin == 2:
        return False, "前端用户无权访问后台页面"
    
    try:
        # 根据用户的部门和身份查询权限
        from models import UserPermissionBackend
        
        # 打印查询条件，便于调试
        print(f"检查权限: 用户={user.name}, 部门={user.department}, 身份={user.identity}, 权限={permission_field}")
        
        permission = db.query(UserPermissionBackend).filter(
            UserPermissionBackend.department == user.department,
            UserPermissionBackend.identity == user.identity
        ).first()
        
        # 如果没有找到权限记录，默认无权访问
        if not permission:
            print(f"未找到部门[{user.department}]身份[{user.identity}]的权限记录")
            return False, f"暂无权限访问该页面"
        
        # 检查对应字段的权限值是否为0（0表示有权限）
        has_permission = getattr(permission, permission_field, 1) == 0
        
        page_names = {
            "user_review": "用户审核页面",
            "user_management": "用户管理页面",
            "form_preset": "表单预设页面",
            "schedule_management": "排班管理页面",
            "distribution_rules": "分发规则页面",
            "distribution_plan": "分发计划页面"
        }
        page_name = page_names.get(permission_field, "该页面")
        
        # 记录权限检查结果，方便调试
        print(f"用户 {user.account} ({user.ID}) 访问 {permission_field} 页面: {'允许' if has_permission else '拒绝'}")
        
        if has_permission:
            return True, "权限验证通过"
        else:
            return False, f"暂无权限访问{page_name}"
            
    except Exception as e:
        print(f"权限检查出错: {str(e)}")
        return False, f"权限检查发生错误: {str(e)}"