进入虚拟环境venv
cd Lead-Distribution_backend
source venv/bin/activate
python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

退出当前虚拟环境
deactivate
查看已安装包
python3 -m pip list
查看单个已安装包
python3 -m pip show fastapi

安装虚拟环境venv
python3.11 -m venv venv
安装依赖
pip install -r requirements.txt --index-url https://pypi.org/simple
pip3 install 'uvicorn[standard]'
pip3 install websockets
pip3 install websockets -i https://pypi.org/simple/
pip3 install openpyxl

线索管理系统
cd backend
python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
线索分发系统
cd frontend
npm run dev

phpmyadmin重启数据库
sudo systemctl restart mysql 

终端python建表
创建python建表文件xxxxxx.py
cd backend
python3 xxxxxx.py
python3 create_user_permissions.py

pydantic模块：数据验证

宝塔linux
tianzongheng
Song4661380@

250415


密码使用passlib库中的bcrypt算法对明文密码进行单向哈希的非对称加密，自动包含随机盐值。原始密码从不存储在数据库，只存储加密后的哈希值。需要验证密码时，对用户输入的密码进行同样加密，对比加密后的数据判断是否正确。
线索的电话号码、微信名称和微信id使用cryptography库中的对称加密算法fernet，需要使用时再解密。

线索加密后注意事项：应做好密钥的备份，防止密钥丢失导致数据无法恢复
加密和解密操作会增加一定的计算开销，对于大量数据的操作可能会有性能影响
目前的实现在to_dict方法中解密，因此每次查询数据都会进行解密操作
兼容性处理：
代码中添加了兼容性处理，如果解密失败会返回原始数据，确保系统平滑过渡
数据搜索限制：
加密后的数据无法直接通过SQL查询进行模糊搜索，如需搜索功能需要额外处理
可以考虑添加一个标记字段，标识数据是否已加密，避免重复加密
对于大量数据查询场景，可优化解密逻辑，只在必要时解密
可以实现密钥轮换机制，定期更新密钥，提高系统安全性
对于需要搜索敏感字段的场景，可以考虑添加一个搜索索引表或使用全文检索方案
以上方案在保证数据安全性的同时，尽量减小了对现有系统的影响，使敏感数据能够安全存储和使用。