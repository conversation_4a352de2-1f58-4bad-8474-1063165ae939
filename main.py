# -*- coding: utf-8 -*-
import base64
import time
import asyncio
from cryptography.fernet import Fernet
from fastapi import FastAPI, Depends, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from database import get_db, test_db_connection, start_db_keepalive, reset_db_pool, safe_db_connection, SessionLocal
from datetime import datetime
import os
from routers import leads, user_review, user_management, presets, recycle_bin, schedule,distribution, monitoring, frontend_api,sala_crm,sala_crm_openseas,leads_withdraw, category,msg_websocket,public
from passlib.context import CryptContext
from models import User
from auth import auth_middleware, get_current_user, check_page_permission, check_backend_permission    
from sqlalchemy import text
from fastapi.responses import JSONResponse
from database import SessionLocal, engine
from models import Base, User
from typing import Optional, Tuple, Dict, Any
from pydantic import BaseModel
from passlib.context import CryptContext

# 定义需要权限控制的页面及对应权限字段的映射
PAGE_PERMISSIONS = {
    "user_review": "用户审核页面",
    "user_management": "用户管理页面",
    "form_preset": "表单预设页面",
    "schedule_management": "排班管理页面",
    "distribution_rules": "分发规则页面",
    "distribution_plan": "分发计划页面"
}

Base.metadata.create_all(bind=engine)

JIEKO_SECRET_KEY = os.getenv("JIEKO_SECRET_KEY", "default_secret_key_32_characters_long")
JIEKO_API_KEY = os.getenv("JIEKO_API_KEY", "default_api_key")
# 确保密钥长度为32字节
if len(JIEKO_SECRET_KEY) < 32:
    JIEKO_SECRET_KEY = JIEKO_SECRET_KEY.ljust(32, '0')
elif len(JIEKO_SECRET_KEY) > 32:
    JIEKO_SECRET_KEY = JIEKO_SECRET_KEY[:32]

app = FastAPI()




cipher = Fernet(base64.urlsafe_b64encode(JIEKO_SECRET_KEY.encode()))

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 添加认证中间件
# app.middleware("http")(auth_middleware)

# 配置模板目录
templates = Jinja2Templates(directory="templates")

# 配置静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.middleware("http")
async def verify_encrypted_api_key(request: Request, call_next):
    no_key_paths = ["/api/secure_key", "/","/api/distribution/media/auto-dispatch","/api/upload/withdraw_evidence"]
    # 添加leads相关的API路径到免验证列表
    no_key_prefixes = ["/api/websocket/", "/api/leads/"]

    if request.url.path in no_key_paths or any(request.url.path.startswith(prefix) for prefix in no_key_prefixes):
        response = await call_next(request)
        return response

    if not request.url.path.startswith("/api/"):
        response = await call_next(request)
        return response

    jiami_key = request.headers.get("key")
    if not jiami_key:
        raise HTTPException(status_code=200, detail="抱歉，您访问的页面不存在")

    try:
        decoded_key = base64.b64decode(jiami_key).decode()
        decrypted_key = cipher.decrypt(decoded_key.encode()).decode()
        if decrypted_key != JIEKO_API_KEY:
            raise HTTPException(status_code=200, detail="Invalid API Key")
    except Exception as e:
        raise HTTPException(status_code=200, detail="Failed to decrypt or invalid key")

    response = await call_next(request)
    return response

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    # allow_origins=[
    #     "http://**************:5173", "http://127.0.0.1:5173", 
    #     "http://**************:8000", "http://127.0.0.1:8000",
    #     "http://**************:8001", "http://127.0.0.1:8001", 
    #     "http://**************:8002", "http://127.0.0.1:8002",
    #     "http://**************:3000", "http://127.0.0.1:3000",
    #     "http://0.0.0.0:5173", "http://0.0.0.0:8000",
    #     "http://0.0.0.0:8001", "http://0.0.0.0:8002",
    #     "http://0.0.0.0:3000"
    # ],  # 增加更多可能的源
    allow_credentials=True,
    #allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=86400,
    allow_origins=["*"],
    allow_methods=["*"],     # 允许的HTTP方法
)

# 注册路由
app.include_router(public.router)
app.include_router(msg_websocket.router)
app.include_router(leads.router)
app.include_router(sala_crm.router)
app.include_router(sala_crm_openseas.router)
app.include_router(leads_withdraw.router)
app.include_router(user_review.router)
app.include_router(user_management.router)
app.include_router(presets.router, tags=["presets"])
app.include_router(recycle_bin.router)
app.include_router(
    schedule.router,
    prefix="/api/schedule",
    tags=["schedule"]
)
app.include_router(distribution.router, tags=["distribution"])
app.include_router(monitoring.router, tags=["monitoring"])
app.include_router(frontend_api.router)
app.include_router(category.router, tags=["category"])

# 注册任务管理路由
from routers import rule_task
app.include_router(rule_task.router, tags=["task"])

@app.get("/api/secure_key")
async def secure_key(request: Request):
    # 获取请求来源
    company = request.headers.get("company")
    if company !="jumosheji":
        raise HTTPException(status_code=400, detail="抱歉，您访问的页面不存在")
    # 生成一个 Fernet 密钥
    #secret_key = Fernet.generate_key()
    # 加密 API Key
    encrypted_api_key = cipher.encrypt(JIEKO_API_KEY.encode()).decode()
    return {"key": encrypted_api_key}

@app.post("/api/secure_key_yz")
async def secure_key_yz(request: Request):
    jiami_key = request.headers.get("key")

    if not jiami_key:
         raise HTTPException(status_code=400, detail="抱歉，您访问的页面不存在")
    key = base64.b64decode(jiami_key).decode()

    try:
        decrypted_key = cipher.decrypt(key.encode()).decode()
        return {"decrypted_key": decrypted_key, "JIEKO_API_KEY": JIEKO_API_KEY}
    except Exception as e:
        raise HTTPException(status_code=400, detail="Failed to decrypt or invalid key")


 
    
# @app.on_event("startup")
# async def startup_event():
#     """应用启动时执行的操作"""
#     # 重置连接池，确保启动时连接池是干净的
#     reset_db_pool()
    
#     # 尝试数据库连接，如果失败则重试
#     retries = 3
#     for attempt in range(retries):
#         if test_db_connection():
#             print(f"数据库连接测试成功（尝试 {attempt + 1}/{retries}）")
#             break
#         else:
#             print(f"警告: 数据库连接测试失败（尝试 {attempt + 1}/{retries}）")
#             if attempt < retries - 1:
#                 print(f"等待 2 秒后重试...")
#                 await asyncio.sleep(2)
#                 # 再次重置连接池
#                 reset_db_pool()
    
#     # 启动数据库连接保活线程
#     start_db_keepalive()
    
#     print("应用启动完成")

# @app.on_event("shutdown")
# async def shutdown_event():
#     """应用关闭时执行的操作"""
#     # 关闭所有数据库连接
#     reset_db_pool()
#     print("应用关闭，数据库连接池已重置")

@app.get("/", response_class=HTMLResponse)
def login_page(request: Request):
    return templates.TemplateResponse(
        "login.html",
        {"request": request}
    )

@app.get("/home", response_class=HTMLResponse)
def home_page(request: Request):
    # 获取当前用户信息
    db = next(get_db())
    user = get_current_user(request, db)
    
    # 用户角色映射
    role_names = {
        0: "超级管理员",
        1: "管理员",
        2: "普通用户",
        3: "待审核用户"
    }
    
    # 如果用户已登录，传递用户信息到模板
    user_info = None
    if user:
        user_info = {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "role": role_names.get(user.is_admin, "未知角色")
        }
    
    # 检查是否有权限被拒绝的消息
    permission_denied = request.query_params.get("permission_denied") == "true"
    denied_page = request.query_params.get("page", "")
    page_name = PAGE_PERMISSIONS.get(denied_page, "该页面")
    
    context = {
        "request": request, 
        "user_info": user_info,
    }
    
    if permission_denied:
        context["permission_denied"] = True
        context["permission_message"] = f"暂无权限访问{page_name}"

    return templates.TemplateResponse("home.html", context)

# 登录接口
@app.post("/api/login")
async def login(request: Request):
    # 直接使用SessionLocal避免依赖注入问题
    db = SessionLocal()
    try:
        data = await request.json()
        
        # # 如果提供了token，直接返回用户信息
        # if "token" in data:
        #     token = data.get("token")
        #     if token and token.startswith("user_"):
        #         user_id = token.split("_")[1]
        #         user = db.query(User).filter(User.ID == user_id).first()
        #         if user:
        #             # 检查用户状态
        #             if user.is_admin == 3:  # 未审核用户
        #                 raise HTTPException(status_code=403, detail="用户数据审核中，无法登录")
                    
        #             # 检查请求来源，普通用户只能访问前端系统
        #             if user.is_admin == 2:
        #                 # 获取请求来源
        #                 referer = request.headers.get("referer", "")
        #                 # 检查是否来自前端系统
        #                 if not referer.startswith("http://localhost:5173"):  # 假设前端系统地址
        #                     raise HTTPException(status_code=403, detail="无权限，请联系系统管理员")
                    
        #             return {
        #                 "token": token,
        #                 "message": "用户信息获取成功",
        #                 "user": {
        #                     "id": user.ID,
        #                     "name": user.name,
        #                     "department": user.department,
        #                     "is_admin": user.is_admin
        #                 }
        #             }
        #     raise HTTPException(status_code=401, detail="无效的token")
        
        # 常规登录流程
        username = data.get("username")
        password = data.get("password")
        
        if not username or not password:
            raise HTTPException(status_code=400, detail="请提供用户名和密码")
            
        user = db.query(User).filter(User.account == username).first()

        if not user:
            raise HTTPException(status_code=401, detail="用户名或密码错误")
            
        # 在验证密码前，先检查用户状态
        # 这样可以确保状态检查优先于密码检查
        if user.is_admin == 3:  # 未审核用户
            raise HTTPException(status_code=403, detail="用户数据审核中，无法登录")

        # 检查请求来源，普通用户只能访问前端系统
        if user.is_admin == 2:
            # 获取请求来源
            referer = request.headers.get("referer", "")
            origin = request.headers.get("origin", "")
            host = request.headers.get("host", "")
            
            # 检查是否来自前端系统
            # is_frontend = "5173" in referer or "5173" in origin or "5173" in host
            # if not is_frontend:
            #     raise HTTPException(status_code=403, detail="无权限，请联系系统管理员")

        # 检查密码是否正确
        if not pwd_context.verify(password, user.password_hash):
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        # 登录成功，返回token
        return {
            "token": f"user_{user.ID}",
            "message": "登录成功",
            "user": {
                "id": user.ID,
                "name": user.name,
                "department": user.department,
                "is_admin": user.is_admin
            }
        }
    except HTTPException:
        # 直接重新抛出HTTP异常，保留原始状态码和详细信息
        raise
    except Exception as e:
        print(f"登录过程中发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
    finally:
        # 确保数据库连接被关闭
        db.close()

# 判断请求是否来自前端系统
# def is_from_frontend(request: Request) -> bool:
#     # 检查请求的来源
#     referer = request.headers.get("referer", "")
#     origin = request.headers.get("origin", "")
#
#     # 检查是否包含前端系统的URL特征
#     frontend_urls = ["localhost:5173", "127.0.0.1:5173"]
#     return any(url in referer or url in origin for url in frontend_urls)

@app.get("/status", response_class=HTMLResponse)
async def status_page(request: Request):
    """数据库状态页面"""
    db_status = test_db_connection()
    return templates.TemplateResponse(
        "status.html",
        {
            "request": request,
            "status": "healthy" if db_status else "unhealthy",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "db_host": os.getenv("DB_HOST"),
            "db_name": os.getenv("DB_NAME")
        }
    )

# 以下为后端访问权限路由
# 用户审核页面路由
@app.get("/user-review", response_class=HTMLResponse)
async def user_review_page(request: Request, db: Session = Depends(get_db)):
    # 检查用户是否有权限访问用户审核页面
    has_permission, message = await check_backend_permission(request, "user_review", db)
    
    # 获取当前用户信息
    user = get_current_user(request, db)
    
    # 如果用户未登录，重定向到登录页面
    if not user:
        return RedirectResponse(url="/", status_code=303)
    
    # 如果无权限，重定向到首页并带上消息参数
    if not has_permission:
        # 重定向到首页并附加权限拒绝消息
        return RedirectResponse(
            url=f"/home?permission_denied=true&page=user_review", 
            status_code=303
        )
    
    # 创建上下文
    context = {
        "request": request,
        "user_info": {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "is_admin": user.is_admin,
            "group_name": getattr(user, "group_name", None)
        }
    }
    
    # 有权限，正常渲染页面
    return templates.TemplateResponse("user_review.html", context)

# 用户管理页面路由
@app.get("/user-management", response_class=HTMLResponse)
async def user_management_page(request: Request, db: Session = Depends(get_db)):
    # 检查用户是否有权限访问用户管理页面
    has_permission, message = await check_backend_permission(request, "user_management", db)
    
    # 获取当前用户信息
    user = get_current_user(request, db)
    
    # 如果用户未登录，重定向到登录页面
    if not user:
        return RedirectResponse(url="/", status_code=303)
    
    # 如果无权限，重定向到首页并带上消息参数
    if not has_permission:
        # 重定向到首页并附加权限拒绝消息
        return RedirectResponse(
            url=f"/home?permission_denied=true&page=user_management", 
            status_code=303
        )
    
    # 创建上下文
    context = {
        "request": request,
        "user_info": {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "is_admin": user.is_admin,
            "group_name": getattr(user, "group_name", None)
        }
    }
    
    # 有权限，正常渲染页面
    return templates.TemplateResponse("user_management.html", context)

# 表单预设页面路由
@app.get("/form-preset", response_class=HTMLResponse)
async def form_preset_page(request: Request, db: Session = Depends(get_db)):
    # 检查用户是否有权限访问表单预设页面
    has_permission, message = await check_backend_permission(request, "form_preset", db)
    
    # 获取当前用户信息
    user = get_current_user(request, db)
    
    # 如果用户未登录，重定向到登录页面
    if not user:
        return RedirectResponse(url="/", status_code=303)
    
    # 如果无权限，重定向到首页并带上消息参数
    if not has_permission:
        return RedirectResponse(
            url=f"/home?permission_denied=true&page=form_preset", 
            status_code=303
        )
    
    # 创建上下文
    context = {
        "request": request,
        "user_info": {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "is_admin": user.is_admin,
            "group_name": getattr(user, "group_name", None)
        }
    }
    
    # 有权限，正常渲染页面
    return templates.TemplateResponse("form_preset.html", context)

# 排班管理页面路由
@app.get("/schedule-management", response_class=HTMLResponse)
async def schedule_management_page(request: Request, db: Session = Depends(get_db)):
    # 检查用户是否有权限访问排班管理页面
    has_permission, message = await check_backend_permission(request, "schedule_management", db)
    
    # 获取当前用户信息
    user = get_current_user(request, db)
    
    # 如果用户未登录，重定向到登录页面
    if not user:
        return RedirectResponse(url="/", status_code=303)
    
    # 如果无权限，重定向到首页并带上消息参数
    if not has_permission:
        return RedirectResponse(
            url=f"/home?permission_denied=true&page=schedule_management", 
            status_code=303
        )
    
    # 创建上下文
    context = {
        "request": request,
        "user_info": {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "is_admin": user.is_admin,
            "group_name": getattr(user, "group_name", None)
        }
    }
    
    # 有权限，正常渲染页面
    return templates.TemplateResponse("schedule_management.html", context)

# 分发规则页面路由
@app.get("/distribution-rules", response_class=HTMLResponse)
@app.get("/distribution_rules", response_class=HTMLResponse)  # 添加下划线版本的路由
async def distribution_rules_page(request: Request, db: Session = Depends(get_db)):
    # 检查用户是否有权限访问分发规则页面
    has_permission, message = await check_backend_permission(request, "distribution_rules", db)
    
    # 获取当前用户信息
    user = get_current_user(request, db)
    
    # 如果用户未登录，重定向到登录页面
    if not user:
        return RedirectResponse(url="/", status_code=303)
    
    # 如果无权限，重定向到首页并带上消息参数
    if not has_permission:
        return RedirectResponse(
            url=f"/home?permission_denied=true&page=distribution_rules", 
            status_code=303
        )
    
    # 创建上下文
    context = {
        "request": request,
        "user_info": {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "is_admin": user.is_admin,
            "group_name": getattr(user, "group_name", None)
        }
    }
    
    # 有权限，正常渲染页面
    return templates.TemplateResponse("distribution_rules.html", context)

# 分发计划页面路由
@app.get("/distribution-plan", response_class=HTMLResponse)
async def distribution_plan_page(request: Request, db: Session = Depends(get_db)):
    # 检查用户是否有权限访问分发计划页面
    has_permission, message = await check_backend_permission(request, "distribution_plan", db)
    
    # 获取当前用户信息
    user = get_current_user(request, db)
    
    # 如果用户未登录，重定向到登录页面
    if not user:
        return RedirectResponse(url="/", status_code=303)
    
    # 如果无权限，重定向到首页并带上消息参数
    if not has_permission:
        return RedirectResponse(
            url=f"/home?permission_denied=true&page=distribution_plan", 
            status_code=303
        )
    
    # 创建上下文
    context = {
        "request": request,
        "user_info": {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "is_admin": user.is_admin,
            "group_name": getattr(user, "group_name", None)
        }
    }
    
    # 有权限，正常渲染页面
    return templates.TemplateResponse("distribution_plan.html", context)

@app.get("/health")
async def health_check():
    """应用健康检查接口"""
    start_time = time.time()
    db_healthy = test_db_connection()
    response_time = time.time() - start_time
    
    status = {
        "status": "healthy" if db_healthy else "unhealthy",
        "timestamp": datetime.now().isoformat(),
        "database": {
            "connected": db_healthy,
            "response_time_ms": round(response_time * 1000, 2)
        },
        "version": os.getenv("APP_VERSION", "1.0.0")
    }
    
    if not db_healthy:
        # 尝试重置连接池
        reset_pool_result = reset_db_pool()
        status["database"]["pool_reset"] = reset_pool_result
        return JSONResponse(
            content=status,
            status_code=503  # Service Unavailable
        )
    
    return status

@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    return templates.TemplateResponse(
        "404.html",
        {"request": request},
        status_code=404
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc):
    error_details = {
        "timestamp": datetime.now().isoformat(),
        "path": request.url.path,
        "method": request.method,
        "error_type": type(exc).__name__,
        "error_message": str(exc)
    }
    
    print(f"全局异常处理: {error_details}")
    
    if isinstance(exc, HTTPException):
        if exc.status_code == 404:
            return templates.TemplateResponse(
                "404.html",
                {"request": request},
                status_code=404
            )
        return JSONResponse(
            content={"detail": exc.detail},
            status_code=exc.status_code
        )
    
    # 检查是否是数据库连接相关错误
    if "database" in str(exc).lower() or "connection" in str(exc).lower() or "sql" in str(exc).lower():
        # 尝试重置数据库连接池
        reset_db_pool()
        return JSONResponse(
            content={"detail": "数据库连接错误，请稍后重试"},
            status_code=503
        )
    
    # 其他未处理的异常
    return JSONResponse(
        content={"detail": "服务器内部错误"},
        status_code=500
    )

# 定义注册请求的数据模型
class RegisterRequest(BaseModel):
    account: str
    password: str
    contact: str
    name: str
    department: str
    group_name: Optional[str] = None  # 允许为空

# 依赖项函数，用于获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 添加辅助函数处理权限检查和上下文构建
async def check_permission_and_get_context(request: Request, permission_field: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    检查用户权限并构建页面上下文
    
    Args:
        request: HTTP请求对象
        permission_field: 权限表中对应页面的字段名
    
    Returns:
        tuple: (有权限, 上下文字典)
    """
    # 获取页面显示名称
    page_name = PAGE_PERMISSIONS.get(permission_field, "当前页面")
    
    # 检查用户是否有权限访问
    has_permission, user = await check_page_permission(request, permission_field)
    
    # 未登录用户返回None，调用者需处理重定向
    if not user:
        return False, None
    
    # 创建上下文字典
    context = {"request": request}
    
    # 如果有用户信息，添加到上下文
    if user:
        context["user_info"] = {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "is_admin": user.is_admin,
            "group_name": getattr(user, "group_name", None)
        }
    
    # 如果无权限，添加警告消息到上下文
    if not has_permission:
        context["no_permission"] = True
        context["permission_message"] = f"暂无权限访问{page_name}"
    
    return has_permission, context

# 添加通用的页面渲染函数
async def render_page_with_permission(request: Request, permission_field: str, template_name: str) -> HTMLResponse:
    """
    通用的页面渲染函数，包含权限检查
    
    Args:
        request: HTTP请求对象
        permission_field: 权限表中对应页面的字段名
        template_name: 要渲染的模板名称
    
    Returns:
        HTMLResponse: 页面响应对象
    """
    has_permission, context = await check_permission_and_get_context(request, permission_field)
    
    # 未登录用户重定向到首页
    if context is None:
        return RedirectResponse(url="/", status_code=303)
    
    # 如果无权限，重定向到home页面并带上提示消息
    if not has_permission:
        # 将权限消息存储在会话或以查询参数传递
        return RedirectResponse(
            url=f"/home?permission_denied=true&page={permission_field}",
            status_code=303
        )
    
    # 渲染页面，无论是否有权限
    return templates.TemplateResponse(template_name, context)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)

