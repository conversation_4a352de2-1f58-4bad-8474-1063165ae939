from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv
import threading
import time
import traceback
import contextlib

# 加载环境变量
load_dotenv()

# 数据库连接配置
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_USER = os.getenv("DB_USER", "root")
DB_PASSWORD = os.getenv("DB_PASSWORD", "root")
DB_NAME = os.getenv("DB_NAME", "lead_distribution")

SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_size=20,  # 增加连接池大小（原来是20）
    max_overflow=30,  # 增加最大溢出连接数（原来是10）
    pool_timeout=60,  # 增加连接超时时间（原来是10）
    pool_recycle=1800,  # 减少连接回收时间（原来是1800）
    pool_pre_ping=True,  # 连接前ping测试
    connect_args={
        "connect_timeout": 10,  # 增加连接超时时间（秒）
    }
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建Base类，用于声明模型
Base = declarative_base()

# 连接重试配置
MAX_RETRIES = 2
RETRY_DELAY = 1  # 秒

# 增加连接获取计数器
connection_attempts = 0
connection_failures = 0

# 获取数据库连接、重连、关闭等
def get_db():
    global connection_attempts, connection_failures
    db = None
    retries = 0
    
    while retries < MAX_RETRIES:
        try:
            connection_attempts += 1
            db = SessionLocal()
            # 尝试执行一个简单查询，验证连接是否有效
            db.execute(text("SELECT 1"))
            
            # 连接成功，返回数据库会话
            yield db
            break
        except Exception as e:
            # 记录错误
            connection_failures += 1
            error_msg = f"数据库会话获取失败，尝试重新连接 (尝试 {retries+1}/{MAX_RETRIES}): {str(e)}"
            print(error_msg)
            
            # 打印完整的堆栈跟踪
            traceback.print_exc()
            
            # 如果已经获取了连接，关闭它
            if db is not None:
                try:
                    db.close()
                except:
                    pass
            
            # 增加重试次数
            retries += 1
            
            # 如果还有重试机会，等待一段时间后重试
            if retries < MAX_RETRIES:
                time.sleep(RETRY_DELAY)
            else:
                # 最后一次尝试失败，抛出异常
                raise Exception(f"数据库连接失败，已重试 {MAX_RETRIES} 次: {str(e)}")
        finally:
            # 确保数据库连接被关闭
            if db is not None:
                try:
                    db.close()
                except Exception as close_error:
                    print(f"关闭数据库连接时出错: {str(close_error)}")

# 安全获取数据库会话的上下文管理器
@contextlib.contextmanager
def safe_db_connection():
    """提供数据库会话的上下文管理器，带有重试机制"""
    db = None
    retries = 0
    
    while retries < MAX_RETRIES:
        try:
            db = SessionLocal()
            # 验证连接
            db.execute(text("SELECT 1"))
            break
        except Exception as e:
            print(f"数据库连接失败 (尝试 {retries+1}/{MAX_RETRIES}): {str(e)}")
            if db is not None:
                try:
                    db.close()
                except:
                    pass
            db = None
            retries += 1
            if retries < MAX_RETRIES:
                time.sleep(RETRY_DELAY)
            else:
                raise Exception(f"无法建立数据库连接: {str(e)}")
    
    try:
        yield db
    finally:
        if db is not None:
            try:
                db.close()
            except Exception as e:
                print(f"关闭数据库连接失败: {str(e)}")

# 添加定期ping数据库的函数，可以在应用启动时调用
def keep_db_connection_alive():
    """定期ping数据库，保持连接活跃"""
    while True:
        try:
            with safe_db_connection() as db:
                if db:
                    db.execute(text("SELECT 1"))
                    print("数据库连接保活: 成功")
        except Exception as e:
            print(f"数据库连接保活失败: {str(e)}")
        
        # 打印连接统计信息
        print(f"数据库连接统计 - 尝试: {connection_attempts}, 失败: {connection_failures}")
        
        time.sleep(30)  # 每30秒ping一次

# 启动保活线程
def start_db_keepalive():
    """启动数据库连接保活线程"""
    keepalive_thread = threading.Thread(target=keep_db_connection_alive, daemon=True)
    keepalive_thread.start()
    print("数据库连接保活线程已启动")

# 测试数据库连接
def test_db_connection():
    """测试数据库连接"""
    try:
        with safe_db_connection() as db:
            if db:
                db.execute(text("SELECT 1"))
                return True
    except Exception as e:
        print(f"数据库连接测试失败: {str(e)}")
        traceback.print_exc()
    return False

# 重置数据库连接池
def reset_db_pool():
    """关闭所有连接并重置连接池"""
    try:
        engine.dispose()
        print("数据库连接池已重置")
        return True
    except Exception as e:
        print(f"重置数据库连接池失败: {str(e)}")
        return False