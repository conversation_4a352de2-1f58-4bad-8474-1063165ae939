import os
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy import and_, func, or_
from sqlalchemy.orm import Session
from database import get_db
from models import ClueSheet, User, SalaCrm, ClueWithdraw,Category, UserPermissionFrontend, DistributionQueue
from datetime import datetime
from pydantic import BaseModel
from auth import get_current_user, base64_decrypt_data
import httpx

router = APIRouter()
WEB_URL = os.getenv("WEB_URL")

# 线索提取数据请求模型
class LeadsWithdraw(BaseModel):
    page: Optional[int] = 1
    pageSize: Optional[int] = 20
    apply_time: Optional[str] = None
    keyword: Optional[str] = None
    sortField: Optional[str] = "apply_time"
    sortOrder: Optional[str] = "desc"

# 添加审核
class AddClueWithdraw(BaseModel):
    ID: Optional[str] = None
    reviewer: Optional[str] = None # 审批人
    withdraw_evidence: Optional[str] = None # 证据
    withdraw_reason: Optional[str] = None # 原因
    withdraw_status: Optional[int] = 0 # 0:待处理，1:已通过，2:已拒绝
    contact_person: Optional[str] = None # 申请人
    approval_type: Optional[int] = 0 # 0:重新分发，1:已撤回
    handle_type: Optional[int] = 0 #处理方式 2:重新分发,5标记撤回


# 审批
class UpClueWithdraw(BaseModel):
    withdraw_status: Optional[int] = 0
    receipt: Optional[str] = None
    handle_type: Optional[int] = 0 #处理方式 2:重新分发,5标记撤回


def split_string(input_str: str = ""):
    QINIU_IMG_TOU = os.getenv("QINIU_IMG_TOU")
    if not input_str:
        return []  # 输入为空时返回空字符串

    # 按逗号分割并去除空字符串
    result = [QINIU_IMG_TOU+part for part in input_str.split(",") if part]
    return result

@router.post("/api/leads_withdraw", summary="以ClueWithdraw为主表的三表联查，返回指定字段")
def post_leads_withdraw_list(data: LeadsWithdraw, request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return HTTPException(status_code=400, detail="用户未登录")
   
    try:
        skip = (data.page - 1) * data.pageSize
        
        # 构建查询 - 只选择需要的字段
        query = db.query(
            ClueWithdraw.ID,
            ClueWithdraw.approval_type,
            ClueWithdraw.contact_person,
            ClueWithdraw.withdraw_reason,
            ClueWithdraw.withdraw_evidence,
            ClueWithdraw.apply_time,
            ClueWithdraw.reviewer,
            ClueWithdraw.shenpi_time,
            ClueWithdraw.withdraw_status,
            ClueWithdraw.receipt,
            ClueWithdraw.handle_type,
            # ClueSheet 表字段
            ClueSheet.record_time,
            ClueSheet.wechat_id,
            ClueSheet.channel,
            ClueSheet.store,
            ClueSheet.remarks,
            # SalaCrm 表字段
            SalaCrm.allocation_date,
            SalaCrm.SN
        ).outerjoin(
            ClueSheet, ClueWithdraw.ID == ClueSheet.ID
        ).outerjoin(
            SalaCrm, ClueWithdraw.ID == SalaCrm.ID
        )

        # 过滤条件
        filters = []
        
        # 关键字搜索
        if data.keyword:
            search_term = f"%{data.keyword}%"
            filters.append(or_(
                ClueWithdraw.ID.like(search_term),
                ClueWithdraw.contact_person.like(search_term),
                ClueWithdraw.withdraw_reason.like(search_term),
                ClueWithdraw.reviewer.like(search_term),
                ClueSheet.channel.like(search_term),
                ClueSheet.store.like(search_term)
            ))
        #return data.allocation_date
        if data.apply_time:
            filters.append(func.DATE(ClueWithdraw.apply_time) == data.apply_time)
        if user.identity != "超管":
            filters.append(ClueWithdraw.reviewer == user.name)

        # 应用过滤条件
        if filters:
            query = query.filter(and_(*filters))
        
        # 排序处理
        query = query.order_by(ClueWithdraw.apply_time.desc())
        
        total = query.count()
        results = query.offset(skip).limit(data.pageSize).all()


        #category 获取withdraw类型
        category_withdraw_arr = {item.id: item.name for item in db.query(Category).filter(Category.type == "withdraw",Category.pid != 0).all()}
        # 处理结果数据
        processed_items = []
        if results:
        
            for row in results:
                #return split_string(row.withdraw_evidence)
                # 构建返回数据 - 只包含指定字段
                item = {
                    # ClueWithdraw 字段
                    "ID": row.ID,
                    "approval_type": row.approval_type,
                    "approval_type_name": category_withdraw_arr.get(row.approval_type),
                    "contact_person": row.contact_person,
                    "withdraw_reason": row.withdraw_reason,
                    "withdraw_evidence": row.withdraw_evidence,
                    "withdraw_evidence_arr":split_string(row.withdraw_evidence),
                    "apply_time": row.apply_time.strftime("%Y-%m-%d %H:%M:%S") if row.apply_time else None,
                    "reviewer": row.reviewer,
                    "shenpi_time": row.shenpi_time.strftime("%Y-%m-%d %H:%M:%S") if row.shenpi_time else None,
                    "withdraw_status": row.withdraw_status,
                    "receipt": row.receipt,
                    "handle_type": row.handle_type,
                    # ClueSheet 字段
                    "record_time": row.record_time.strftime("%Y-%m-%d") if row.record_time else None,
                    "wechat_id": base64_decrypt_data(row.wechat_id) if row.wechat_id else None,
                    "channel": row.channel,
                    "store": row.store,
                    "remarks": row.remarks,
                    # SalaCrm 字段
                    "allocation_date": row.allocation_date.strftime("%Y-%m-%d") if row.allocation_date else None,
                    "SN": row.SN
                }
                processed_items.append(item)

        return {
            "list": processed_items,
            "total": total
        }
        
    except Exception as e:
        return HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 申请撤回
@router.put("/api/cehui_clue_withdraw/{lead_id}")
async def cehui_clue_withdraw(lead_id: str, data: AddClueWithdraw, request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return HTTPException(status_code=400, detail="用户未登录")
    if not data:
        return HTTPException(status_code=400, detail="数据为空")
    ClueSheetData = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).order_by(ClueSheet.record_time.desc()).first()
    if not ClueSheetData:
        return HTTPException(status_code=400, detail=f"ID为{lead_id}的线索记录不存在")

    if not (ClueSheetData.contact_person == user.name or user.identity == "超管"):
        return HTTPException(status_code=400, detail="权限不足")

    # 检查申请数据是否填写
    if not data.withdraw_evidence or not data.withdraw_reason or not data.reviewer:
        return HTTPException(status_code=400, detail="申请数据不能为空")

    # 审批状态 0:待处理，1:已通过，2:已拒绝
    db_clue_withdraw = db.query(ClueWithdraw).filter(
        and_(ClueWithdraw.ID == lead_id, ClueWithdraw.withdraw_status.in_([0, 1, 2]))).order_by(
        ClueWithdraw.apply_time.desc()).first()
    if db_clue_withdraw:
        if db_clue_withdraw.apply_num >= 2:
            return HTTPException(status_code=400, detail="申请超过2次，不能再申请")

        if db_clue_withdraw.withdraw_status == 0:
            return HTTPException(status_code=400, detail="已申请，不要重复提交")
    try:

        if not db_clue_withdraw:
            # 创建新数据
            add_data_clue_withdraw = ClueWithdraw(
                ID=lead_id,
                contact_person=user.name,
                withdraw_reason=data.withdraw_reason,
                withdraw_evidence=data.withdraw_evidence,
                reviewer=data.reviewer,
                withdraw_status=0,
                apply_time=datetime.now(),
                approval_type=data.approval_type if data.approval_type else 2,
                apply_num=1
            )
            db.add(add_data_clue_withdraw)
        else:

            if data.withdraw_evidence is not None:
                db_clue_withdraw.withdraw_evidence = data.withdraw_evidence
            if data.withdraw_reason is not None:
                db_clue_withdraw.withdraw_reason = data.withdraw_reason
            if data.reviewer is not None:
                db_clue_withdraw.reviewer = data.reviewer

            db_clue_withdraw.apply_num = db_clue_withdraw.apply_num + 1
            db_clue_withdraw.apply_time = datetime.now()
            db_clue_withdraw.withdraw_status = 0
            if db_clue_withdraw.withdraw_status == 1:  # 已通过
                db_clue_withdraw.apply_num = 1
        # 将线索表状态改为审批中
        ClueSheetData.status = 4
        db.commit()

        # 申请审批成功后，触发WebSocket推送通知
        try:
            print(f"申请审批成功，开始触发WebSocket推送通知，线索ID: {lead_id}")
            # 通过HTTP客户端调用WebSocket推送接口
            async with httpx.AsyncClient() as client:
                # 构建推送接口的完整URL
                push_url = f"{WEB_URL}/api/websocket/cehui_clue_withdraw/{lead_id}"
                print(f"📡 调用审批通知推送接口: {push_url}")

                # 发送GET请求到推送接口，设置超时时间
                response = await client.get(push_url, timeout=15.0)

                if response.status_code == 200:
                    print(f"📋 推送响应: {response.json()}")
                else:
                    print(f"❌ 申请审批通知WebSocket推送调用失败，HTTP状态码: {response.status_code}")
                    print(f"📝 响应内容: {response.text}")

        except httpx.TimeoutException:
            # 超时异常单独处理
            print(f"⏰ 申请审批通知WebSocket推送调用超时（15秒），但不影响审批流程")
        except httpx.RequestError as req_error:
            # 网络请求异常
            print(f"🌐 申请审批通知WebSocket推送网络请求失败: {str(req_error)}")
        except Exception as push_error:
            # WebSocket推送失败不影响主要的审批流程，确保错误隔离
            print(f"⚠️ 调用申请审批通知WebSocket推送时出错: {str(push_error)}")

        return {"status_code": 1, "detail": "申请成功"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return HTTPException(status_code=400, detail=str(e))


# 审核员进行审批
@router.put("/api/shenpi_cehui/{lead_id}",summary="审核员进行审批")
async def shenpi_clue_withdraw(lead_id: str, data: UpClueWithdraw, request: Request, db: Session = Depends(get_db)):
   
    # 检查申请数据是否填写
    if not data.withdraw_status:
        return HTTPException(status_code=400, detail="申请数据不能为空")

    user = get_current_user(request, db)
    if not user:
        return HTTPException(status_code=400, detail="用户未登录")
    if not data:
        return HTTPException(status_code=400, detail="数据为空")
    ClueSheetData = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).order_by(ClueSheet.record_time.desc()).first()
   
    if not ClueSheetData:
        return HTTPException(status_code=400, detail=f"ID为{lead_id}的线索记录不存在")
    #return lead_id
    db_clue_withdraw = db.query(ClueWithdraw).filter(ClueWithdraw.ID == lead_id,ClueWithdraw.withdraw_status == 0).order_by(ClueWithdraw.apply_time.desc()).first()
    if not db_clue_withdraw:
        return HTTPException(status_code=400, detail=f"ID为{lead_id}的申请记录不存在")
    
    if not (db_clue_withdraw.reviewer == user.name or user.identity == "超管"):
        return HTTPException(status_code=400, detail="权限不足")
   
    # 当拒绝的时候，回执不能为空
    if data.withdraw_status == 2 and not data.receipt:
        return HTTPException(status_code=400, detail="驳回申请回执必填")
  
    # 当同意的时候，提交线索状态不能为空
    # status=2审批通过且选择"重新分发"：线索状态由4变更为2（等待中）//#status=5审批通过且选择"标记已撤回"：线索状态由4变更为5（已撤回）
    if data.withdraw_status == 1 and not data.handle_type:
        return HTTPException(status_code=400, detail="审批通过类型必填")
  
    try:
        # 1同意
        if data.withdraw_status == 1:
            
            db_clue_withdraw.receipt = data.receipt if data.receipt else None
            db_clue_withdraw.withdraw_status = data.withdraw_status
            db_clue_withdraw.shenpi_time = datetime.now()
            db_clue_withdraw.handle_type = data.handle_type #处理方式
          
            # 将contact_person追加到ClueSheet的not_admin字段中
            if db_clue_withdraw.contact_person:
                if ClueSheetData.not_admin:
                    # 如果not_admin已有内容，追加新内容
                    ClueSheetData.not_admin = f"{ClueSheetData.not_admin},{db_clue_withdraw.contact_person}"
                else:
                    # 如果not_admin为空，直接设置
                    ClueSheetData.not_admin = db_clue_withdraw.contact_person
            
            ClueSheetData.contact_person = ""
            ClueSheetData.status = data.handle_type
            ClueSheetData.withdraw_contact_person = db_clue_withdraw.contact_person if not ClueSheetData.withdraw_contact_person else f"{ClueSheetData.withdraw_contact_person},{db_clue_withdraw.contact_person}"
            
        # 2驳回
        else:  # data.withdraw_status ==2:
            db_clue_withdraw.withdraw_status = data.withdraw_status
            db_clue_withdraw.shenpi_time = datetime.now()
            db_clue_withdraw.receipt = data.receipt
            # 将线索表状态改为审批中
            ClueSheetData.status = 3
       
        db.commit()

        # 审批操作成功后，触发WebSocket推送通知
        try:
            print(f"🚀 审批操作成功，开始触发WebSocket推送通知，线索ID: {lead_id}")

            # 通过HTTP客户端调用WebSocket推送接口
            async with httpx.AsyncClient() as client:
                # 构建推送接口的完整URL
                push_url = f"{WEB_URL}/api/websocket/shenpi_cehui/{lead_id}"
                print(f"📡 调用审批通知推送接口: {push_url}")

                # 发送GET请求到推送接口，设置超时时间
                response = await client.get(push_url, timeout=15.0)

                if response.status_code == 200:
                    print(f"✅ 审批通知WebSocket推送调用成功")
                    print(f"📋 推送响应: {response.json()}")
                else:
                    print(f"❌ 审批通知WebSocket推送调用失败，HTTP状态码: {response.status_code}")
                    print(f"📝 响应内容: {response.text}")

        except httpx.TimeoutException:
            # 超时异常单独处理
            print(f"⏰ 审批通知WebSocket推送调用超时（15秒），但不影响审批流程")
        except httpx.RequestError as req_error:
            # 网络请求异常
            print(f"🌐 审批通知WebSocket推送网络请求失败: {str(req_error)}")
        except Exception as push_error:
            # WebSocket推送失败不影响主要的审批流程，确保错误隔离
            print(f"⚠️ 调用审批通知WebSocket推送时出错: {str(push_error)}")

        return {"status_code": 1, "detail": "操作成功"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return HTTPException(status_code=400, detail=str(e))


@router.get("/api/tongzhi_leads_withdraw", summary="以ClueWithdraw为主表的三表联查，返回指定字段")
def get_tongzhi_leads_withdraw(request: Request, db: Session = Depends(get_db)):
    try:
        user = get_current_user(request, db)
        if not user:
            return HTTPException(status_code=400, detail="用户未登录")
        # 构建查询 - 只选择需要的字段
        query = db.query(
            ClueWithdraw.ID,
            ClueWithdraw.reviewer,
            ClueWithdraw.withdraw_reason,
            ClueWithdraw.withdraw_evidence,
            ClueWithdraw.shenpi_time,
            ClueWithdraw.withdraw_status,
            ClueWithdraw.receipt,
            ClueWithdraw.is_tongzhi,
            # ClueSheet 表字段
            ClueSheet.wechat_id,
            ClueSheet.channel,
            ClueSheet.store,
            ClueSheet.remarks,
        ).outerjoin(
            ClueSheet, ClueWithdraw.ID == ClueSheet.ID
        )

        # 过滤条件
        filters = []
        filters.append(ClueWithdraw.contact_person == user.name)
        filters.append(ClueWithdraw.is_tongzhi == 0)#是否通知
        filters.append(ClueWithdraw.withdraw_status.in_([1,2]))
        query = query.filter(and_(*filters))

        total = query.count()
        results = query.all()
        #销售点开全都变为已通知
        db.query(ClueWithdraw).filter(ClueWithdraw.contact_person == user.name,
                                                                    ClueWithdraw.is_tongzhi == 0,
                                                                    ClueWithdraw.withdraw_status.in_([1, 2])).update({ClueWithdraw.is_tongzhi: 1})
        db.commit()

        # 处理结果数据
        processed_items = []
        if results:
            for row in results:
                # 构建返回数据 - 只包含指定字段
                item = {
                    # ClueWithdraw 字段
                    "ID": row.ID,
                    "withdraw_reason": row.withdraw_reason,
                    "withdraw_evidence": row.withdraw_evidence,
                    "withdraw_evidence_arr": split_string(row.withdraw_evidence),
                    "reviewer": row.reviewer,
                    "shenpi_time": row.shenpi_time.strftime("%Y-%m-%d %H:%M:%S") if row.shenpi_time else None,
                    "withdraw_status": row.withdraw_status,
                    "receipt": row.receipt,
                    # ClueSheet 字段
                    "wechat_id": base64_decrypt_data(row.wechat_id) if row.wechat_id else None,
                    "channel": row.channel,
                    "store": row.store,
                    "remarks": row.remarks
                }
                processed_items.append(item)

        return {
            "list": processed_items,
            "total": total
        }

    except Exception as e:
        return HTTPException(status_code=400, detail=f"查询失败: {str(e)}")
    

@router.get("/api/leads_withdraw", summary="查询所有审批待处理的所有日期")
def get_leads_withdraw_riqi_list(request: Request, db: Session = Depends(get_db)):
    """
    查询所有审批待处理的申请日期列表

    功能说明：
    1. 只查询 apply_time 字段（申请时间）
    2. 使用 DATE() 函数提取日期部分，忽略时间部分
    3. 使用 GROUP BY 对日期进行去重
    4. 返回不重复的日期列表，按日期排序

    返回格式：
    {
        "list": ["2025-07-11", "2025-07-12", "2025-07-13"],
        "total": 3
    }
    """
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            return HTTPException(status_code=400, detail="用户未登录")

        # 构建过滤条件
        filters = []

        # 线索查看权限（全部/组内/个人）
        if user.identity != "超管":
            filters.append(ClueWithdraw.reviewer == user.name)

        # 只查询待处理状态的申请
        filters.append(ClueWithdraw.withdraw_status == 0)

        # 使用 DATE() 函数提取日期部分，GROUP BY 去重，ORDER BY 排序
        date_query = db.query(
            func.DATE(ClueWithdraw.apply_time).label('apply_date')
        ).filter(
            and_(*filters)
        ).group_by(
            func.DATE(ClueWithdraw.apply_time)  # 按日期分组去重
        ).order_by(
            func.DATE(ClueWithdraw.apply_time).desc()  # 按日期降序排列
        )

        # 执行查询获取结果
        date_results = date_query.all()

        # 处理查询结果，转换为字符串格式的日期列表
        date_list = []
        for result in date_results:
            if result.apply_date:  # 确保日期不为空
                date_list.append(result.apply_date.strftime("%Y-%m-%d"))

        return {
            "list": date_list,
            "total": len(date_list)
        }

    except Exception as e:
        return HTTPException(status_code=400, detail=f"查询失败: {str(e)}")