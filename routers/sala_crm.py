from typing import Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session, joinedload
from database import get_db
from models import ClueSheet, User, SalaCrm,SalaCrmHistory,SalaCrmHistoryOpenseas,ClueWithdraw, UserPermissionFrontend, DistributionQueue, OrderBillNumber
from datetime import datetime, timedelta
from pydantic import BaseModel
from auth import get_current_user, base64_decrypt_data
import json
import logging

logger = logging.getLogger(__name__)


# 创建新线索
class AddsalaCrm(BaseModel):
    ID: Optional[str] = None
    allocation_date: Optional[str] = None
    SN: Optional[str] = None
    wechat_name: Optional[str] = None
    is_add: Optional[int] = None
    add_date: Optional[datetime] = None
    reason_failure: Optional[str] = None
    customer_name: Optional[int] = None
    is_billed: Optional[int] = None
    bill_number: Optional[str] = None
    bill_riqi: Optional[datetime] = None
    bill_use_time: Optional[float] = None
    customer_record: Optional[str] = None
    keyword_tips: Optional[str] = None
    self_reminder_flag: Optional[str] = None
    clue_basic_tag: Optional[str] = None
    clue_stage: Optional[str] = None
    last_followup_time: Optional[datetime] = None
    last_followup_record: Optional[str] = None
    failure_analysis: Optional[str] = None
    is_read: int = 0


# 更新线索
class UpsalaCrm(BaseModel):
    class Config:
        # 只包含实际设置的字段，忽略未设置的字段
        exclude_unset = True

    ID: Optional[str] = None
    wechat_name: Optional[str] = None
    is_add: Optional[int] = None
    add_date: Optional[str] = None
    reason_failure: Optional[str] = None
    customer_name: Optional[int] = None
    is_billed: Optional[int] = None
    bill_number: Optional[str] = None
    customer_record: Optional[str] = None
    customer_record_images: Optional[str] = None
    keyword_tips: Optional[str] = None
    self_reminder_flag: Optional[str] = None
    clue_basic_tag: Optional[str] = None
    clue_stage: Optional[str] = None
    last_followup_time: Optional[datetime] = None
    last_followup_record: Optional[str] = None
    failure_analysis: Optional[str] = None
    is_read: Optional[int] = None
    chat_image: Optional[str] = None

    # 需求特征字段
    s_demand_feature: Optional[str] = None
    a_demand_feature: Optional[str] = None
    b_demand_feature: Optional[str] = None
    c_demand_feature: Optional[str] = None
    s_zu_demand_feature: Optional[str] = None
    a_zu_demand_feature: Optional[str] = None
    b_zu_demand_feature: Optional[str] = None
    c_zu_demand_feature: Optional[str] = None
    zu_customer_name: Optional[int] = None
    #ClueSheet
    kefu_image: Optional[str] = None
   


router = APIRouter()

#通用的历史记录追加函数，将新值追加到指定表的历史记录字段中，保持JSON格式结构
def add_history_data(model_class: str, record_id: str, new_value: str,images_history: str, history_field_name: str, db: Session) -> bool:
    
    try:
        # 根据字符串获取对应的模型类对象
        actual_model_class = None
        if model_class == "SalaCrmHistory":
            actual_model_class = SalaCrmHistory
        elif model_class == "SalaCrmHistoryOpenseas":
            actual_model_class = SalaCrmHistoryOpenseas
        else:
            return False
       
        # 查询记录
        record = db.query(actual_model_class).filter(actual_model_class.ID == record_id).first()
        if not record:
            return False

        # 检查历史字段是否存在
        if not hasattr(record, history_field_name):
            return False

        # 只有当新值不为空时才追加历史记录
        if not new_value or not new_value.strip():
            # 对于空值，我们仍然需要记录这个变更
            new_value = ""  # 统一处理为空字符串
        if not images_history:
            images_history = ""
        # 创建新的历史记录条目
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        new_record = {"datetime": current_time, "remark": new_value.strip()}
        if history_field_name in {"customer_history_record"}:
            new_record = {"datetime": current_time, "remark": new_value.strip(),"images":images_history}
      
        # 获取现有的历史记录
        existing_history = {}
        current_history_value = getattr(record, history_field_name)

        if current_history_value:
            try:
                existing_history = json.loads(current_history_value)
            except (json.JSONDecodeError, TypeError):
                existing_history = {}

        # 计算下一个索引
        if existing_history:
            # 获取所有数字键并找到最大值
            numeric_keys = [int(k) for k in existing_history.keys() if k.isdigit()]
            next_index = max(numeric_keys) + 1 if numeric_keys else 0
        else:
            next_index = 0

        # 添加新记录
        existing_history[str(next_index)] = new_record

        # 更新数据库字段
        setattr(record, history_field_name, json.dumps(existing_history, ensure_ascii=False))

        # 提交更改
        db.commit()
        return True

    except Exception as e:
        db.rollback()
        return False

@router.put("/api/sala_crm/{lead_id}",summary="编辑客情记录")
def update_sala_crm(lead_id: str, data: UpsalaCrm, request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return HTTPException(status_code=400, detail="用户未登录")

    # 获取实际传递的字段，忽略未设置的字段
    update_data = data.model_dump(exclude_unset=True)
    if not update_data:
        return HTTPException(status_code=400, detail="数据为空")
   
    SalaCrmHistoryData = db.query(SalaCrmHistory).filter(SalaCrmHistory.ID == lead_id).first()
   
    if not SalaCrmHistoryData:
        # 创建新数据
        SalaCrmHistoryData = SalaCrmHistory(
            ID=lead_id
        )
        db.add(SalaCrmHistoryData)
    
    ClueSheetData = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).order_by(ClueSheet.record_time.desc()).first()
    if not ClueSheetData:
        return HTTPException(status_code=400, detail=f"ID为{lead_id}的线索记录不存在")
    
    # 查询组长
    group_users_name = [user.name for user in db.query(User).filter(User.department == "销售部",User.group_name == user.group_name,User.identity == "组长").all()]
    if not (ClueSheetData.contact_person == user.name or user.identity == "超管" or user.name in group_users_name):
        return HTTPException(status_code=400, detail="权限不足")
    
    db_sala_crm = db.query(SalaCrm).filter(SalaCrm.ID == lead_id).order_by(SalaCrm.last_followup_time.desc()).first()
    if not db_sala_crm:
        # 创建新数据
        db_sala_crm = SalaCrm(
            ID=lead_id,
            last_followup_time=ClueSheetData.record_time,
            SN=0
        )
        db.add(db_sala_crm)
    #return 112121
    """更新线索记录"""
    try:
       
        if update_data.get("wechat_name"):
            wechat_name = update_data.get("wechat_name") 
            db_sala_crm.wechat_name =wechat_name
        if update_data.get("is_add"):
            is_add = update_data.get("is_add") 
            db_sala_crm.is_add = is_add
            if is_add == 1:
                db_sala_crm.add_date = datetime.now()
        if update_data.get("add_date"):
            add_date = update_data.get("add_date")
            db_sala_crm.add_date = add_date
        if update_data.get("reason_failure"):
            reason_failure = update_data.get("reason_failure")
            db_sala_crm.reason_failure = reason_failure
        if update_data.get("customer_name"):
            customer_name = update_data.get("customer_name")
            db_sala_crm.customer_name = customer_name
        if update_data.get("is_billed"):
            is_billed = update_data.get("is_billed")
            db_sala_crm.is_billed = is_billed
            if is_billed == 1:
                # 开单日期，开单用时
                db_sala_crm.bill_riqi = datetime.now()
                delta = db_sala_crm.bill_riqi - db_sala_crm.allocation_date
                total_days = round(delta.total_seconds() / (3600 * 24),1)
                db_sala_crm.bill_use_time = total_days

                # 将自动计算的字段添加到返回数据中
                update_data["bill_use_time"] = total_days
                update_data["bill_riqi"] = db_sala_crm.bill_riqi.strftime("%Y-%m-%d %H:%M:%S")

        bill_number = update_data.get("bill_number")
        if bill_number:
            db_sala_crm.bill_number = bill_number

        keyword_tips = update_data.get("keyword_tips")
        if keyword_tips:
            db_sala_crm.keyword_tips = keyword_tips
        clue_basic_tag = update_data.get("clue_basic_tag")
        if clue_basic_tag:
            db_sala_crm.clue_basic_tag = update_data.clue_basic_tag
        clue_stage = update_data.get("clue_stage")
        if clue_stage:
            FOLLOW_UP_STAGES = {"已报价-跟进中", "跟进-有意向"}
            if clue_stage in FOLLOW_UP_STAGES and db_sala_crm.clue_stage not in FOLLOW_UP_STAGES:
               db_sala_crm.clue_stage_follow_time = datetime.now()
            db_sala_crm.clue_stage = clue_stage
        last_followup_time = update_data.get("last_followup_time")
        if last_followup_time:
            db_sala_crm.last_followup_time = last_followup_time

        last_followup_record = update_data.get("last_followup_record")
        if last_followup_record:
            db_sala_crm.last_followup_record = last_followup_record

        if update_data.get("failure_analysis"):
            failure_analysis = update_data.get("failure_analysis")
            db_sala_crm.failure_analysis = failure_analysis
        if update_data.get("is_read"):
            is_read = update_data.get("is_read")
            db_sala_crm.is_read = is_read
        if update_data.get("chat_image"):
            chat_image = update_data.get("chat_image")
            db_sala_crm.chat_image = chat_image
        zu_customer_name = update_data.get("zu_customer_name")
        if zu_customer_name and isinstance(zu_customer_name, (int, str)):
            db_sala_crm.zu_customer_name = zu_customer_name
       
        # 处理客情记录和图片 - 只有当前端明确传递了这些字段时才更新
        customer_record_updated = False
        images_history = ""

        # 检查是否传递了customer_record字段
        if "customer_record" in update_data:
            customer_record = update_data.get("customer_record")
            if customer_record != db_sala_crm.customer_record:
                db_sala_crm.customer_record = customer_record
                customer_record_updated = True

        # 检查是否传递了customer_record_images字段
        if "customer_record_images" in update_data:
            customer_record_images = update_data.get("customer_record_images")
            images_history = customer_record_images if customer_record_images else ""
            if db_sala_crm.customer_record_images != customer_record_images:
                db_sala_crm.customer_record_images = customer_record_images
                customer_record_updated = True

        # 只有当客情记录或图片有更新时才保存历史记录
        if customer_record_updated:
            try:
                # 使用当前数据库中的值作为历史记录
                current_customer_record = db_sala_crm.customer_record or ""
                add_history_data('SalaCrmHistory',lead_id,current_customer_record,images_history,'customer_history_record', db)
            except Exception as e:
                print(f"保存客情历史记录失败: {e}")  # 不阻断主要业务逻辑
        self_reminder_flag = update_data.get("self_reminder_flag")
        if self_reminder_flag is not None and self_reminder_flag != db_sala_crm.self_reminder_flag:
            db_sala_crm.self_reminder_flag = self_reminder_flag
            try:
                add_history_data('SalaCrmHistory',lead_id,self_reminder_flag,images_history,'self_reminder_history_flag', db)
            except Exception as e:
                return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        s_demand_feature = update_data.get("s_demand_feature")
        if s_demand_feature:
            if s_demand_feature != db_sala_crm.s_demand_feature:
                db_sala_crm.s_demand_feature = s_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,s_demand_feature,images_history,'s_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        a_demand_feature = update_data.get("a_demand_feature")
        if a_demand_feature:
            if a_demand_feature != db_sala_crm.a_demand_feature:
                db_sala_crm.a_demand_feature = a_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,a_demand_feature,images_history,'a_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        b_demand_feature = update_data.get("b_demand_feature")
        if b_demand_feature:
            if b_demand_feature != db_sala_crm.b_demand_feature:
                db_sala_crm.b_demand_feature = b_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,b_demand_feature,images_history,'b_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        b_demand_feature = update_data.get("b_demand_feature")
        if b_demand_feature:
            if b_demand_feature != db_sala_crm.b_demand_feature:
                db_sala_crm.b_demand_feature = b_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,b_demand_feature,images_history,'b_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        c_demand_feature = update_data.get("c_demand_feature")
        if update_data.get("c_demand_feature") is not None:
            if c_demand_feature != db_sala_crm.c_demand_feature:
                db_sala_crm.c_demand_feature = c_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,c_demand_feature,images_history,'c_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        s_zu_demand_feature = update_data.get("s_zu_demand_feature")
        if s_zu_demand_feature:
            if s_zu_demand_feature != db_sala_crm.s_zu_demand_feature:
                db_sala_crm.s_zu_demand_feature = s_zu_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,s_zu_demand_feature,images_history,'s_zu_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        a_zu_demand_feature = update_data.get("a_zu_demand_feature")
        if a_zu_demand_feature:
            if a_zu_demand_feature != db_sala_crm.a_zu_demand_feature:
                db_sala_crm.a_zu_demand_feature = a_zu_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,a_zu_demand_feature,images_history,'a_zu_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        b_zu_demand_feature = update_data.get("b_zu_demand_feature")  
        if b_zu_demand_feature is not None:
            if b_zu_demand_feature != db_sala_crm.b_zu_demand_feature:
                db_sala_crm.b_zu_demand_feature = b_zu_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,b_zu_demand_feature,images_history,'b_zu_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        c_zu_demand_feature = update_data.get("c_zu_demand_feature")
        if c_zu_demand_feature:
            if c_zu_demand_feature != db_sala_crm.c_zu_demand_feature:
                db_sala_crm.c_zu_demand_feature = c_zu_demand_feature
                try:
                    add_history_data('SalaCrmHistory',lead_id,c_zu_demand_feature,images_history,'c_zu_demand_feature_history', db)
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")

        kefu_image = update_data.get("kefu_image")
        if kefu_image and ClueSheetData.kefu_image != kefu_image:#ClueSheet的客服截图
            ClueSheetData.kefu_image = kefu_image
            db.commit()
            db.refresh(ClueSheetData)
            return {"status_code": 1, "detail": "更新成功", "id": lead_id}
        # 更新到最新的最后跟进时间
        db_sala_crm.last_followup_time = datetime.now()
       
        db.commit()
        db.refresh(db_sala_crm)

        # 返回成功信息和实际更新的数据
        return {
            "status_code": 1,
            "detail": "更新成功",
            "id": lead_id,
            "updated_fields": update_data
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return HTTPException(status_code=400, detail=str(e))


# 客情列表
# 更新线索
class PostSalaCrm(BaseModel):
    page: Optional[int] = 1
    pageSize: Optional[int] = 20
    allocation_date: Optional[str] = None
    contact_person: Optional[str] = None
    keyword: Optional[str] = None
    filters: Optional[List] = None
    sortField: Optional[str] = "allocation_date"
    sortOrder: Optional[str] = "desc"


@router.post("/api/sala_crm", summary="日期查询，如2025/05/11")
def post_sala_crm_list(data: PostSalaCrm, request: Request, db: Session = Depends(get_db)):
    # 分页，计算跳过数据量（作用不明）
    skip = (data.page - 1) * data.pageSize
    filters = []

    # 分配日期
    if data.allocation_date is not None:
        # return data.allocation_date
        filters.append(func.DATE(SalaCrm.allocation_date) == data.allocation_date)

    filters.append(SalaCrm.is_deleted == False)

    if data.keyword:
        # 如果搜索词可能是电话或微信ID，尝试进行加密搜索
        search_term = f"%{data.keyword}%"
        filters.append(
            SalaCrm.ID.like(search_term) |
            SalaCrm.SN.like(search_term) |
            SalaCrm.wechat_name.like(search_term) |
            SalaCrm.reason_failure.like(search_term) |
            SalaCrm.customer_name.like(search_term) |
            SalaCrm.bill_number.like(search_term) |
            SalaCrm.keyword_tips.like(search_term) |
            SalaCrm.clue_basic_tag.like(search_term) |
            SalaCrm.failure_analysis.like(search_term)
        )

    Field_in_all = ["clue_basic_tag", "clue_stage"]

    if data.filters:
        for filter_item in data.filters:
            for key, value in filter_item.items():

                if key in Field_in_all and value:
                    if hasattr(ClueSheet, key):
                        condition = getattr(ClueSheet, key).in_(value)
                        filters.append(condition)
                    elif hasattr(SalaCrm, key):
                        condition = getattr(SalaCrm, key).in_(value)
                        filters.append(condition)

                elif key == "allocation_date" and value:
                    # 日期范围过滤
                    start = datetime.strptime(value["start"], "%Y-%m-%d")
                    end = datetime.strptime(value["end"], "%Y-%m-%d") + timedelta(days=1)
                    filters.append(SalaCrm.allocation_date >= start)
                    filters.append(SalaCrm.allocation_date < end)
                elif value:
                    if hasattr(ClueSheet, key):
                        condition = getattr(ClueSheet, key) == value
                        filters.append(condition)
                    if hasattr(SalaCrm, key):
                        condition = getattr(SalaCrm, key) == value
                        filters.append(condition)

    # 线索查看权限（全部/组内/个人）
    user = get_current_user(request, db)
    existing_frontend = db.query(UserPermissionFrontend).filter(
        UserPermissionFrontend.department == user.department,
        UserPermissionFrontend.identity == user.identity
    ).first()

    if existing_frontend.lead_crm==0 and user.identity!="超管":
        # 个人
        if existing_frontend.check_person_crm == 0 and user.department in {"电商部", "新媒体部"}:
            filters.append(ClueSheet.registrar == user.name)
        if existing_frontend.check_person_crm == 0 and user.department == "销售部" :
            filters.append(ClueSheet.contact_person == user.name)
        # 组内
        if existing_frontend.check_group_crm ==0 and user.department in {"电商部", "新媒体部"}:
            # user_name_arr = [user_name for user_name, in
            #                  db.query(User.name).filter(User.group_name == user.group_name).all()]
            # filters.append(ClueSheet.registrar.in_(user_name_arr))
            filters.append(ClueSheet.registrar == user.name)
        if existing_frontend.check_group_crm ==0 and user.department in {"销售部"}:
            user_name_arr = [user_name for user_name, in
                             db.query(User.name).filter(User.group_name == user.group_name).all()]
            filters.append(ClueSheet.contact_person.in_(user_name_arr))
      
    # 获取线索ID
    filters.append(SalaCrm.is_deleted == False)
    filters.append(ClueSheet.is_deleted == False)

    list = db.query(SalaCrm).join(SalaCrm.clue_sheet).options(joinedload(SalaCrm.clue_sheet)).filter(
        and_(*filters)).order_by(SalaCrm.allocation_date.desc()).offset(skip).limit(data.pageSize).all()
    
    total = db.query(SalaCrm).join(SalaCrm.clue_sheet).options(joinedload(SalaCrm.clue_sheet)).filter(
        and_(*filters)).count()

    decrypted_items = []
    for item in list:
        if item.allocation_date:
            item.allocation_date = item.allocation_date.strftime("%Y-%m-%d %H:%M:%S")
        if item.last_followup_time:
            item.last_followup_time = item.last_followup_time.strftime("%Y-%m-%d %H:%M:%S")
        if item.bill_riqi:
            item.bill_riqi = item.bill_riqi.strftime("%Y-%m-%d %H:%M:%S")
        if item.add_date:
            item.add_date = item.add_date.strftime("%Y-%m-%d")
        if item.clue_sheet.phone_number:
            item.clue_sheet.phone_number = base64_decrypt_data(item.clue_sheet.phone_number)
        if item.clue_sheet.wechat_id:
            item.clue_sheet.wechat_id = base64_decrypt_data(item.clue_sheet.wechat_id)
        decrypted_items.append(item)

    return {
        "list": decrypted_items,
        "total": total
    }


# 更新线索
class PlUpsalaCrm(BaseModel):
    IDS: Optional[List] = str
    wechat_name: Optional[str] = None
    is_add: Optional[int] = None
    add_date: Optional[datetime] = None
    reason_failure: Optional[str] = None
    customer_name: Optional[str] = None
    is_billed: Optional[int] = None
    bill_number: Optional[str] = None
    customer_record: Optional[str] = None
    keyword_tips: Optional[str] = None
    clue_basic_tag: Optional[str] = None
    clue_stage: Optional[str] = None
    last_followup_time: Optional[datetime] = None
    last_followup_record: Optional[str] = None
    failure_analysis: Optional[str] = None
    is_read: Optional[int] = None


# 批量修改客户信息
# @router.post("/api/sala_crm/plup")
# async def pl_update_sala_crm(data: PlUpsalaCrm, request: Request,db: Session = Depends(get_db)):
#
#     user = get_current_user(request, db)
#     if not user:
#         return HTTPException(status_code=400, detail="用户未登录")
#     if not data.IDS:
#         return HTTPException(status_code=400, detail="请选中要批量修改的数据")
#     # 添加失败必须要填原因
#     if data.is_add==2 and not data.reason_failure:
#         return HTTPException(status_code=400, detail="添加失败必须要填原因")
#
#     if data.wechat_name is not None:
#         db_sala_crm.wechat_name = data.wechat_name
#     if data.is_add is not None:
#         db_sala_crm.is_add = data.is_add
#         if data.is_add == 1:
#             db_sala_crm.is_add = datetime.now()
#     if data.reason_failure is not None:
#         db_sala_crm.reason_failure = data.reason_failure
#     if data.customer_name is not None:
#         db_sala_crm.customer_name = data.customer_name
#     if data.is_billed is not None:
#         db_sala_crm.is_billed = data.is_billed
#     if data.bill_number is not None:
#         db_sala_crm.bill_number = data.bill_number
#     if data.customer_record is not None:
#         db_sala_crm.customer_record = data.customer_record
#     if data.keyword_tips is not None:
#         db_sala_crm.keyword_tips = data.keyword_tips
#     if data.clue_basic_tag is not None:
#         db_sala_crm.clue_basic_tag = data.clue_basic_tag
#     if data.clue_stage is not None:
#         db_sala_crm.clue_stage = data.clue_stage
#     if data.last_followup_time is not None:
#         db_sala_crm.last_followup_time = data.last_followup_time
#     if data.last_followup_record is not None:
#         db_sala_crm.last_followup_record = data.last_followup_record
#     if data.failure_analysis is not None:
#         db_sala_crm.failure_analysis = data.failure_analysis
#     if data.is_read is not None:
#         db_sala_crm.is_read = data.is_read
#
#     for ID in data.IDS:
#         check_data = db.query(ClueSheet).filter(ClueSheet.ID == ID,SalaCrm.is_deleted == False).order_by(
#             ClueSheet.record_time.desc()).first()
#         if not check_data:
#             return HTTPException(status_code=400, detail=f"ID为{ID}的线索记录不存在")
#         if not (check_data.contact_person == user.name or user.identity == "超管"):
#             return HTTPException(status_code=400, detail="权限不足")
#
#     db_sala_crm = db.query(SalaCrm).filter(SalaCrm.ID == lead_id).order_by(SalaCrm.last_followup_time.desc()).first()
#     if not db_sala_crm:
#         # 创建新数据
#         db_sala_crm = SalaCrm(
#             ID=lead_id,
#             last_followup_time=ClueSheetData.record_time,
#             SN=0
#         )
#         db.add(db_sala_crm)

# 获取订单编号
class OrderBill(BaseModel):
    sala_id: str


@router.post("/api/get_bill_number", summary="日期查询，如2025/05/11")
def post_sala_crm_list(data: OrderBill, request: Request, db: Session = Depends(get_db)):
    if not data.sala_id:
        return HTTPException(status_code=400, detail="参数为空")

    check_sala = db.query(SalaCrm).filter(SalaCrm.ID == data.sala_id).first()
    if not check_sala:
        return HTTPException(status_code=400, detail="数据不存在")

    order_sala = db.query(OrderBillNumber).filter(OrderBillNumber.sala_id == data.sala_id).first()
    if order_sala:
        return {"data": order_sala.orderno}
    else:
        db_order_bill_number = db.query(OrderBillNumber).with_entities(OrderBillNumber.id).order_by(
            OrderBillNumber.id.desc()).first()
        last_id = db_order_bill_number.id

        new_last_id = last_id + 1
        new_orderno = "MJ" + str(new_last_id)
        # 创建新数据
        db_order_bill_number = OrderBillNumber(
            id=new_last_id,
            sala_id=data.sala_id,
            orderno=new_orderno,
            created_at=datetime.now()
        )
        db.add(db_order_bill_number)
        db.commit()

        return {
            "data": new_orderno
        }


@router.post("/api/leads_sala_crm", summary="以ClueSheet为主表的三表联查，支持多条件过滤")
def post_leads_sala_crm_list(data: PostSalaCrm, request: Request, db: Session = Depends(get_db)):
    """
    以ClueSheet为主表的三表联合查询接口
    关联 SalaCrm 和 ClueWithdraw 表，支持多条件过滤和权限控制
    """
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="用户未登录")

        data.pageSize = 200
        skip = (data.page - 1) * data.pageSize

        # 构建查询 - 以 ClueSheet 为主表进行左外连接
        query = db.query(
            # ClueSheet 表字段
            ClueSheet.ID,
            ClueSheet.wechat_id,
            ClueSheet.phone_number,
            ClueSheet.channel,
            ClueSheet.store,
            ClueSheet.queue,
            ClueSheet.type,
            ClueSheet.status,
            ClueSheet.contact_person,
            ClueSheet.record_time,
            ClueSheet.remarks,
            ClueSheet.registrar,
            # SalaCrm 表字段 (可能为空)
            SalaCrm.SN,
            SalaCrm.is_read,
            SalaCrm.is_add,
            SalaCrm.reason_failure,
            SalaCrm.allocation_date,
            SalaCrm.wechat_name,
            SalaCrm.customer_name,
            # ClueWithdraw 表字段 (可能为空)
            ClueWithdraw.withdraw_reason,
            ClueWithdraw.withdraw_evidence,
            ClueWithdraw.apply_time,
            ClueWithdraw.reviewer,
            ClueWithdraw.withdraw_status,
            ClueWithdraw.receipt
        ).outerjoin(
            SalaCrm, ClueSheet.ID == SalaCrm.ID
        ).outerjoin(
            ClueWithdraw, ClueSheet.ID == ClueWithdraw.ID
        )

        # 构建过滤条件
        filters = []

        # 基础过滤条件
        filters.append(ClueSheet.is_deleted == False)
        # 分配日期过滤
        data.allocation_date = data.allocation_date if data.allocation_date else datetime.now().date()
        filters.append(func.DATE(SalaCrm.allocation_date) == data.allocation_date)
        if data.contact_person:
            filters.append(ClueSheet.contact_person == data.contact_person)

        # 关键字搜索 - 跨三表多字段搜索
        if data.keyword:
            search_term = f"%{data.keyword}%"
            filters.append(
                or_(
                    ClueSheet.ID.like(search_term),
                    ClueSheet.channel.like(search_term),
                    ClueSheet.store.like(search_term),
                    ClueSheet.contact_person.like(search_term),
                    ClueSheet.registrar.like(search_term),
                    ClueSheet.remarks.like(search_term),
                    SalaCrm.wechat_name.like(search_term),
                    SalaCrm.customer_name.like(search_term),
                    SalaCrm.reason_failure.like(search_term),
                    ClueWithdraw.withdraw_reason.like(search_term),
                    ClueWithdraw.reviewer.like(search_term)
                )
            )

        # 高级过滤条件处理
        if data.filters:
            for filter_item in data.filters:
                for key, value in filter_item.items():
                    if not value:
                        continue

                    # 标签和阶段等多选过滤
                    if key in ["clue_basic_tag", "clue_stage"] and isinstance(value, list):
                        if hasattr(SalaCrm, key):
                            condition = getattr(SalaCrm, key).in_(value)
                            filters.append(condition)

                    # 日期范围过滤
                    elif key == "allocation_date" and isinstance(value, dict):
                        if "start" in value and value["start"]:
                            start_date = datetime.strptime(value["start"], "%Y-%m-%d")
                            filters.append(SalaCrm.allocation_date >= start_date)
                        if "end" in value and value["end"]:
                            end_date = datetime.strptime(value["end"], "%Y-%m-%d") + timedelta(days=1)
                            filters.append(SalaCrm.allocation_date < end_date)

                    # 其他单值过滤
                    else:
                        if hasattr(ClueSheet, key):
                            condition = getattr(ClueSheet, key) == value
                            filters.append(condition)
                        elif hasattr(SalaCrm, key):
                            condition = getattr(SalaCrm, key) == value
                            filters.append(condition)
                        elif hasattr(ClueWithdraw, key):
                            condition = getattr(ClueWithdraw, key) == value
                            filters.append(condition)

        # 权限控制：根据用户部门和权限限制数据访问范围
        existing_frontend = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.department == user.department,
            UserPermissionFrontend.identity == user.identity
        ).first()

        if user.department == "销售部" and existing_frontend:
            # 个人权限：只能查看自己的数据
            if existing_frontend.check_person_crm == 0:
                filters.append(ClueSheet.contact_person == user.name)
            # 组内权限：只能查看同组数据
            elif existing_frontend.check_group_crm == 0:
                user_name_arr = [
                    user_name for user_name, in
                    db.query(User.name).filter(User.group_name == user.group_name).all()
                ]
                filters.append(ClueSheet.contact_person.in_(user_name_arr))

        # 新媒体部：只能查看自己登记的数据
        elif user.department == "新媒体部":
            filters.append(ClueSheet.registrar == user.name)

        # 应用所有过滤条件
        if filters:
            query = query.filter(and_(*filters))

        # 排序处理
        query = query.order_by(SalaCrm.allocation_date.desc(),SalaCrm.SN.desc())

        # 执行查询
        total = query.count()
        results = query.offset(skip).limit(data.pageSize).all()

        # 处理结果数据
        processed_items = []
        for row in results:
            # 构建返回数据结构
            item = {
                # ClueSheet 基础信息
                "clue_sheet": {
                    "ID": row.ID,
                    "wechat_id": base64_decrypt_data(row.wechat_id) if row.wechat_id else None,
                    "phone_number": base64_decrypt_data(row.phone_number) if row.phone_number else None,
                    "channel": row.channel,
                    "store": row.store,
                    "queue": row.queue,
                    "type": row.type,
                    "status": row.status,
                    "contact_person": row.contact_person,
                    "registrar": row.registrar,
                    "remarks": row.remarks,
                    "record_time": row.record_time.strftime("%Y-%m-%d %H:%M:%S") if row.record_time else None
                },
                # SalaCrm CRM信息 (可能为空)
                "sala_crm": {
                    "SN": row.SN,
                    "is_add": row.is_add,
                    "is_read": row.is_read,
                    "reason_failure": row.reason_failure,
                    "allocation_date": row.allocation_date.strftime("%Y-%m-%d") if row.allocation_date else None,
                    "wechat_name": row.wechat_name,
                    "customer_name": row.customer_name,
                },
                # ClueWithdraw 撤回信息 (可能为空)
                "clue_withdraw": {
                    "withdraw_reason": row.withdraw_reason,
                    "withdraw_evidence": row.withdraw_evidence,
                    "apply_time": row.apply_time.strftime("%Y-%m-%d %H:%M:%S") if row.apply_time else None,
                    "reviewer": row.reviewer,
                    "withdraw_status": row.withdraw_status,
                    "receipt": row.receipt
                } if row.withdraw_reason else None
            }
            processed_items.append(item)

        return {
            "list": processed_items,
            "total": total
        }

    except HTTPException:
        raise
    except Exception as e:
        return HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# type: 请求类型参数，可选 0 - CRM权限 1 - 消息权限 2 - 其他权限（预留）
class PostType(BaseModel):
    type: Optional[int] = 0

@router.post("/api/leads_xiaoshou", summary="获取销售组与销售")
def post_leads_xiaoshou(data: PostType, request: Request, db: Session = Depends(get_db)):
    """
    根据用户权限获取销售组与销售人员信息
    
    参数说明：
    - type=0: 根据CRM权限字段控制 (check_all_crm, check_group_crm, check_person_crm)
    - type=1: 根据消息权限字段控制 (check_all_messages, check_member_messages, check_mine_messages)  
    - type=2: 预留给其他权限字段
    
    权限级别（优先级从高到低）：
    1. check_all_* == 0: 查看全部数据（返回所有销售部用户）
    2. check_group_*/check_member_* == 0: 查看本组数据（返回同组用户）
    3. check_person_*/check_mine_* == 0: 查看个人数据（返回空数组）
    """
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="用户未登录")

        # 查询用户权限
        existing_frontend = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.department == user.department,
            UserPermissionFrontend.identity == user.identity
        ).first()
        
        if not existing_frontend:
            return []

        # 根据type选择权限字段组
        if data.type == 0:
            # CRM权限字段
            check_all_field = existing_frontend.check_all_crm
            check_group_field = existing_frontend.check_group_crm  
            check_person_field = existing_frontend.check_person_crm
        elif data.type == 1:
            # 消息权限字段
            check_all_field = existing_frontend.check_all_messages
            check_group_field = existing_frontend.check_member_messages
            check_person_field = existing_frontend.check_mine_messages
        elif data.type == 2:
            # 预留给其他权限字段，暂时返回空数组
            return []
        else:
            # 无效的type值
            return []

        # 权限判断逻辑（按优先级）
        result = []
        
        # 1. 个人权限：只能看个人数据
        if check_person_field == 0 and user.identity != "超管":
            result = []
        # 2. 组权限：查看本组数据  
        elif check_group_field == 0  and  user.identity != "超管":
            # 查询同组用户
            group_users = db.query(User).filter(
                User.department == "销售部",
                User.group_name == user.group_name
            ).order_by(User.name).all()
            
            if group_users:
                children = []
                for user_item in group_users:
                    children.append({
                        "value": user_item.account,
                        "label": user_item.name
                    })
                
                group_name = user.group_name or "未分组"
                result.append({
                    "value": group_name,
                    "label": group_name,
                    "children": children
                })
        # 3. 全部权限：查看全部数据
        elif check_all_field == 0 or user.identity == "超管":
            # 查询所有销售部用户，按组分组
            sales_users = db.query(User).filter(
                User.department == "销售部"
            ).order_by(User.group_name, User.name).all()
            
            # 按组分组
            groups = {}
            for user_item in sales_users:
                group_name = user_item.group_name or "未分组"
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append({
                    "value": user_item.account,
                    "label": user_item.name
                })
            
            # 构建返回格式
            for group_name, members in groups.items():
                result.append({
                    "value": group_name,
                    "label": group_name,
                    "children": members
                })
            return result
        # 4. 无权限
        else:
            result = []
            
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        return HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
class PostFdvalue(BaseModel):
    ID: str
    name: str
    type: Optional[int] = 0  # 字段类型，默认为0（客情历史记录类型）

@router.post("/api/sala_crm/fdvalue", summary="获取表字段的值")
def post_fdvalue(data: PostFdvalue, request: Request, db: Session = Depends(get_db)):
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            return HTTPException(status_code=401, detail="用户未登录")

        # 参数验证
        if not data.ID or not data.name:
            return HTTPException(status_code=400, detail="参数不能为空")
    
        # 查询线索记录是否存在
        clue_sheet_data = db.query(ClueSheet).filter(ClueSheet.ID == data.ID).first()
        if not clue_sheet_data:
            return HTTPException(status_code=404, detail=f"ID为{data.ID}的线索记录不存在")

        group_users_name = [user.name for user in db.query(User).filter(User.department == "销售部",User.group_name == user.group_name,User.identity == "组长").all()]
        # 权限验证：只有对接人或超管可以查看
        if not (clue_sheet_data.contact_person == user.name or user.identity == "超管" or user.name in group_users_name):
            return HTTPException(status_code=403, detail="权限不足")

        # 查询 SalaCrm 记录
        sala_crm_history_data = db.query(SalaCrmHistory).filter(SalaCrmHistory.ID == data.ID).first()
        fieldtype = 1
        if data.name == "customer_history_record":
            fieldname = "customer_history_record"
            fieldvalue = sala_crm_history_data.customer_history_record
            fieldtype = 0
        if data.name == "self_reminder_history_flag":
            fieldname = "self_reminder_history_flag"
            fieldvalue = sala_crm_history_data.self_reminder_history_flag
            fieldtype = 0
        if data.name == "s_demand_feature_history":
            fieldname = "s_demand_feature_history"
            fieldvalue = sala_crm_history_data.s_demand_feature_history
            fieldtype = 0

        if data.name == "a_demand_feature_history":
            fieldname = "a_demand_feature_history"
            fieldvalue = sala_crm_history_data.a_demand_feature_history
            fieldtype = 0
        if data.name == "b_demand_feature_history":
            fieldname = "b_demand_feature_history"
            fieldvalue = sala_crm_history_data.b_demand_feature_history
            fieldtype = 0
        if data.name == "c_demand_feature_history":
            fieldname = "c_demand_feature_history"
            fieldvalue = sala_crm_history_data.c_demand_feature_history
            fieldtype = 0
        if data.name == "s_zu_demand_feature_history":
            fieldname = "s_zu_demand_feature_history"
            fieldvalue = sala_crm_history_data.s_zu_demand_feature_history
            fieldtype = 0
        if data.name == "a_zu_demand_feature_history":
            fieldname = "a_zu_demand_feature_history"
            fieldvalue = sala_crm_history_data.a_zu_demand_feature_history
            fieldtype = 0
        if data.name == "b_zu_demand_feature_history":
            fieldname = "b_zu_demand_feature_history"
            fieldvalue = sala_crm_history_data.b_zu_demand_feature_history
            fieldtype = 0
        if data.name == "c_zu_demand_feature_history":
            fieldname = "c_zu_demand_feature_history"
            fieldvalue = sala_crm_history_data.c_zu_demand_feature_history
            fieldtype = 0

        # 特殊处理：当字段名为 "customer_record" 且 type=0 时，返回客情历史记录
        if fieldtype == 0:
            # 如果 SalaCrm 记录不存在或 customer_history_record 为空，返回空数组
            if not fieldvalue:
                return []

            try:
                # 解析 JSON 格式的历史记录
                history_data = json.loads(fieldvalue)

                # 将 JSON 对象转换为数组格式，按索引顺序排列
                result = []
                # 获取所有数字键并排序
                numeric_keys = sorted([int(k) for k in history_data.keys() if k.isdigit()])

                for key in numeric_keys:
                    if str(key) in history_data:
                        result.append(history_data[str(key)])

                return result

            except (json.JSONDecodeError, TypeError, ValueError):
                # JSON 解析失败时返回空数组
                return []

        # 其他字段的处理
        else:
            # 如果 SalaCrm 记录不存在，返回 null 值
            if not sala_crm_history_data:
                return {"value": None}

            # 获取字段值
            field_value = getattr(sala_crm_history_data, data.name, None)

            # 处理特殊数据类型
            if field_value is not None:
                # 处理 datetime 类型
                if hasattr(field_value, 'strftime'):
                    field_value = field_value.strftime("%Y-%m-%d %H:%M:%S")
                # 处理其他类型保持原样

            return {"value": field_value}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    

@router.get("/api/sala_crm/{lead_id}",summary="获取单个客情记录")
def get_lead(lead_id: str, request: Request, db: Session = Depends(get_db)):
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            return HTTPException(status_code=400, detail="用户未登录")

        # 查询客情记录
        lead = db.query(SalaCrm).filter(SalaCrm.ID == lead_id).first()

        # 检查记录是否存在
        if not lead:
            return HTTPException(status_code=400, detail=f"ID为{lead_id}的客情记录不存在")

        # 权限验证：检查用户是否有权限查看该记录
        # 通过关联的ClueSheet表检查contact_person
        clue_sheet = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).first()
        if clue_sheet and not (clue_sheet.contact_person == user.name or user.identity == "超管"):
            return HTTPException(status_code=400, detail="权限不足，无法查看该客情记录")

        return lead

    except HTTPException:
        raise
    except Exception as e:
        return HTTPException(status_code=500, detail=f"获取客情记录失败: {str(e)}")


@router.get("/api/sala_crm/history/{lead_id}",summary="获取单个客情历史记录")
def get_sala_crm_history(lead_id: str, request: Request, db: Session = Depends(get_db)):
    try:
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=400, detail="用户未登录")

        # 查询客情记录
        historydata = db.query(SalaCrmHistory).filter(SalaCrm.ID == lead_id).first()

        # 检查记录是否存在
        if not historydata:
            return HTTPException(status_code=400, detail=f"ID为{lead_id}的客情记录不存在")

        # 权限验证：检查用户是否有权限查看该记录
        # 通过关联的ClueSheet表检查contact_person
        clue_sheet = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).first()
        if clue_sheet and not (clue_sheet.contact_person == user.name or user.identity == "超管"):
            return HTTPException(status_code=400, detail="权限不足，无法查看该客情记录")

        return historydata

    except HTTPException:
        raise
    except Exception as e:
        return HTTPException(status_code=500, detail=f"获取客情记录失败: {str(e)}")