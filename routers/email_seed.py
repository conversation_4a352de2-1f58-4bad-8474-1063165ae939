from fastapi import FastAPI, BackgroundTasks
from pydantic import BaseModel, EmailStr
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig, MessageType
from dotenv import load_dotenv
import os

# 加载 .env 文件中的环境变量
load_dotenv()

# 配置 FastAPI-Mail
conf = ConnectionConfig(
    MAIL_USERNAME="<EMAIL>",
    MAIL_PASSWORD="ridxkhaomdcvijbb",
    MAIL_PORT=int(465),
    MAIL_SERVER="smtp.qq.com",
    MAIL_SSL_TLS=True,  # 使用 SSL/TLS 加密
    MAIL_STARTTLS=False,
    USE_CREDENTIALS=True,
    VALIDATE_CERTS=True
)

app = FastAPI()

# 请求体模型
class EmailSchema(BaseModel):
    email: EmailStr
    subject: str
    body: str

# 发送邮件的路由
@app.post("/send-email")
async def send_email(email_data: EmailSchema, background_tasks: BackgroundTasks):
    # 构建邮件内容
    message = MessageSchema(
        subject=email_data.subject,
        recipients=[email_data.email],
        body=email_data.body,
        subtype=MessageType.html  # 如果发送纯文本，改为 MessageType.plain
    )

    # 使用后台任务异步发送邮件
    fm = FastMail(conf)
    background_tasks.add_task(fm.send_message, message)
    return {"message": "邮件正在后台发送"}

# 启动应用
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)