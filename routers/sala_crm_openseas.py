from typing import Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session, joinedload
from database import get_db
from models import ClueSheet, User, SalaCrmOpenseas, SalaCrmHistoryOpenseas, ClueWithdraw, UserPermissionFrontend, DistributionQueue, OrderBillNumber, SalaCrm, SalaCrmHistory
from routers.sala_crm import add_history_data
from datetime import datetime, timedelta
from pydantic import BaseModel
from auth import get_current_user, base64_decrypt_data
import json
import logging

logger = logging.getLogger(__name__)


# 更新线索
class UpSalaCrmOpenseas(BaseModel):
    class Config:
        # 只包含实际设置的字段，忽略未设置的字段
        exclude_unset = True

    ID: Optional[str] = None
    distribute_num: Optional[int] = None
    contact_person_openseas: Optional[str] = None
    wechat_name: Optional[str] = None
    is_add: Optional[int] = None
    add_date: Optional[str] = None
    reason_failure: Optional[str] = None
    customer_name: Optional[int] = None
    is_billed: Optional[int] = None
    bill_number: Optional[str] = None
    customer_record: Optional[str] = None
    customer_record_images: Optional[str] = None
    keyword_tips: Optional[str] = None
    self_reminder_flag: Optional[str] = None
    clue_basic_tag: Optional[str] = None
    clue_stage: Optional[str] = None
    last_followup_time: Optional[datetime] = None
    last_followup_record: Optional[str] = None
    failure_analysis: Optional[str] = None
    is_read: Optional[int] = None
    chat_image: Optional[str] = None

    # 需求特征字段
    s_demand_feature: Optional[str] = None
    a_demand_feature: Optional[str] = None
    b_demand_feature: Optional[str] = None
    c_demand_feature: Optional[str] = None
    s_zu_demand_feature: Optional[str] = None
    a_zu_demand_feature: Optional[str] = None
    b_zu_demand_feature: Optional[str] = None
    c_zu_demand_feature: Optional[str] = None
    zu_customer_name: Optional[int] = None
    #ClueSheet
    kefu_image: Optional[str] = None
   
router = APIRouter()

# 公海列表
class PostSalaCrmOpenseas(BaseModel):
    page: Optional[int] = 1
    pageSize: Optional[int] = 20
    allocation_date: Optional[str] = None
    contact_person_openseas: Optional[str] = None
    keyword: Optional[str] = None
    filters: Optional[List] = None
    sortField: Optional[str] = "allocation_date"
    sortOrder: Optional[str] = "desc"

@router.post("/api/sala_crm_openseas", summary="公海记录查询")
def post_sala_crm_openseas_list(data: PostSalaCrmOpenseas, request: Request, db: Session = Depends(get_db)):
    try:
       
        # 用户权限验证
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="用户未登录")
        # 分页计算
        skip = (data.page - 1) * data.pageSize
        filters = []

        # 分配日期过滤
        if data.allocation_date is not None:
            filters.append(func.DATE(SalaCrmOpenseas.allocation_date) == data.allocation_date)

        # 基础过滤条件
        filters.append(ClueSheet.is_deleted == 0)

        # 关键字搜索
        if data.keyword:
            search_term = f"%{data.keyword}%"
            filters.append(
                or_(
                    SalaCrmOpenseas.ID.like(search_term),
                    SalaCrmOpenseas.SN.like(search_term),
                    SalaCrmOpenseas.wechat_name.like(search_term),
                    SalaCrmOpenseas.reason_failure.like(search_term),
                    SalaCrmOpenseas.customer_name.like(search_term),
                    SalaCrmOpenseas.bill_number.like(search_term),
                    SalaCrmOpenseas.keyword_tips.like(search_term),
                    SalaCrmOpenseas.clue_basic_tag.like(search_term),
                    SalaCrmOpenseas.failure_analysis.like(search_term)
                )
            )
        
        Field_in_all = ["clue_basic_tag", "clue_stage"]

        if data.filters:
            for filter_item in data.filters:
                for key, value in filter_item.items():

                    if key in Field_in_all and value:
                        if hasattr(ClueSheet, key):
                            condition = getattr(ClueSheet, key).in_(value)
                            filters.append(condition)
                        elif hasattr(SalaCrmOpenseas, key):
                            condition = getattr(SalaCrmOpenseas, key).in_(value)
                            filters.append(condition)

                    elif key == "allocation_date" and value:
                        # 日期范围过滤
                        start = datetime.strptime(value["start"], "%Y-%m-%d")
                        end = datetime.strptime(value["end"], "%Y-%m-%d") + timedelta(days=1)
                        filters.append(SalaCrmOpenseas.allocation_date >= start)
                        filters.append(SalaCrmOpenseas.allocation_date < end)
                    elif value:
                        if hasattr(ClueSheet, key):
                            condition = getattr(ClueSheet, key) == value
                            filters.append(condition)
                        elif hasattr(SalaCrmOpenseas, key):
                            condition = getattr(SalaCrmOpenseas, key) == value
                            filters.append(condition)

        # 线索查看权限（全部/组内/个人）
        existing_frontend = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.department == user.department,
            UserPermissionFrontend.identity == user.identity
        ).first()
     
        if existing_frontend and existing_frontend.lead_crm==0 and user.identity!="超管":
            # 个人权限
            if existing_frontend.check_person_crm == 0 and user.department in {"电商部", "新媒体部"}:
                filters.append(ClueSheet.registrar == user.name)
            if existing_frontend.check_person_crm == 0 and user.department == "销售部" :
                filters.append(SalaCrmOpenseas.contact_person_openseas == user.name)
            # 组内权限
            if existing_frontend.check_group_crm ==0 and user.department in {"电商部", "新媒体部"}:
                filters.append(ClueSheet.registrar == user.name)
            if existing_frontend.check_group_crm ==0 and user.department in {"销售部"}:
                user_name_arr = [user_name for user_name, in
                                 db.query(User.name).filter(User.group_name == user.group_name).all()]
                filters.append(SalaCrmOpenseas.contact_person_openseas.in_(user_name_arr))

        # 设置分页参数
        data.pageSize = 20
        skip = (data.page - 1) * data.pageSize

        # 构建查询 - 以 SalaCrmOpenseas 为主表进行左外连接
        query = db.query(
            # SalaCrmOpenseas 表字段
            SalaCrmOpenseas.ID,
            SalaCrmOpenseas.SN,
            SalaCrmOpenseas.is_read,
            SalaCrmOpenseas.is_add,
            SalaCrmOpenseas.reason_failure,
            SalaCrmOpenseas.allocation_date,
            SalaCrmOpenseas.wechat_name,
            SalaCrmOpenseas.customer_name,
            SalaCrmOpenseas.contact_person_openseas,
            SalaCrmOpenseas.is_billed,
            SalaCrmOpenseas.bill_number,
            SalaCrmOpenseas.bill_riqi,
            SalaCrmOpenseas.bill_use_time,
            SalaCrmOpenseas.last_followup_time,
            SalaCrmOpenseas.clue_stage,
            SalaCrmOpenseas.clue_basic_tag,
            SalaCrmOpenseas.chat_image,
            SalaCrmOpenseas.customer_record,
            SalaCrmOpenseas.self_reminder_flag,
            SalaCrmOpenseas.s_demand_feature,
            SalaCrmOpenseas.a_demand_feature,
            SalaCrmOpenseas.b_demand_feature,
            SalaCrmOpenseas.c_demand_feature,
            SalaCrmOpenseas.s_zu_demand_feature,
            SalaCrmOpenseas.a_zu_demand_feature,
            SalaCrmOpenseas.b_zu_demand_feature,
            SalaCrmOpenseas.c_zu_demand_feature,
            SalaCrmOpenseas.zu_customer_name,
            SalaCrmOpenseas.failure_analysis,
            # ClueSheet 表字段 (可能为空)
            ClueSheet.distribute_num,
            ClueSheet.wechat_id,
            ClueSheet.phone_number,
            ClueSheet.channel,
            ClueSheet.store,
            ClueSheet.queue,
            ClueSheet.type,
            ClueSheet.status,
            ClueSheet.contact_person,
            ClueSheet.record_time,
            ClueSheet.remarks,
            ClueSheet.registrar,
            ClueSheet.kefu_image
        ).outerjoin(
            ClueSheet, SalaCrmOpenseas.ID == ClueSheet.ID
        )

        # 应用过滤条件
        if filters:
            query = query.filter(and_(*filters))

        # 排序处理
        query = query.order_by(SalaCrmOpenseas.allocation_date.desc(), SalaCrmOpenseas.SN.desc())

        # 执行查询
        total = query.count()
        results = query.offset(skip).limit(data.pageSize).all()

        # 处理结果数据 - 只返回查询中指定的字段
        processed_items = []
        for item in results:
            # 只处理查询中指定的字段
            item_dict = {
                #运营板块 公海分配日期；接收序号；原对接人；分发次数；现对接人；微信ID；店铺；客服截图；
                "ID": item.ID,
                "allocation_date": item.allocation_date.strftime("%Y-%m-%d %H:%M:%S") if item.allocation_date else None,
                "SN": item.SN,
                "contact_person": item.contact_person,
                "distribute_num": item.distribute_num,
                "contact_person_openseas": item.contact_person_openseas,
                "wechat_id": base64_decrypt_data(item.wechat_id) if item.wechat_id else None,
                "store": item.store,
                "chat_image": item.chat_image,
                #自动更新 开单编号；最新跟进时间；
                "bill_number": item.bill_number,
                "last_followup_time": item.last_followup_time.strftime("%Y-%m-%d %H:%M:%S") if item.last_followup_time else None,
                #销售板块：是否开单；添加状态；微信昵称；线索阶段；客情记录；再次跟进提醒；24小时内有无和客户语音/电话/视频；
                "is_billed": item.is_billed,
                "is_add": item.is_add,
                "wechat_name": item.wechat_name,
                "clue_stage": item.clue_stage,
                "customer_record": item.customer_record,
                "self_reminder_flag": item.self_reminder_flag,
                "customer_name": item.customer_name,
               #客户需求判断关键词记录(销售板块)：S级强需求（特征↓）；A级需求不明确（特征↓）；B级低需求（特征↓）；C级无需求（特征↓）；
                "s_demand_feature": item.s_demand_feature,
                "a_demand_feature": item.a_demand_feature,
                "b_demand_feature": item.b_demand_feature,
                "c_demand_feature": item.c_demand_feature,
                #客户需求判断关键词记录（组长板块）：S级强需求（特征↓）；A级需求不明确（特征↓）；B级低需求（特征↓）；C级无需求（特征↓）；24小时内有无和客户语音/电话/视频；未成交分析；上传聊天截图；                
                "s_zu_demand_feature": item.s_zu_demand_feature,
                "a_zu_demand_feature": item.a_zu_demand_feature,
                "b_zu_demand_feature": item.b_zu_demand_feature,
                "c_zu_demand_feature": item.c_zu_demand_feature,
                "zu_customer_name": item.zu_customer_name,
                "failure_analysis": item.failure_analysis,
                "kefu_image": item.kefu_image,
                #开单周期：开单时间；开单时长；
                "bill_riqi": item.bill_riqi.strftime("%Y-%m-%d %H:%M:%S") if item.bill_riqi else None,
                "bill_use_time": item.bill_use_time,
            }
            processed_items.append(item_dict)

        return {
            "list": processed_items,
            "total": total
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")



@router.put("/api/sala_crm_openseas/{lead_id}",summary="编辑公海记录")
def update_sala_crm_openseas(lead_id: str, data: UpSalaCrmOpenseas, request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return HTTPException(status_code=400, detail="用户未登录")

    # 获取实际传递的字段，忽略未设置的字段
    update_data = data.model_dump(exclude_unset=True)
    if not update_data:
        return HTTPException(status_code=400, detail="数据为空")
   
    SalaCrmHistoryOpenseasData = db.query(SalaCrmHistoryOpenseas).filter(SalaCrmHistoryOpenseas.ID == lead_id).first()
   
    if not SalaCrmHistoryOpenseasData:
        # 创建新数据
        SalaCrmHistoryOpenseasData = SalaCrmHistoryOpenseas(
            ID=lead_id
        )
        db.add(SalaCrmHistoryOpenseasData)
    
    ClueSheetData = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).order_by(ClueSheet.record_time.desc()).first()
    if not ClueSheetData:
        return HTTPException(status_code=400, detail=f"ID为{lead_id}的线索记录不存在")
    
    db_sala_crm_openseas = db.query(SalaCrmOpenseas).filter(SalaCrmOpenseas.ID == lead_id).order_by(SalaCrmOpenseas.last_followup_time.desc()).first()
    if not db_sala_crm_openseas:
        return HTTPException(status_code=400, detail=f"ID为{lead_id}的公海记录不存在")
    # 查询组长
    group_users_name = [user.name for user in db.query(User).filter(User.department == "销售部",User.group_name == user.group_name,User.identity == "组长").all()]
    if not (db_sala_crm_openseas.contact_person_openseas == user.name or user.identity == "超管" or user.name in group_users_name):
        return HTTPException(status_code=400, detail="权限不足")
    
    """更新线索记录"""
    try:
       
        if update_data.get("wechat_name"):
            wechat_name = update_data.get("wechat_name") 
            db_sala_crm_openseas.wechat_name =wechat_name
        if update_data.get("is_add"):
            is_add = update_data.get("is_add") 
            db_sala_crm_openseas.is_add = is_add
            if is_add == 1:
                db_sala_crm_openseas.add_date = datetime.now()
        if update_data.get("add_date"):
            add_date = update_data.get("add_date")
            db_sala_crm_openseas.add_date = add_date
        if update_data.get("reason_failure"):
            reason_failure = update_data.get("reason_failure")
            db_sala_crm_openseas.reason_failure = reason_failure
        if update_data.get("customer_name"):
            customer_name = update_data.get("customer_name")
            db_sala_crm_openseas.customer_name = customer_name
        if update_data.get("is_billed"):
            is_billed = update_data.get("is_billed")
            db_sala_crm_openseas.is_billed = is_billed
            if is_billed == 1:
                # 开单日期，开单用时
                db_sala_crm_openseas.bill_riqi = datetime.now()
                delta = db_sala_crm_openseas.bill_riqi - db_sala_crm_openseas.allocation_date
                total_days = round(delta.total_seconds() / (3600 * 24),1)
                db_sala_crm_openseas.bill_use_time = total_days

                # 将自动计算的字段添加到返回数据中
                update_data["bill_use_time"] = total_days
                update_data["bill_riqi"] = db_sala_crm_openseas.bill_riqi.strftime("%Y-%m-%d %H:%M:%S")

        bill_number = update_data.get("bill_number")
        if bill_number:
            db_sala_crm_openseas.bill_number = bill_number

        keyword_tips = update_data.get("keyword_tips")
        if keyword_tips:
            db_sala_crm_openseas.keyword_tips = keyword_tips
        clue_basic_tag = update_data.get("clue_basic_tag")
        if clue_basic_tag:
            db_sala_crm_openseas.clue_basic_tag = update_data.clue_basic_tag
        clue_stage = update_data.get("clue_stage")
        if clue_stage:
            FOLLOW_UP_STAGES = {"已报价-跟进中", "跟进-有意向"}
            if clue_stage in FOLLOW_UP_STAGES and db_sala_crm_openseas.clue_stage not in FOLLOW_UP_STAGES:
               db_sala_crm_openseas.clue_stage_follow_time = datetime.now()
            db_sala_crm_openseas.clue_stage = clue_stage
        last_followup_time = update_data.get("last_followup_time")
        if last_followup_time:
            db_sala_crm_openseas.last_followup_time = last_followup_time

        last_followup_record = update_data.get("last_followup_record")
        if last_followup_record:
            db_sala_crm_openseas.last_followup_record = last_followup_record

        if update_data.get("failure_analysis"):
            failure_analysis = update_data.get("failure_analysis")
            db_sala_crm_openseas.failure_analysis = failure_analysis
        if update_data.get("is_read"):
            is_read = update_data.get("is_read")
            db_sala_crm_openseas.is_read = is_read
        if update_data.get("chat_image"):
            chat_image = update_data.get("chat_image")
            db_sala_crm_openseas.chat_image = chat_image
        zu_customer_name = update_data.get("zu_customer_name")
        if zu_customer_name and isinstance(zu_customer_name, (int, str)):
            db_sala_crm_openseas.zu_customer_name = zu_customer_name
       
        # 处理客情记录和图片 - 只有当前端明确传递了这些字段时才更新
        customer_record_updated = False
        images_history = ""

        # 检查是否传递了customer_record字段
        if "customer_record" in update_data:
            customer_record = update_data.get("customer_record")
            if customer_record != db_sala_crm_openseas.customer_record:
                db_sala_crm_openseas.customer_record = customer_record
                customer_record_updated = True

        # 检查是否传递了customer_record_images字段
        if "customer_record_images" in update_data:
            customer_record_images = update_data.get("customer_record_images")
            images_history = customer_record_images if customer_record_images else ""
            if db_sala_crm_openseas.customer_record_images != customer_record_images:
                db_sala_crm_openseas.customer_record_images = customer_record_images
                customer_record_updated = True

        # 只有当客情记录或图片有更新时才保存历史记录
        if customer_record_updated:
            try:
                # 使用当前数据库中的值作为历史记录
                current_customer_record = db_sala_crm_openseas.customer_record or ""
                add_history_data('SalaCrmHistoryOpenseas',lead_id,current_customer_record,images_history,'customer_history_record', db)
            except Exception as e:
                print(f"保存客情历史记录失败: {e}")  # 不阻断主要业务逻辑
        self_reminder_flag = update_data.get("self_reminder_flag")
        if self_reminder_flag is not None and self_reminder_flag != db_sala_crm_openseas.self_reminder_flag:
            db_sala_crm_openseas.self_reminder_flag = self_reminder_flag
            try:
                add_history_data('SalaCrmHistoryOpenseas',lead_id,self_reminder_flag,images_history,'self_reminder_history_flag', db)
            except Exception as e:
                return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        s_demand_feature = update_data.get("s_demand_feature")
        if s_demand_feature:
            if s_demand_feature != db_sala_crm_openseas.s_demand_feature:
                db_sala_crm_openseas.s_demand_feature = s_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,s_demand_feature,images_history,'s_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        a_demand_feature = update_data.get("a_demand_feature")
        if a_demand_feature:
            if a_demand_feature != db_sala_crm_openseas.a_demand_feature:
                db_sala_crm_openseas.a_demand_feature = a_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,a_demand_feature,images_history,'a_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        b_demand_feature = update_data.get("b_demand_feature")
        if b_demand_feature:
            if b_demand_feature != db_sala_crm_openseas.b_demand_feature:
                db_sala_crm_openseas.b_demand_feature = b_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,b_demand_feature,images_history,'b_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        b_demand_feature = update_data.get("b_demand_feature")
        if b_demand_feature:
            if b_demand_feature != db_sala_crm_openseas.b_demand_feature:
                db_sala_crm_openseas.b_demand_feature = b_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,b_demand_feature,images_history,'b_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        c_demand_feature = update_data.get("c_demand_feature")
        if update_data.get("c_demand_feature") is not None:
            if c_demand_feature != db_sala_crm_openseas.c_demand_feature:
                db_sala_crm_openseas.c_demand_feature = c_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,c_demand_feature,images_history,'c_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        s_zu_demand_feature = update_data.get("s_zu_demand_feature")
        if s_zu_demand_feature:
            if s_zu_demand_feature != db_sala_crm_openseas.s_zu_demand_feature:
                db_sala_crm_openseas.s_zu_demand_feature = s_zu_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,s_zu_demand_feature,images_history,'s_zu_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        a_zu_demand_feature = update_data.get("a_zu_demand_feature")
        if a_zu_demand_feature:
            if a_zu_demand_feature != db_sala_crm_openseas.a_zu_demand_feature:
                db_sala_crm_openseas.a_zu_demand_feature = a_zu_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,a_zu_demand_feature,images_history,'a_zu_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        b_zu_demand_feature = update_data.get("b_zu_demand_feature")  
        if b_zu_demand_feature is not None:
            if b_zu_demand_feature != db_sala_crm_openseas.b_zu_demand_feature:
                db_sala_crm_openseas.b_zu_demand_feature = b_zu_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,b_zu_demand_feature,images_history,'b_zu_demand_feature_history', db) 
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")
        c_zu_demand_feature = update_data.get("c_zu_demand_feature")
        if c_zu_demand_feature:
            if c_zu_demand_feature != db_sala_crm_openseas.c_zu_demand_feature:
                db_sala_crm_openseas.c_zu_demand_feature = c_zu_demand_feature
                try:
                    add_history_data('SalaCrmHistoryOpenseas',lead_id,c_zu_demand_feature,images_history,'c_zu_demand_feature_history', db)
                except Exception as e:
                    return HTTPException(status_code=400, detail=f"保存历史记录失败: {e}")

        kefu_image = update_data.get("kefu_image")
        if kefu_image and ClueSheetData.kefu_image != kefu_image:#ClueSheet的客服截图
            ClueSheetData.kefu_image = kefu_image
            db.commit()
            db.refresh(ClueSheetData)
            return {"status_code": 1, "detail": "更新成功", "id": lead_id}
        # 更新到最新的最后跟进时间
        db_sala_crm_openseas.last_followup_time = datetime.now()
       
        db.commit()
        db.refresh(db_sala_crm_openseas)

        # 返回成功信息和实际更新的数据
        return {
            "status_code": 1,
            "detail": "更新成功",
            "id": lead_id,
            "updated_fields": update_data
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return HTTPException(status_code=400, detail=str(e))