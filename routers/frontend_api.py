from fastapi import APIRouter, HTTPException, Depends, Request, UploadFile, Form, File, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.sql import func
from database import SessionLocal, get_db
from models import User, UserPermissionFrontend, ClueSheet
from auth import get_current_user
from typing import List, Dict, Any, Optional
import os
import shutil
from datetime import datetime
from pathlib import Path

router = APIRouter()

# 获取用户信息
@router.get("/api/users/info")
async def get_users_info(request: Request, db: Session = Depends(get_db)):
    """
    获取所有用户的部门和分组信息，用于前端根据部门/分组筛选数据
    """
    try:
        # 获取当前用户（用于权限验证）
        current_user = get_current_user(request, db)
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        # 查询所有用户的基本信息
        users = db.query(User.name, User.department, User.group_name, User.identity).all()
        
        # 构建返回数据
        users_info = [
            {
                "name": user.name,
                "department": user.department,
                "group_name": user.group_name,
                "identity": user.identity 
            }
            for user in users
        ]
        
        return users_info
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户部门信息失败: {str(e)}")

# 新增API：按日期获取线索消息
@router.get("/api/leads/messages-by-date")
async def get_leads_messages_by_date(
    request: Request, 
    date: Optional[str] = Query(None, description="筛选日期，格式YYYY-MM-DD"),
    db: Session = Depends(get_db)
):
    """
    根据日期获取线索消息数据，减少前端处理负担
    """
    try:
        # 获取当前用户（用于权限验证）
        current_user = get_current_user(request, db)
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")

        # 基础查询：获取未删除且对接人不为空的线索
        query = db.query(ClueSheet).filter(
            ClueSheet.is_deleted == False,
            ClueSheet.contact_person != None,
            ClueSheet.contact_person != ""
        )
        
        # 如果提供了日期，添加日期筛选
        if date:
            query = query.filter(func.date(ClueSheet.record_date) == date)
        
        # 获取结果
        leads = query.all()
        
        # 处理结果，构建返回数据
        messages = []
        for lead in leads:
            # 转换线索为消息格式，根据前端需要的数据结构
            message = {
                "leadId": lead.ID,
                "wechatId": lead.wechat_id,
                "store": lead.store,
                "contact_person": lead.contact_person,
                "contact_person_department": lead.department,  # 根据实际字段调整
                "contact_person_group_name": lead.group_name,  # 根据实际字段调整
                "createdAt": lead.record_date + "T00:00:00Z",  # 转换为ISO格式
                "status": lead.status,
                "withdraw_status": lead.withdraw_status if hasattr(lead, 'withdraw_status') else None,
                "reviewer": lead.reviewer if hasattr(lead, 'reviewer') else None,
                "isRead": lead.is_read == 1 if hasattr(lead, 'is_read') else False,
                "channel": lead.channel if hasattr(lead, 'channel') else "",
                # 添加其他前端需要的字段
            }
            messages.append(message)
        
        print(f"日期 {date or '所有'} 的消息数量: {len(messages)}")
        return messages
        
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    except Exception as e:
        print(f"获取线索消息失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取线索消息失败: {str(e)}")

# 获取回收站权限
@router.get("/api/user/permissions/recycle-bin")
async def get_recycle_bin_permissions(request: Request, db: Session = Depends(get_db)):
    """
    获取当前用户对回收站的权限设置
    权限说明：
    - check_all_bin: 为0时可查看同部门所有数据
    - check_member_bin: 为0时可查看同部门同分组数据
    - check_mine_bin: 为0时只能查看自己的数据
    这三个权限互斥，只有一个能为0
    """
    try:
        current_user = get_current_user(request, db)
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        # 超级管理员拥有所有权限
        if current_user.is_admin == 0:
            return {
                "check_all_bin": 0,
                "check_member_bin": 1,
                "check_mine_bin": 1
            }
        
        # 根据用户身份查询权限表
        permission = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.department == current_user.department,
            UserPermissionFrontend.identity == current_user.identity
        ).first()
        
        if not permission:
            # 默认最严格权限
            return {
                "check_all_bin": 1,
                "check_member_bin": 1,
                "check_mine_bin": 0
            }
        
        # 检查是否有回收站权限
        if permission.recycle_bin != 0:
            # 无权访问回收站
            return {
                "check_all_bin": 1,
                "check_member_bin": 1,
                "check_mine_bin": 1
            }
        
        return {
            "check_all_bin": permission.check_all_bin,
            "check_member_bin": permission.check_member_bin,
            "check_mine_bin": permission.check_mine_bin
        }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取回收站权限失败: {str(e)}")

# 获取线索表权限
@router.get("/api/user/permissions/lead-table")
async def get_lead_table_permissions(request: Request, db: Session = Depends(get_db)):
    """
    获取当前用户对线索表的权限设置
    权限说明：
    - check_all_lead: 为0时可查看同部门所有数据
    - check_member_lead: 为0时可查看同部门同分组数据
    - check_mine_lead: 为0时只能查看自己的数据
    - edit_all_lead: 为0时可编辑同部门所有数据
    - edit_member_lead: 为0时可编辑同部门同分组数据
    - edit_mine_lead: 为0时只能编辑自己的数据
    - forbid_edit: 为0时禁止任何编辑
    - delete_all_lead: 为0时可删除同部门所有数据
    - delete_member_lead: 为0时可删除同部门同分组数据
    - delete_mine_lead: 为0时只能删除自己的数据
    - export_lead_table: 为0时可导出线索表
    这些权限在各自组内互斥，只有一个能为0
    """
    try:
        current_user = get_current_user(request, db)
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        # 超级管理员拥有所有权限
        if current_user.is_admin == 0:
            return {
                "check_all_lead": 0,
                "check_member_lead": 1,
                "check_mine_lead": 1,
                "edit_all_lead": 0,
                "edit_member_lead": 1,
                "edit_mine_lead": 1,
                "forbid_edit": 1,
                "delete_all_lead": 0,
                "delete_member_lead": 1,
                "delete_mine_lead": 1,
                "export_lead_table": 0,  
                "import_lead_table": 0,
                "user_info": {
                    "name": current_user.name,
                    "department": current_user.department,
                    "group_name": current_user.group_name
                }
            }
        
        # 根据用户身份查询权限表
        permission = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.department == current_user.department,
            UserPermissionFrontend.identity == current_user.identity
        ).first()
        
        if not permission:
            # 没有找到权限记录，返回默认权限（无权限）
            return {
                "check_all_lead": 1,
                "check_member_lead": 1,
                "check_mine_lead": 1,
                "edit_all_lead": 1,
                "edit_member_lead": 1,
                "edit_mine_lead": 1,
                "forbid_edit": 1,
                "delete_all_lead": 1,
                "delete_member_lead": 1,
                "delete_mine_lead": 1,
                "export_lead_table": 1,  # 默认无导出权限
                "import_lead_table": 1,  # 默认无导入权限
                "user_info": {
                    "name": current_user.name,
                    "department": current_user.department,
                    "group_name": current_user.group_name
                }
            }
        
        # 返回权限值
        return {
            "check_all_lead": permission.check_all_lead,
            "check_member_lead": permission.check_member_lead,
            "check_mine_lead": permission.check_mine_lead,
            "edit_all_lead": permission.edit_all_lead,
            "edit_member_lead": permission.edit_member_lead,
            "edit_mine_lead": permission.edit_mine_lead,
            "forbid_edit": permission.forbid_edit,
            "delete_all_lead": permission.delete_all_lead,
            "delete_member_lead": permission.delete_member_lead,
            "delete_mine_lead": permission.delete_mine_lead,
            "export_lead_table": permission.export_lead_table,
            "import_lead_table": permission.import_lead_table,
            "user_info": {
                "name": current_user.name,
                "department": current_user.department,
                "group_name": current_user.group_name
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取权限失败: {str(e)}")

# 获取前端通用权限配置
@router.get("/api/permissions/frontend")
async def get_frontend_permissions(request: Request, db: Session = Depends(get_db)):
    """
    获取前端通用权限配置，包括用户消息页和其他页面的访问控制
    返回所有前端权限配置项目
    """
    try:
        current_user = get_current_user(request, db)
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        print(f"开始获取前端权限配置，用户: {current_user.name}, 部门: {current_user.department}, 身份: {current_user.identity}")
        
        # 查询所有权限配置
        permissions_records = db.query(UserPermissionFrontend).all()
        print(f"找到 {len(permissions_records)} 条前端权限记录")
        
        # 超级管理员拥有所有权限
        if current_user.is_admin == 0:
            # 返回所有部门和身份的权限记录，方便前端展示
            frontend_permissions = []
            for perm in permissions_records:
                frontend_permissions.append({
                    "department": perm.department,
                    "identity": perm.identity,
                    "check_all_messages": int(perm.check_all_messages) if perm.check_all_messages is not None else 1,
                    "check_member_messages": int(perm.check_member_messages) if perm.check_member_messages is not None else 1,
                    "check_mine_messages": int(perm.check_mine_messages) if perm.check_mine_messages is not None else 1,
                })
                
            # 打印第一条记录作为示例
            if frontend_permissions:
                print(f"权限示例: {frontend_permissions[0]}")
                
            return frontend_permissions
        else:
            # 对于普通用户，查找符合其部门和身份的权限记录
            frontend_permissions = []
            
            # 记录用户权限
            for perm in permissions_records:
                permission_data = {
                    "department": perm.department,
                    "identity": perm.identity,
                    "check_all_messages": int(perm.check_all_messages) if perm.check_all_messages is not None else 1,
                    "check_member_messages": int(perm.check_member_messages) if perm.check_member_messages is not None else 1,
                    "check_mine_messages": int(perm.check_mine_messages) if perm.check_mine_messages is not None else 1,
                }
                
                frontend_permissions.append(permission_data)
                
                # 打印匹配的权限
                if perm.department == current_user.department and perm.identity == current_user.identity:
                    print(f"找到匹配权限: 部门={perm.department}, 身份={perm.identity}")
                    print(f"权限值: check_all_messages={perm.check_all_messages}({type(perm.check_all_messages)}), "
                          f"check_member_messages={perm.check_member_messages}({type(perm.check_member_messages)}), "
                          f"check_mine_messages={perm.check_mine_messages}({type(perm.check_mine_messages)})")
                    print(f"转换后权限值: {permission_data}")
            
            # 返回所有权限记录，前端会根据用户部门和身份进行筛选
            return frontend_permissions
    except Exception as e:
        print(f"获取前端权限失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取前端权限失败: {str(e)}")

# 添加文件上传端点 - 用于不同类型的媒体文件
@router.post("/api/upload/clue_media")
async def upload_clue_media(file: UploadFile = File(...), field_type: str = Form(...)):
    """上传线索相关的媒体文件（图片或音频）"""
    try:
        # 根据field_type决定上传目录
        directory_map = {
            "intent_chat": "intent_chat_screenshots",
            "deal_chat": "deal_chat_screenshots",
            "post_deal": "post_deal_communications",
            "upgrade_service": "upgrade_service_screenshots",
            "thanks": "thanks_screenshots",
            "clue_management": "clue_management_screenshots",
            "post_delivery_audio": "post_delivery_audios"
        }
        
        # 获取上传目录，如果没有指定类型，使用默认目录
        upload_dir_name = directory_map.get(field_type, "other_media")
        
        # 创建上传目录
        upload_dir = Path(f"static/uploads/{upload_dir_name}")
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        file_extension = os.path.splitext(file.filename)[1]
        new_filename = f"{timestamp}{file_extension}"
        
        # 保存文件
        file_path = upload_dir / new_filename
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 返回相对路径
        relative_path = f"uploads/{upload_dir_name}/{new_filename}"
        
        return {"file_path": relative_path}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}") 