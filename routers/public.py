import os
import random
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pathlib import Path
from datetime import datetime
import shutil
from typing import List, Union, Optional

router = APIRouter()

# 定义允许的图片文件类型
ALLOWED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
# 定义最大文件大小（10MB）
MAX_FILE_SIZE = 10 * 1024 * 1024
# 定义最大文件数量
MAX_FILE_COUNT = 10

def validate_image_file(file: UploadFile) -> None:
    """验证图片文件类型和大小"""
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")

    # 检查文件扩展名
    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in ALLOWED_IMAGE_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件类型。支持的格式: {', '.join(ALLOWED_IMAGE_EXTENSIONS)}"
        )

    # 检查文件大小
    if hasattr(file, 'size') and file.size and file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"文件大小超过限制，最大允许 {MAX_FILE_SIZE // (1024*1024)}MB"
        )

@router.post("/api/upload/withdraw_evidence", summary="上传撤回凭证文件")
async def upload_withdraw_evidence(
    files: Optional[List[UploadFile]] = File(None),
    file: Optional[UploadFile] = File(None)
):
    """上传撤回凭证文件 - 智能支持单文件和多文件上传

    支持两种调用方式：
    1. 单文件上传（向后兼容）：formData.append('file', file);
    2. 多文件上传：formData.append('files', file1); formData.append('files', file2);

    自动检测上传方式并处理，确保完全向后兼容
    """
    try:
        # 智能检测上传方式并统一处理
        upload_files = []

        if files and len(files) > 0:
            # 多文件上传方式
            upload_files = files
        elif file:
            # 单文件上传方式（向后兼容）
            upload_files = [file]
        else:
            raise HTTPException(status_code=400, detail="未检测到上传文件")

        # 检查文件数量限制
        if len(upload_files) > MAX_FILE_COUNT:
            raise HTTPException(
                status_code=400,
                detail=f"文件数量超过限制，最多允许上传 {MAX_FILE_COUNT} 个文件"
            )

        # 创建上传目录
        upload_dir = Path("static/uploads/withdraw_evidence")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # 生成时间戳（所有文件共用）
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

        uploaded_files = []
        file_paths = []
        filenames = []

        # 处理每个文件
        for index, upload_file in enumerate(upload_files):
            # 验证文件
            validate_image_file(upload_file)

            # 生成文件名
            file_extension = os.path.splitext(upload_file.filename)[1].lower()

            # 生成随机数后缀（1-100000），确保文件名唯一性
            random_suffix = random.randint(1, 100000)

            # 根据文件数量决定命名规则，添加随机数后缀避免文件名冲突
            if len(upload_files) == 1:
                # 单文件：withdraw_20250712143022_45678.jpg
                new_filename = f"withdraw_{timestamp}_{random_suffix}{file_extension}"
            else:
                # 多文件：withdraw_20250712143022_01_45678.jpg, withdraw_20250712143022_02_78901.jpg
                new_filename = f"withdraw_{timestamp}_{index+1:02d}_{random_suffix}{file_extension}"

            # 保存文件
            file_path = upload_dir / new_filename
            try:
                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(upload_file.file, buffer)
            except Exception as file_error:
                raise HTTPException(
                    status_code=500,
                    detail=f"保存文件 {upload_file.filename} 失败: {str(file_error)}"
                )

            # 记录文件信息
            relative_path = f"uploads/withdraw_evidence/{new_filename}"
            uploaded_files.append(relative_path)
            file_paths.append(relative_path)
            filenames.append(new_filename)

        # 返回结果 - 保持原有格式，完全向后兼容
        return {
            "success": True,
            "message": f"文件上传成功，共上传 {len(upload_files)} 个文件",
            "file_path": ",".join(file_paths),  # 逗号分隔的字符串（兼容原有格式）
            "filename": ",".join(filenames),    # 逗号分隔的字符串（兼容原有格式）
            "file_count": len(upload_files),    # 文件数量
            "files": uploaded_files             # 文件列表（便于前端处理多文件）
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")



@router.post("/api/public/upload_to_qiniu/")
async def upload_to_qiniu(file: UploadFile = File(...)):
    return {"filename": file.filename}
