import base64
import hashlib
from email.mime.multipart import MIMEMultipart
from hashlib import sha256
from email.header import Header
from fastapi import APIRouter, Depends, Query, HTTPException, Body, UploadFile, File, Form, Request, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
from database import get_db
from models import ClueSheet, User, UserPermissionFrontend, SalaCrm
from datetime import datetime,timedelta
from pydantic import BaseModel
import smtplib
from email.mime.text import MIMEText
from sqlalchemy import and_, func, or_
from auth import get_current_user,base64_encrypt_data,base64_decrypt_data
import os
import shutil
import json
from pathlib import Path
import tempfile
import pandas as pd
import json
import io


# 创建新线索
class LeadCreate(BaseModel):
    ID: Optional[str] = None
    record_date: Optional[str] = None
    channel: Optional[str] = None
    phone_number: Optional[str] = None
    wechat_id: str
    store: str
    registrar: Optional[str] = None
    anchor: Optional[str] = None
    shift: Optional[str] = None
    wangwang_id: Optional[str] = None
    remarks: Optional[str] = None
    contact_person: Optional[str] = None
    record_time: Optional[datetime] = None
    queue: Optional[str] = None
    type: Optional[str] = None
    AD_PLANT_ID: Optional[str] = None
    AD_IDEAR_ID: Optional[str] = None
    kefu_image: Optional[str] = None
    status: int = 1  # 1:已提交, 2:等待中, 3:已发送
    is_deleted: int = 0
    delete_time: Optional[datetime] = None
    delete_user: Optional[str] = None
    kefu_image: Optional[str] = None

# 更新线索
class LeadUpdate(BaseModel):
    ID: Optional[str] = None
    record_date: Optional[str] = None
    channel: Optional[str] = None
    wangwang_id: Optional[str] = None
    phone_number: Optional[str] = None
    wechat_id: Optional[str] = None
    store: Optional[str] = None
    registrar: Optional[str] = None
    contact_person: Optional[str] = None
    record_time: Optional[datetime] = None
    anchor: Optional[str] = None
    shift: Optional[str] = None
    remarks: Optional[str] = None
    status: Optional[int] = None  # 1:已提交, 2:等待中, 3:已发送
    queue: Optional[str] = None
    type: Optional[str] = None
    is_deleted: Optional[int] = None
    delete_time: Optional[datetime] = None
    delete_user: Optional[str] = None
    AD_PLANT_ID: Optional[str] = None
    AD_IDEAR_ID: Optional[str] = None
    kefu_image: Optional[str] = None

# Excel导入相关模型
class ExcelPreviewResponse(BaseModel):
    columns: List[str]
    data: List[List[Any]]
    total_rows: int

class ExcelImportConfig(BaseModel):
    start_row: int = 2  # 从第2行开始（跳过标题行）
    end_row: Optional[int] = None  # 结束行，None表示到文件末尾
    field_mapping: Dict[str, str] = {}  # Excel列名到数据库字段的映射
    strict_validation: bool = True  # 严格验证开关，默认开启

# 线索导入数据验证模型
class LeadImportData(BaseModel):
    record_date: str  # 记录日期 - 必填
    wechat_id: str   # 微信ID - 必填
    wechat_name: str # 微信昵称 - 必填
    store: str       # 店铺 - 必填
    registrar: str   # 登记人 - 必填
    contact_person: str  # 对接人 - 必填
    channel: Optional[str] = None
    phone_number: Optional[str] = None
    wangwang_id: Optional[str] = None
    anchor: Optional[str] = None
    shift: Optional[str] = None
    remarks: Optional[str] = None
    queue: Optional[str] = "0"  # 默认免费队列
    type: Optional[str] = "0"   # 默认类型

router = APIRouter()

def format_number_field(value):
    """
    格式化数字字段，去除浮点数的.0后缀
    """
    if pd.isna(value):
        return ""

    # 如果是浮点数且是整数值，转换为整数字符串
    if isinstance(value, float):
        if value.is_integer():
            return str(int(value))
        else:
            return str(value)

    # 如果是字符串，检查是否以.0结尾
    str_value = str(value).strip()
    if str_value.endswith('.0'):
        try:
            # 尝试转换为浮点数再转为整数
            float_val = float(str_value)
            if float_val.is_integer():
                return str(int(float_val))
        except ValueError:
            pass

    return str_value

@router.get("/api/leads",summary="日期查询，如2025/05/11")
def get_leads(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = "",
    department: Optional[str] = None,
    record_date: Optional[str] = None,
    request: Request = None,
    db: Session = Depends(get_db)
):
    
    filters = []
    # 分页，计算跳过数据量（作用不明）
    skip = (page - 1) * size
    filters.append(ClueSheet.is_deleted == 0)

    if search:
        #如果搜索词可能是电话或微信ID，尝试进行加密搜索
        search_term = f"%{search}%"
        encrypted_search = f"%{base64_encrypt_data(search)}%"
        filters.append(
            ClueSheet.ID.like(search_term) |
            ClueSheet.channel.like(search_term) |
            ClueSheet.record_date.like(search_term) |
            ClueSheet.wangwang_id.like(search_term) |
            ClueSheet.phone_number.like(encrypted_search) |
            ClueSheet.wechat_id.like(encrypted_search) |
            ClueSheet.store.like(search_term) |
            ClueSheet.registrar.like(search_term) |
            ClueSheet.contact_person.like(search_term) |
            ClueSheet.anchor.like(search_term) |
            ClueSheet.shift.like(search_term) |
            ClueSheet.remarks.like(search_term)
        )
    # 线索查看权限（全部/组内/个人）
    user = get_current_user(request, db)
    existing_frontend = db.query(UserPermissionFrontend).filter(
        UserPermissionFrontend.department == user.department,
        UserPermissionFrontend.identity == user.identity
    ).first()

    if existing_frontend.lead_table==0 and user.identity!="超管":
        # 个人
        if existing_frontend.check_mine_lead == 0 and user.department in {"电商部", "新媒体部"}:
            filters.append(ClueSheet.registrar == user.name)
        if existing_frontend.check_mine_lead == 0 and user.department == "销售部" :
            filters.append(ClueSheet.contact_person == user.name)
        # 组内
        if existing_frontend.check_member_lead ==0 and user.department in {"电商部", "新媒体部"}:
            # user_name_arr = [user_name for user_name, in
            #                  db.query(User.name).filter(User.group_name == user.group_name).all()]
            # filters.append(ClueSheet.registrar.in_(user_name_arr))
            filters.append(ClueSheet.registrar == user.name)
        if existing_frontend.check_member_lead ==0 and user.department in {"销售部"}:
            user_name_arr = [user_name for user_name, in
                             db.query(User.name).filter(User.group_name == user.group_name).all()]
            filters.append(ClueSheet.contact_person.in_(user_name_arr))

    # 前端做好了过滤，这两个都可以去掉
    # 如果指定了部门，过滤该部门的数据
    if department:
        filters.append(ClueSheet.channel == department)
    if record_date:
        # 过滤线索中登记人属于该部门的数据
        filters.append(func.date(ClueSheet.record_date) == record_date)

  
    total = db.query(ClueSheet).filter(and_(*filters)).count()
    items = db.query(ClueSheet).filter(and_(*filters)).order_by(ClueSheet.record_time.desc()).offset(skip).limit(size).all()
    # 解密查询结果中的敏感数据用于显示
    decrypted_items = []
    for item in items:
        # 解密
        if item.wechat_id:
            item.wechat_id = base64_decrypt_data(item.wechat_id)
        if item.phone_number:
            item.phone_number = base64_decrypt_data(item.phone_number)
        decrypted_items.append(item)

    return {
        "items": decrypted_items,
        "total": total
    }

@router.get("/api/leads/{lead_id}")
def get_lead(lead_id: str, db: Session = Depends(get_db)):
    """获取单个线索记录"""
    lead = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).first()
    if not lead:
        raise HTTPException(status_code=404, detail=f"ID为{lead_id}的线索记录不存在")
    
    # 解密敏感数据
    lead.decrypt_sensitive_data()
    
    return lead

def get_next_sequence_number(db: Session, date_prefix: str) -> str:
    # 查询当天最大的ID
    today_pattern = f"{date_prefix}%"
    max_id = db.query(func.max(ClueSheet.ID)).filter(ClueSheet.ID.like(today_pattern)).scalar()
    
    if max_id is None:
        return f"{date_prefix}000001"
    
    # 提取序列号部分并加1
    sequence_number = int(str(max_id)[-6:]) + 1
    return f"{date_prefix}{sequence_number:06d}"


@router.post("/api/leads")
async def update_or_delete_lead_legacy(lead: LeadUpdate, db: Session = Depends(get_db)):
   
    """处理旧版前端的更新或删除请求"""
    try:
        # 检查是否是更新或删除操作
        if hasattr(lead, 'operation') and lead.operation in ["update", "delete"]:
            # 检查记录是否存在
            db_lead = db.query(ClueSheet).filter(ClueSheet.ID == lead.ID).first()
            if not db_lead:
                raise HTTPException(status_code=404, detail=f"ID为{lead.ID}的线索记录不存在")

            # 根据operation字段决定是更新还是删除
            if lead.operation == "delete":
                # 删除记录
                db.delete(db_lead)
                db.commit()
                return {"success": True, "message": "线索删除成功", "id": lead.ID}
            elif lead.operation == "update":
                # 标记是否需要更新queue和type字段
                update_queue = False
                update_type = False
                
                # 更新记录
                if lead.channel is not None:
                    db_lead.channel = lead.channel
                if lead.wangwang_id is not None:
                    db_lead.wangwang_id = lead.wangwang_id
                if lead.phone_number is not None:
                    db_lead.phone_number = lead.phone_number
                if lead.wechat_id is not None:
                    db_lead.wechat_id = lead.wechat_id
                if lead.wechat_name is not None:
                    db_lead.wechat_name = lead.wechat_name
                if lead.store is not None:
                    db_lead.store = lead.store
                    update_queue = True  # 店铺变化，需要更新queue
                if lead.registrar is not None:
                    db_lead.registrar = lead.registrar
                if lead.contact_person is not None:
                    db_lead.contact_person = lead.contact_person
                    update_type = True  # 对接人变化，需要更新type
                if lead.record_time is not None:
                    db_lead.record_time = lead.record_time
                if lead.anchor is not None:
                    db_lead.anchor = lead.anchor
                if lead.shift is not None:
                    db_lead.shift = lead.shift
                if lead.remarks is not None:
                    db_lead.remarks = lead.remarks
                if lead.status is not None:
                    db_lead.status = lead.status
                if lead.is_read is not None:
                    db_lead.is_read = 1 if lead.is_read else 0
                if lead.is_add is not None:
                    db_lead.is_add = lead.is_add
                if lead.reason_failure is not None:
                    db_lead.reason_failure = lead.reason_failure
                if lead.kefu_image is not None:
                    db_lead.kefu_image = lead.kefu_image
                
                # 根据规则更新queue和type字段
                if update_queue:
                    db_lead.queue = "0" if "F" in db_lead.store else "1"
                
                if update_type:
                    db_lead.type = "1" if db_lead.contact_person else "0"

                db.commit()
                db.refresh(db_lead)
                return {"success": True, "message": "线索更新成功", "id": lead.ID}
        # 如果不是更新或删除操作，则创建新线索
        else:
            # # 检查记录是否存在
            # hash_wechat_id = base64_encrypt_data(lead.wechat_id) if lead.wechat_id else ""
            # five_day_nei_date = datetime.now().date() - timedelta(days=5)
            # data_lead = db.query(ClueSheet).filter(ClueSheet.wechat_id == hash_wechat_id, ClueSheet.store == lead.store, 
            #                                        ClueSheet.is_deleted == 0,ClueSheet.record_time >= five_day_nei_date,).order_by(ClueSheet.record_time.desc()).first()
            # if data_lead:
            #     data_lead.jiemi_wechat_id = base64_decrypt_data(data_lead.wechat_id)
            #     return {
            #         "duplicate": True,
            #         "message": f"5天内微信ID '{data_lead.jiemi_wechat_id}' 在店铺 '{data_lead.store}' 中已存在",
            #         "existing_lead": {
            #             "id": lead.ID,
            #             "record_date": data_lead.record_date,
            #             "registrar": data_lead.registrar,
            #             "record_time": data_lead.record_time,
            #             "wechat_id": data_lead.jiemi_wechat_id,
            #             "phone_number": data_lead.phone_number
            #         }
            #     }
            return await create_lead_internal(lead, db)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

async def create_lead_internal(lead: LeadCreate, db: Session):
    """内部函数，用于创建新线索"""
    try:
        # 生成当前日期前缀（格式：YYYYMMDD）
        current_date = datetime.now().strftime("%Y%m%d")
        # 生成新的ID
        new_id = get_next_sequence_number(db, current_date)
        
        # 如果没有提供record_time，使用当前时间
        record_time = lead.record_time if lead.record_time else datetime.now()
        
        # 确保wangwang_id不为空
        wangwang_id = lead.wangwang_id if lead.wangwang_id else ""
        
        # 根据规则设置queue和type字段
        # 检查店铺字段是否包含"F"
        queue_value = "0" if "F" in lead.store else "1"
        
        # 检查对接人字段是否为空
        contact_person = lead.contact_person if hasattr(lead, 'contact_person') and lead.contact_person else ""
        type_value = "1" if contact_person else "0"
        allocation_date = datetime.now() if lead.contact_person else None

        # 加密敏感数据
        hash_phone_number = base64_encrypt_data(lead.phone_number) if lead.phone_number else ""
        hash_wechat_id = base64_encrypt_data(lead.wechat_id) if lead.wechat_id else ""
        # 创建新数据
        db_sala_crm = SalaCrm(
            ID=new_id,
            allocation_date=allocation_date,
            last_followup_time=record_time,
            SN=0,
            AD_PLANT_ID=lead.AD_PLANT_ID,
            AD_IDEAR_ID=lead.AD_IDEAR_ID
        )
        #return db_sala_crm
        db.add(db_sala_crm)

        # 创建新的线索记录
        new_lead = ClueSheet(
            ID=new_id,
            record_date=lead.record_date,
            channel=lead.channel if lead.channel else "",
            wangwang_id=wangwang_id,
            phone_number=hash_phone_number,
            wechat_id=hash_wechat_id,
            store=lead.store,
            registrar=lead.registrar,
            contact_person=contact_person,
            record_time=record_time,
            anchor=lead.anchor,
            shift=lead.shift,
            remarks=lead.remarks if lead.remarks else "",
            status=lead.status if lead.status is not None else 1,
            queue=queue_value,
            type=type_value,
            kefu_image=lead.kefu_image,
        )
        db.add(new_lead)

        db.commit()
        db.refresh(new_lead)
        # 调用监测接口更新数据
        try:
            from .monitoring import monitor_data, get_monitoring_stats, manager
            import json

            # 准备监测数据
            monitor_payload = {
                "ID": new_id,
                "wechat_id": new_lead.wechat_id,
                "store": new_lead.store,
                "status": new_lead.status,
                "created_at": new_lead.record_time.isoformat() if new_lead.record_time else None
            }

            # 调用监测接口
            async def process_monitoring():
                await monitor_data(monitor_payload, db)
                # 获取并广播最新统计数据
                stats = get_monitoring_stats(db=db)
                await manager.broadcast(json.dumps(stats))

            await process_monitoring()
        except Exception as e:
            print(f"调用监测接口出错: {str(e)}")

        return {"success": True, "message": "线索创建成功", "id": new_id}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/leads", include_in_schema=False)
async def create_lead(lead: LeadCreate, db: Session = Depends(get_db)):
    """创建新线索记录"""
    return await create_lead_internal(lead, db)

@router.put("/api/leads/{lead_id}")
def update_lead(lead_id: str, lead: LeadUpdate,request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        raise HTTPException(status_code=401, detail="未登录或登录已过期")
     # 权限控制：根据用户部门和权限限制数据访问范围
    existing_frontend = db.query(UserPermissionFrontend).filter(
        UserPermissionFrontend.department == user.department,
        UserPermissionFrontend.identity == user.identity
    ).first()
    filters = []
    if not (existing_frontend.edit_all_lead == 0 or user.identity == "超管"):
        
        # 组内
        if existing_frontend.edit_member_lead ==0 and user.department in {"电商部", "新媒体部"}:
            # user_name_arr = [user_name for user_name, in
            #                  db.query(User.name).filter(User.group_name == user.group_name).all()]
            # filters.append(ClueSheet.registrar.in_(user_name_arr))
            filters.append(ClueSheet.registrar == user.name)
        if existing_frontend.edit_member_lead ==0 and user.department in {"销售部"}:
            user_name_arr = [user_name for user_name, in
                             db.query(User.name).filter(User.group_name == user.group_name).all()]
            filters.append(ClueSheet.contact_person.in_(user_name_arr))
        
        # 个人
        if existing_frontend.edit_mine_lead == 0 and user.department in {"电商部", "新媒体部"}:
            filters.append(ClueSheet.registrar == user.name)
        if existing_frontend.edit_mine_lead == 0 and user.department == "销售部" :
            filters.append(ClueSheet.contact_person == user.name)
        if existing_frontend.forbid_edit==0:
            forbid_edit_condition = or_(
                    ClueSheet.contact_person == user.name,  # 单人情况：直接匹配
                    ClueSheet.registrar == user.name  # 多人情况：逗号分隔字符串中包含
            )
            filters.append(forbid_edit_condition)
    
    
    """更新线索记录"""
    try:
        # 检查记录是否存在
        filters.append(ClueSheet.is_deleted == 0)
        filters.append(ClueSheet.ID == lead_id)
        db_lead = db.query(ClueSheet).filter(and_(*filters)).first()
        if not db_lead:
            return HTTPException(status_code=400, detail=f"ID为{lead_id}的线索记录不存在或无权限")
        # 更新记录
        if lead.record_date is not None:
            db_lead.record_date = lead.record_date
        if lead.channel is not None:
            db_lead.channel = lead.channel
        if lead.wangwang_id is not None:
            db_lead.wangwang_id = lead.wangwang_id

        if lead.phone_number is not None:
            # 更新前加密
            db_lead.phone_number = base64_encrypt_data(lead.phone_number)
        if lead.wechat_id is not None:
            # 更新前加密
            db_lead.wechat_id = base64_encrypt_data(lead.wechat_id)

        if lead.store is not None:
            db_lead.store = lead.store
        if lead.registrar is not None:
            db_lead.registrar = lead.registrar
        if lead.contact_person is not None:
            db_lead.contact_person = lead.contact_person
        if lead.record_time is not None:
            db_lead.record_time = lead.record_time
        if lead.anchor is not None:
            db_lead.anchor = lead.anchor
        if lead.shift is not None:
            db_lead.shift = lead.shift

        if lead.remarks is not None:
            db_lead.remarks = lead.remarks
        if lead.status is not None:
            db_lead.status = lead.status
        if lead.queue is not None:
            db_lead.queue = lead.queue
        if lead.type is not None:
            db_lead.type = lead.type


        db.commit()
        db.refresh(db_lead)
        return {"success": True, "message": "线索更新成功", "id": lead_id}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/api/leads/{lead_id}")
def delete_lead(lead_id: str,request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return HTTPException(status_code=401, detail="用户未登录")

    """将线索标记为已删除（移至回收站）"""
    lead = db.query(ClueSheet).filter(ClueSheet.ID == lead_id, ClueSheet.is_deleted == False).first()
    if not lead:
        raise HTTPException(status_code=404, detail="找不到该线索或线索已被删除")
    
    # 标记为已删除并记录删除时间
    lead.is_deleted = True
    lead.delete_time = datetime.now()
    lead.delete_user = user.name

    sala_crm_data = db.query(SalaCrm).filter(SalaCrm.ID == lead_id, SalaCrm.is_deleted == False).first()
    if sala_crm_data:
        sala_crm_data.is_deleted = True
    db.commit()
    return {"message": f"线索 {lead_id} 已移至回收站"}

@router.get("/api/leads/check-duplicate")
def check_duplicate_wechat_id(
    store: str = Query(..., description="店铺名称"),
    wechat_id: str = Query(..., description="微信ID"),
    exclude_id: Optional[str] = Query(None, description="排除的ID（用于更新时排除自身）"),
    db: Session = Depends(get_db)
):
    """检查特定店铺下是否存在重复的微信ID"""
    # 加密wechat_id用于查询
    hash_wechat_id = base64_encrypt_data(wechat_id)

    # 构建查询，同时查询明文和加密形式
    query = db.query(ClueSheet).filter(
        ClueSheet.store == store,
        ClueSheet.wechat_id == hash_wechat_id,
        ClueSheet.is_deleted == 0
    )
    
    # 如果提供了exclude_id，则排除该ID的记录
    if exclude_id:
        query = query.filter(ClueSheet.ID != exclude_id)
    
    # 查找是否存在匹配的记录
    existing_lead = query.first()

    if existing_lead:
        # 解密敏感数据
        existing_lead.decrypt_sensitive_data()
        
        return {
            "duplicate": True,
            "message": f"微信ID '{wechat_id}' 在店铺 '{store}' 中已存在",
            "existing_lead": {
                "id": existing_lead.ID,
                "record_date": existing_lead.record_date,
                "registrar": existing_lead.registrar,
                "record_time": existing_lead.record_time,
                "wechat_id": existing_lead.wechat_id,
                "phone_number": existing_lead.phone_number
            }
        }
    else:
        return {
            "duplicate": False,
            "message": "微信ID在该店铺中不存在重复"
        }

@router.post("/api/leads/check-duplicate")
def check_duplicate_wechat_id_post(
    data: dict = Body(..., description="包含店铺名称、微信ID和日期的数据"),
    db: Session = Depends(get_db)
):
    try:
        store = data.get("store", "")
        wechat_id = data.get("wechat_id", "")
        record_date = data.get("record_date", "")
        
        if not store or not wechat_id:
            return {"duplicate": False, "message": "缺少必要参数"}

        # 加密微信ID用于查询
            # 加密微信ID用于查询
        hash_wechat_id = base64_encrypt_data(wechat_id) if wechat_id else ""
        five_day_nei_date = datetime.now().date() - timedelta(days=5)
        # 基础查询条件
        query = db.query(ClueSheet).filter(
            ClueSheet.store == store,
            ClueSheet.wechat_id == hash_wechat_id,
            ClueSheet.record_time >= five_day_nei_date,
            ClueSheet.is_deleted == 0
        )

        five_days_ago = datetime.strptime(record_date, "%Y/%m/%d") -  timedelta(days=5) if record_date else datetime.now() - timedelta(days=5)
        #添加日期条件(5天)
        query = query.filter(ClueSheet.record_date >= five_days_ago)

        # 检查是否存在重复记录
        duplicate = query.first()
        ##return duplicate
        if duplicate:
            # 获取重复记录的详细信息
            return {
                "duplicate": True,
                "message": "该微信ID在当前店铺中已存在",
                "data": {
                    "ID": duplicate.ID,
                    "record_date": duplicate.record_date,
                    "store": duplicate.store,
                    "wechat_id": base64_decrypt_data(duplicate.wechat_id),
                    "registrar": duplicate.registrar
                }
            }
        return {"duplicate": False, "message": "未发现重复记录"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/leads/check-batch-duplicates")
def check_batch_duplicates(
    items: list = Body(..., description="要检查的数据列表，每项包含store和wechat_id"),
    db: Session = Depends(get_db)
):

    """批量检查微信ID是否在对应店铺中重复"""
    try:
        if not items or not isinstance(items, list):
            return {"success": False, "message": "无效的数据格式，期望一个列表"}
        
        results = []
        for index, item in enumerate(items):
            store = item.get("store", "")
            wechat_id = item.get("wechat_id", "")
            record_date = item.get("record_date", "")
            
            if not store or not wechat_id:
                results.append({
                    "index": index,
                    "duplicate": False,
                    "message": "缺少店铺名称或微信ID",
                    "item": item
                })
                continue
            
            # 加密微信ID用于查询
            hash_wechat_id = base64_encrypt_data(wechat_id) if wechat_id else ""
            five_day_nei_date = datetime.now().date() - timedelta(days=5)

            # 基础查询条件
            query = db.query(ClueSheet).filter(
                ClueSheet.store == store,
                ClueSheet.wechat_id == hash_wechat_id,
                ClueSheet.record_time >= five_day_nei_date,
                ClueSheet.is_deleted == False
            )
            
            # 如果提供了日期，则加入日期条件
            if record_date:
                query = query.filter(ClueSheet.record_date == record_date)
            
            # 查找是否存在匹配的记录
            existing_lead = query.first()


            if existing_lead:
                # 在结果中添加重复记录信息
                results.append({
                    "index": index,
                    "duplicate": True,
                    "message": f"微信ID '{wechat_id}' 在店铺 '{store}' 中已存在",
                    "item": item,
                    "existing_lead": {
                        "id": existing_lead.ID,
                        "record_date": existing_lead.record_date,
                        "store": existing_lead.store,
                        "wechat_id": base64_decrypt_data(existing_lead.wechat_id),
                        "registrar": existing_lead.registrar
                    }
                })
            else:
                # 在结果中添加非重复记录信息
                results.append({
                    "index": index,
                    "duplicate": False,
                    "message": "微信ID在该店铺中不存在重复",
                    "item": item
                })
        return {
            "success": True,
            "total": len(items),
            "has_duplicates": sum(1 for r in results if r.get("duplicate", False)),
            "results": results
        }
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        return {"success": False, "message": f"检查批量重复时出错: {str(e)}", "details": error_details}

@router.post("/api/leads/batch")
def create_batch_leads(
    leads: list = Body(..., description="要批量提交的线索数据列表"),
    db: Session = Depends(get_db)
):
    """批量创建线索"""
    try:
        if not leads or not isinstance(leads, list):
            raise HTTPException(status_code=400, detail="无效的数据格式，期望一个列表")
        
        success_count = 0
        error_count = 0
        results = []

        for lead_data in leads:
            try:
                # 生成当前日期前缀（格式：YYYYMMDD）
                current_date = datetime.now().strftime("%Y%m%d")
                # 生成新的ID
                new_id = get_next_sequence_number(db, current_date)



                # 确保电话号码和微信ID不为空
                phone_number = lead_data.get("phone_number", "")
                wechat_id = lead_data.get("wechat_id", "")
                wechat_name = lead_data.get("wechat_name", "")
                store = lead_data.get("store", "")

                # 检查必填字段
                if not wechat_id or not store:
                    error_count += 1
                    results.append({
                        "success": False,
                        "message": "缺少必填字段 (phone_number, wechat_id, store)",
                        "data": lead_data
                    })
                    continue
                
                # 根据规则设置queue和type字段
                queue_value = "0" if "F" in store else "1"
                contact_person = lead_data.get("contact_person", "")
                type_value = "1" if contact_person else "0"
                
                # 加密敏感数据
                hash_phone_number = base64_encrypt_data(phone_number) if phone_number else ""
                hash_wechat_id = base64_encrypt_data(wechat_id) if wechat_id else ""

                # 创建新的线索记录
                record_time = datetime.fromisoformat(lead_data.get("record_time") .replace("Z", "+00:00")).strftime('%Y-%m-%d %H:%M:%S') if lead_data.get("record_time") else datetime.now()
                allocation_date =  datetime.now() if contact_person else None

                new_id = str(int(new_id) + success_count)

                # 创建新数据
                db_sala_crm = SalaCrm(
                    ID=new_id,
                    allocation_date=allocation_date,
                    last_followup_time=record_time,
                    SN=0,
                    AD_PLANT_ID=lead_data.get("AD_PLANT_ID", ""),
                    AD_IDEAR_ID=lead_data.get("AD_IDEAR_ID", "")
                )
                db.add(db_sala_crm)

                #return record_time
                new_lead = ClueSheet(
                    ID=new_id,
                    record_date=lead_data.get("record_date", datetime.now().strftime("%Y-%m-%d")),
                    channel=lead_data.get("channel", ""),
                    wangwang_id=lead_data.get("wangwang_id", ""),
                    phone_number=hash_phone_number,
                    wechat_id=hash_wechat_id,
                    store=store,
                    registrar=lead_data.get("registrar", ""),
                    contact_person=contact_person,
                    record_time=record_time,
                    anchor=lead_data.get("anchor", ""),
                    shift=lead_data.get("shift", ""),
                    remarks=lead_data.get("remarks", ""),
                    status=1,  # 默认状态为已提交
                    queue=queue_value,
                    type=type_value,
                    kefu_image=lead_data.get("kefu_image", ""),

                )
                db.add(new_lead)

                # 添加成功结果
                success_count += 1
                results.append({
                    "success": True,
                    "message": "线索创建成功",
                    "id": new_id,
                    "data": lead_data
                })
            except Exception as e:
                error_count += 1
                results.append({
                    "success": False,
                    "message": f"创建线索时出错: {str(e)}",
                    "data": lead_data
                })
        
        # 提交事务
        db.commit()
        
        return {
            "success": True,
            "total": len(leads),
            "success_count": success_count,
            "error_count": error_count,
            "results": results
        }
    except Exception as e:
        db.rollback()
        import traceback
        error_details = traceback.format_exc()
        raise HTTPException(status_code=500, detail=f"批量创建线索时出错: {str(e)}, 详情: {error_details}")

@router.put("/api/leads/{lead_id}/status")
def update_lead_status(
    lead_id: str,
    status: int = Body(..., embed=True),
    db: Session = Depends(get_db)
):
    """更新线索的状态"""
    try:
        # 查找线索
        lead = db.query(ClueSheet).filter(ClueSheet.ID == lead_id, ClueSheet.is_deleted == False).first()
        
        if not lead:
            raise HTTPException(status_code=404, detail=f"未找到ID为 {lead_id} 的线索")
        
        # 更新状态
        lead.status = status
        db.commit()
        
        return {"success": True, "message": f"线索 {lead_id} 的状态已更新为 {status}"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新线索状态失败: {str(e)}")



# 添加文件上传端点 - 按线索ID存储文件
@router.post("/api/upload/lead_file")
async def upload_lead_file(
    file: UploadFile = File(...),
    lead_id: str = Form(...),
    field_type: str = Form(...),
    db: Session = Depends(get_db)
):
    """上传线索相关的媒体文件（图片或音频），按线索ID存储"""
    try:
        # 检查线索是否存在
        lead = db.query(ClueSheet).filter(ClueSheet.ID == lead_id).first()
        if not lead:
            raise HTTPException(status_code=404, detail=f"找不到ID为 {lead_id} 的线索")
        
        # 创建以线索ID命名的目录
        lead_dir = Path(f"static/uploads/{lead_id}")
        lead_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        original_filename = file.filename
        file_extension = os.path.splitext(original_filename)[1]
        new_filename = f"{field_type}_{timestamp}{file_extension}"
        
        # 保存文件
        file_path = lead_dir / new_filename
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 返回相对路径
        relative_path = f"uploads/{lead_id}/{new_filename}"
        
        return {"file_path": relative_path}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


# 线索数据上传Excel文件并预览
@router.post("/api/leads/preview_import_xiansuo")
async def preview_import_xiansuo_data(file: UploadFile = File(...)):
    """
    上传Excel文件并预览线索数据
    返回前10行数据用于预览和字段映射确认
    """
    # 检查文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持.xlsx或.xls格式的Excel文件")

    # 创建临时文件保存上传的Excel
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    try:
        # 写入临时文件
        content = await file.read()
        temp_file.write(content)
        temp_file.close()

        # 使用pandas读取Excel文件
        df = pd.read_excel(temp_file.name)

        # 检查是否有数据
        if df.empty:
            raise HTTPException(status_code=400, detail="Excel文件中没有数据")

        # 获取前10行数据用于预览
        preview_rows = min(10, len(df))
        preview_data = df.head(preview_rows).values.tolist()

        # 处理特殊浮点值（NaN, Infinity等）和数字格式化
        processed_data = []
        for row in preview_data:
            processed_row = []
            for col_index, value in enumerate(row):
                col_name = df.columns[col_index] if col_index < len(df.columns) else f"Column_{col_index}"

                # 处理各种数据类型
                if pd.isna(value):
                    # 处理所有类型的NaN值
                    processed_row.append(None)
                elif isinstance(value, float):
                    if value == float('inf') or value == float('-inf'):
                        processed_row.append(None)  # 将Infinity转换为None
                    else:
                        # 对于电话号码和微信ID字段，使用格式化函数去除.0
                        if col_name in ["电话号码", "微信ID"]:
                            processed_row.append(format_number_field(value))
                        else:
                            processed_row.append(value)
                elif isinstance(value, (int, str, bool)):
                    # 对于微信ID字段也需要格式化
                    if col_name in ["微信ID"]:
                        processed_row.append(format_number_field(value))
                    else:
                        # 处理字符串中的换行符
                        if isinstance(value, str):
                            processed_row.append(value.strip())
                        else:
                            processed_row.append(value)
                elif hasattr(value, 'isoformat'):
                    # 处理日期时间类型
                    processed_row.append(value.isoformat())
                else:
                    # 其他类型转换为字符串
                    try:
                        processed_row.append(str(value))
                    except:
                        processed_row.append(None)
            processed_data.append(processed_row)

        # 返回预览数据
        return ExcelPreviewResponse(
            columns=df.columns.tolist(),
            data=processed_data,
            total_rows=len(df)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理Excel文件失败: {str(e)}")
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

# 线索数据导入Excel数据
@router.post("/api/leads/import_xiansuo")
async def import_xiansuo_data(
    file: UploadFile = File(...),
    config: str = None,
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    导入Excel线索数据到数据库
    需要验证必填字段并自动生成ID
    """
    # 用户权限验证
    user = None
    try:
        user = get_current_user(request, db)
        existing_frontend = db.query(UserPermissionFrontend).filter(UserPermissionFrontend.department == user.department,UserPermissionFrontend.identity == user.identity).first()
        if not (existing_frontend.import_lead_table == 0 or user.identity == "超管"):
            return HTTPException(status_code=400, detail="权限不足")
    except HTTPException:
        # 重新抛出权限相关的HTTP异常
        raise
    except:
        # 如果获取用户失败，允许继续（用于测试）
        pass

    # 解析config参数
    try:
        config_data = json.loads(config) if config else {}
        import_config = ExcelImportConfig(**config_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"解析导入配置失败: {str(e)}")

    # 检查文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持.xlsx或.xls格式的Excel文件")

    # 创建临时文件保存上传的Excel
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])

    try:
        # 写入临时文件
        content = await file.read()
        temp_file.write(content)
        temp_file.close()

        # 使用pandas读取Excel文件
        df = pd.read_excel(temp_file.name)

        # 检查是否有数据
        if df.empty:
            raise HTTPException(status_code=400, detail="Excel文件中没有数据")

        # 确定数据范围
        # 注意：pandas读取Excel后，第一行就是数据行（标题行已被作为列名）
        # 所以如果前端传递start_row=2，表示从Excel第2行开始，对应pandas索引0
        # 修正：start_row=2应该对应pandas索引0（第一行数据）
        start_row = max(0, import_config.start_row - 2)  # 修正索引计算
        end_row = import_config.end_row - 1 if import_config.end_row else len(df)  # 如果有end_row也需要修正

        # 获取要导入的数据
        import_df = df.iloc[start_row:end_row]

        # 必填字段映射（中文字段名到英文字段名）
        required_fields = {
            "记录日期": "record_date",
            "渠道": "channel",
            "微信ID": "wechat_id",
            "微信昵称": "wechat_name",  # 这个字段会单独处理，存储到SalaCrm表
            "店铺": "store",
            "登记人": "registrar",
            "对接人": "contact_person",
            "主播": "anchor"  # 主播为必填字段
        }

        # 可选字段映射（不包括必填字段）
        optional_fields = {
            "渠道ID": "channel_id",  # 新增可选字段
            "电话号码": "phone_number",
            "旺旺号": "wangwang_id",
            "主播": "anchor",
            "班次": "shift",
            "备注": "remarks"
        }

        # 验证必填字段是否存在
        missing_fields = []
        for chinese_name in required_fields.keys():
            if chinese_name not in df.columns:
                missing_fields.append(chinese_name)

        if missing_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件缺少必填字段: {', '.join(missing_fields)}"
            )

        # 获取登记人、对接人和主播的有效名单
        registrar_names = db.query(User.name).filter(User.department.in_(['新媒体部', '电商部'])).all()
        valid_registrars = [name[0] for name in registrar_names]

        contact_person_names = db.query(User.name).filter(User.department == '销售部').all()
        valid_contact_persons = [name[0] for name in contact_person_names]

        # 从presets.json读取主播名单
        valid_hosts = []
        try:
            presets_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'presets.json')
            with open(presets_path, 'r', encoding='utf-8') as f:
                presets_data = json.load(f)

            # 提取所有主播名字
            for host_group in presets_data.get('hosts', []):
                hosts_str = host_group.get('hosts', '')
                if hosts_str:
                    # 按逗号分割主播名字
                    host_names = [name.strip() for name in hosts_str.split(',') if name.strip()]
                    valid_hosts.extend(host_names)

        except Exception as e:
            print(f"读取主播配置文件失败: {str(e)}")
            # 如果读取失败，设置为空列表，这样会导致所有主播验证失败
            valid_hosts = []

        # 读取所有有效的店铺名单（新媒体 + 电商）
        valid_stores = []

        try:
            # 1. 从presets.json读取新媒体店铺
            presets_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'presets.json')
            with open(presets_path, 'r', encoding='utf-8') as f:
                presets_config = json.load(f)

            shops = presets_config.get('shops', [])
            for shop in shops:
                # 提取所有平台的简称
                if 'douyin_abbr' in shop and shop['douyin_abbr']:
                    valid_stores.append(shop['douyin_abbr'])
                if 'video_abbr' in shop and shop['video_abbr']:
                    valid_stores.append(shop['video_abbr'])
                if 'xiaohongshu_abbr' in shop and shop['xiaohongshu_abbr']:
                    valid_stores.append(shop['xiaohongshu_abbr'])
                if 'kuaishou_abbr' in shop and shop['kuaishou_abbr']:
                    valid_stores.append(shop['kuaishou_abbr'])

            print(f"从presets.json读取到 {len(valid_stores)} 个新媒体店铺")

        except Exception as e:
            print(f"读取新媒体店铺配置失败: {e}")

        try:
            # 2. 从ecommerce_stores.json读取电商店铺
            ecommerce_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'ecommerce_stores.json')
            with open(ecommerce_path, 'r', encoding='utf-8') as f:
                ecommerce_config = json.load(f)

            stores_data = ecommerce_config.get('stores', [])
            ecommerce_stores = []
            for store_group in stores_data:
                stores_str = store_group.get('stores', '')
                if stores_str:
                    # 按逗号分割店铺名称
                    store_names = [store.strip() for store in stores_str.split(',') if store.strip()]
                    ecommerce_stores.extend(store_names)

            valid_stores.extend(ecommerce_stores)
            print(f"从ecommerce_stores.json读取到 {len(ecommerce_stores)} 个电商店铺")

        except Exception as e:
            print(f"读取电商店铺配置失败: {e}")

        # 去重店铺名单
        valid_stores = list(set(valid_stores))
        print(f"总共有效店铺数量: {len(valid_stores)}")
        print(f"有效店铺列表: {valid_stores[:10]}...")  # 只打印前10个作为示例

        # 获取验证模式
        strict_validation = import_config.strict_validation
        print(f"验证模式: {'严格验证' if strict_validation else '宽松验证'}")

        # 第一阶段：全量验证所有数据行
        all_validation_errors = []
        validated_rows = []

        for index, row in import_df.iterrows():
            row_errors = []
            row_number = index + 2  # 计算实际行号（从1开始，包含表头）

            # 获取微信ID用于错误提示
            wechat_id = row.get("微信ID", "")
            if pd.isna(wechat_id):
                wechat_id = ""

            # 验证必填字段不为空
            for chinese_name in required_fields.keys():
                value = row.get(chinese_name)
                if pd.isna(value) or str(value).strip() == "" or str(value).lower() == "nan":
                    row_errors.append(f"{chinese_name}不能为空")

            # 验证记录时间格式 - 支持多种格式
            record_time_value = row.get("记录时间")
            if not pd.isna(record_time_value) and str(record_time_value).strip():
                record_time_str = str(record_time_value).strip()
                parsed_time = None

                # 支持的时间格式列表
                time_formats = [
                    "%Y/%m/%d %H:%M:%S",      # 2025/7/7 16:59:47
                    "%Y-%m-%d %H:%M:%S",      # 2025-07-07 16:59:47
                    "%Y/%m/%d %H:%M",         # 2025/7/7 16:59
                    "%Y-%m-%d %H:%M",         # 2025-07-07 16:59
                    "%Y/%m/%d",               # 2025/7/7 (只有日期，时间设为00:00:00)
                    "%Y-%m-%d",               # 2025-07-07 (只有日期，时间设为00:00:00)
                ]

                # 尝试解析时间
                for fmt in time_formats:
                    try:
                        parsed_time = datetime.strptime(record_time_str, fmt)
                        break
                    except ValueError:
                        continue

                # 如果所有格式都失败，尝试pandas的智能解析
                if parsed_time is None:
                    try:
                        parsed_time = pd.to_datetime(record_time_str)
                        if pd.isna(parsed_time):
                            raise ValueError("无法解析时间")
                    except:
                        row_errors.append(f"记录时间格式错误：'{record_time_str}'，支持格式如：2025/7/7 16:59:47 或 2025-07-07 16:59:47")


            # 严格验证模式下的额外业务验证
            if strict_validation:
                # 验证登记人
                registrar_value = row.get("登记人")
                if not pd.isna(registrar_value) and str(registrar_value).strip():
                    registrar_name = str(registrar_value).strip()
                    if registrar_name not in valid_registrars:
                        row_errors.append(f"登记人'{registrar_name}'不在新媒体部或电商部人员名单中")

                # 验证对接人
                contact_person_value = row.get("对接人")
                if not pd.isna(contact_person_value) and str(contact_person_value).strip():
                    contact_person_name = str(contact_person_value).strip()
                    if contact_person_name not in valid_contact_persons:
                        row_errors.append(f"对接人'{contact_person_name}'不在销售部人员名单中")

                # 验证主播
                host_value = row.get("主播")
                if not pd.isna(host_value) and str(host_value).strip():
                    host_name = str(host_value).strip()
                    if host_name not in valid_hosts:
                        row_errors.append(f"主播'{host_name}'不在主播名单中")

                # 验证店铺
                store_value = row.get("店铺")
                if not pd.isna(store_value) and str(store_value).strip():
                    store_name = str(store_value).strip()
                    if store_name not in valid_stores:
                        row_errors.append(f"店铺'{store_name}'不在有效店铺名单中")

            # 如果当前行有错误，合并为一条错误信息
            if row_errors:
                combined_error = f"第{row_number}行: 微信ID（可复制）：{wechat_id} " + "; ".join(row_errors)
                all_validation_errors.append(combined_error)
            else:
                # 如果该行没有错误，保存用于后续导入
                validated_rows.append((index, row))

        # 如果存在任何验证错误，直接返回错误信息，不执行导入
        if all_validation_errors:
            return {
                "success": False,
                "message": "数据验证失败，请修正错误后重新导入",
                "total_processed": len(import_df),
                "success_count": 0,
                "error_count": len(import_df) - len(validated_rows),
                "error_details": all_validation_errors
            }

        # 第二阶段：所有数据验证通过后，执行批量导入
        # 统计信息
        success_count = 0
        error_count = 0
        error_details = []

        # 获取当天的起始序列号
        current_date = datetime.now().strftime("%Y%m%d")
        today_pattern = f"{current_date}%"
        max_id = db.query(func.max(ClueSheet.ID)).filter(ClueSheet.ID.like(today_pattern)).scalar()

        if max_id is None:
            current_sequence = 1
        else:
            current_sequence = int(str(max_id)[-6:]) + 1

        # 处理验证通过的数据行
        for index, row in validated_rows:
            try:
                # 生成当前记录的ID
                new_id = f"{current_date}{current_sequence:06d}"
                current_sequence += 1

                # 处理记录日期 - 格式为2025/07/05
                record_date_value = row.get("记录日期")
                if pd.isna(record_date_value):
                    record_date = datetime.now().date()
                else:
                    if isinstance(record_date_value, str):
                        # 尝试解析字符串日期，优先支持2025/07/05格式
                        try:
                            record_date = datetime.strptime(record_date_value, "%Y/%m/%d").date()
                        except ValueError:
                            try:
                                # 兼容其他格式
                                record_date = datetime.strptime(record_date_value, "%Y-%m-%d").date()
                            except ValueError:
                                try:
                                    record_date = datetime.strptime(record_date_value, "%Y/%m/%d").date()
                                except ValueError:
                                    record_date = datetime.now().date()
                    else:
                        # 如果是datetime对象，提取日期部分
                        record_date = record_date_value.date() if hasattr(record_date_value, 'date') else datetime.now().date()

                # 将record_date格式化为2025/07/05格式存储
                record_date_str = record_date.strftime("%Y/%m/%d")

                # 处理记录时间 - 支持多种格式并转换为标准格式
                record_time_value = row.get("记录时间")
                record_time_str = str(record_time_value).strip()
                parsed_time = None

                # 支持的时间格式列表
                time_formats = [
                    "%Y/%m/%d %H:%M:%S",      # 2025/7/7 16:59:47
                    "%Y-%m-%d %H:%M:%S",      # 2025-07-07 16:59:47
                    "%Y/%m/%d %H:%M",         # 2025/7/7 16:59
                    "%Y-%m-%d %H:%M",         # 2025-07-07 16:59
                    "%Y/%m/%d",               # 2025/7/7 (只有日期，时间设为00:00:00)
                    "%Y-%m-%d",               # 2025-07-07 (只有日期，时间设为00:00:00)
                ]

                # 尝试解析时间
                for fmt in time_formats:
                    try:
                        parsed_time = datetime.strptime(record_time_str, fmt)
                        break
                    except ValueError:
                        continue

                # 如果所有格式都失败，尝试pandas的智能解析
                if parsed_time is None:
                    try:
                        parsed_time = pd.to_datetime(record_time_str)
                        if pd.isna(parsed_time):
                            raise ValueError("无法解析时间")
                        # 将pandas的Timestamp转换为datetime
                        parsed_time = parsed_time.to_pydatetime()
                    except:
                        # 如果还是失败，使用当前时间
                        parsed_time = datetime.now()

                # 转换为标准格式：2025-06-25 01:29:47
                record_time = parsed_time

                # 获取当前登录用户（如果有的话）
                current_user_name = user.name if user else "系统导入"

                # 处理渠道字段（已在第一阶段验证过）
                channel_value = row.get("渠道")

                # 处理渠道ID字段（旺旺号字段用于存储渠道ID）
                channel_id_value = row.get("渠道ID")
                if pd.isna(channel_id_value) or str(channel_id_value).strip() == "":
                    wangwang_id = "无"
                else:
                    wangwang_id = str(channel_id_value).strip()

                # 准备ClueSheet数据
                clue_data = {
                    "ID": new_id,
                    "record_date": record_date_str,  # 使用格式化后的日期字符串
                    "channel": str(channel_value).strip(),
                    "wangwang_id": wangwang_id,
                    "phone_number": format_number_field(row.get("电话号码")) if not pd.isna(row.get("电话号码")) else None,
                    "wechat_id": format_number_field(row.get("微信ID")),
                    "queue": 0 if "F" in str(row.get("店铺", "")) else 1,  # 包含F为付费店铺(0)，否则为免费店铺(1)
                    "store": str(row.get("店铺", "")).strip(),
                    "registrar": str(row.get("登记人", "")).strip() if not pd.isna(row.get("登记人")) else current_user_name,
                    "contact_person": str(row.get("对接人", "")).strip(),
                    "type": 3,  # 默认为3
                    "record_time": record_time,
                    "anchor": str(row.get("主播", "")).strip() if not pd.isna(row.get("主播")) else None,
                    "remarks": str(row.get("备注", "")).strip() if not pd.isna(row.get("备注")) else None,
                    "shift": str(row.get("班次", "")).strip() if not pd.isna(row.get("班次")) else None,
                    "status": 3,  # 默认为3
                    "is_deleted": 0,
                    "delete_time": None,
                    "delete_user": None,
                    "not_admin": None,
                    "kefu_image": None
                }

                # 加密敏感数据（在创建对象之前）
                if clue_data["phone_number"]:
                    clue_data["phone_number"] = base64_encrypt_data(clue_data["phone_number"])
                if clue_data["wechat_id"]:
                    clue_data["wechat_id"] = base64_encrypt_data(clue_data["wechat_id"])

                # 创建ClueSheet记录
                new_clue = ClueSheet(**clue_data)
                db.add(new_clue)

                # 创建SalaCrm记录
                # 获取微信昵称
                wechat_name_value = row.get("微信昵称")
                wechat_name = str(wechat_name_value).strip() if not pd.isna(wechat_name_value) else ""

                sala_crm_data = {
                    "ID": new_id,
                    "allocation_date": record_time,  # 与ClueSheet.record_time相同
                    "SN": 0,
                    "wechat_name": wechat_name,
                    "add_date": None,  # 默认为NULL
                    "last_followup_time": record_time,  # 与ClueSheet.record_time相同
                    "is_deleted": 0
                }

                new_sala_crm = SalaCrm(**sala_crm_data)
                db.add(new_sala_crm)

                # 使用事务确保两个表同时成功或失败
                try:
                    db.commit()
                    success_count += 1
                except Exception as commit_error:
                    db.rollback()
                    error_details.append(f"第{index+2}行数据库提交失败: {str(commit_error)}")
                    error_count += 1
                    continue

            except Exception as e:
                error_count += 1
                error_details.append(f"第{index+2}行导入失败: {str(e)}")
                continue

        # 所有记录已经逐个提交，无需再次提交

        # 返回导入结果
        result = {
            "success": True,
            "message": f"导入完成",
            "total_processed": success_count + error_count,
            "success_count": success_count,
            "error_count": error_count
        }

        if error_details:
            result["error_details"] = error_details[:10]  # 只返回前10个错误详情
            if len(error_details) > 10:
                result["error_details"].append(f"... 还有{len(error_details) - 10}个错误")

        return result

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)


@router.post("/api/leads/export_error_data")
async def export_error_data(file: UploadFile = File(...)):
    """
    导出错误数据Excel文件
    """
    try:
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')

        # 保存上传的文件
        with open(temp_file.name, 'wb') as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 读取Excel文件
        df = pd.read_excel(temp_file.name)

        # 必填字段映射（与import_xiansuo保持一致）
        required_fields = {
            "记录日期": "record_date",
            "渠道": "channel",
            "微信ID": "wechat_id",
            "微信昵称": "wechat_name",
            "店铺": "store",
            "登记人": "registrar",
            "对接人": "contact_person",
            "主播": "anchor"
        }

        # 验证数据并收集错误信息
        error_rows = []
        error_details_map = {}

        for index, row in df.iterrows():
            row_errors = []
            row_number = index + 2  # 计算实际行号（从1开始，包含表头）

            # 获取微信ID用于错误提示
            wechat_id = row.get("微信ID", "")
            if pd.isna(wechat_id):
                wechat_id = ""

            # 验证必填字段不为空
            for chinese_name in required_fields.keys():
                value = row.get(chinese_name)
                if pd.isna(value) or str(value).strip() == "" or str(value).lower() == "nan":
                    row_errors.append(f"{chinese_name}不能为空")

            # 如果有错误，记录该行，格式与import_xiansuo保持一致
            if row_errors:
                error_rows.append(index)
                combined_error = f"第{row_number}行: 微信ID（可复制）：{wechat_id} " + "; ".join(row_errors)
                error_details_map[index] = combined_error

        # 如果没有错误数据，返回提示
        if not error_rows:
            raise HTTPException(status_code=400, detail="没有发现错误数据")

        # 创建错误数据DataFrame
        error_df = df.iloc[error_rows].copy()

        # 添加错误详情列（只显示错误信息，不包括行号和微信ID）
        error_details_only = []
        for index, row in df.iterrows():
            if index in error_rows:
                row_errors = []
                # 重新验证必填字段不为空（只获取错误信息）
                for chinese_name in required_fields.keys():
                    value = row.get(chinese_name)
                    if pd.isna(value) or str(value).strip() == "" or str(value).lower() == "nan":
                        row_errors.append(f"{chinese_name}不能为空")
                error_details_only.append("; ".join(row_errors))

        error_df['错误详情'] = error_details_only

        # 生成文件名
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"错误数据_{current_time}.xlsx"

        # 创建Excel文件到内存
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            error_df.to_excel(writer, index=False, sheet_name='错误数据')

        output.seek(0)

        # 对文件名进行URL编码以支持中文
        from urllib.parse import quote
        encoded_filename = quote(filename.encode('utf-8'))

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            }
        )

    except HTTPException:
        # 重新抛出HTTP异常（如"没有发现错误数据"）
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出错误数据失败: {str(e)}")
    finally:
        # 删除临时文件
        if 'temp_file' in locals() and os.path.exists(temp_file.name):
            os.unlink(temp_file.name)