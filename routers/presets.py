from fastapi import APIRouter, HTTPException, UploadFile, File, Query
import json
import os
import tempfile
import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from io import BytesIO
from fastapi import Request, Depends
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from database import get_db
from auth import get_current_user, check_backend_permission
from fastapi.templating import Jinja2Templates

router = APIRouter(prefix="/api/presets")

# 预设值JSON文件路径
PRESETS_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "presets.json")
# 电商店铺预设JSON文件路径
ECOMMERCE_STORES_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "ecommerce_stores.json")

# 数据模型
class ShopItem(BaseModel):
    name: str
    manager: Optional[str] = ""
    douyin_abbr: Optional[str] = ""
    video_abbr: Optional[str] = ""
    xiaohongshu_abbr: Optional[str] = ""
    kuaishou_abbr: Optional[str] = ""

class ShopUpdate(BaseModel):
    old_name: str
    new_name: str
    manager: Optional[str] = ""
    douyin_abbr: Optional[str] = ""
    video_abbr: Optional[str] = ""
    xiaohongshu_abbr: Optional[str] = ""
    kuaishou_abbr: Optional[str] = ""

class PresetItem(BaseModel):
    name: str

class PresetUpdate(BaseModel):
    old_name: str
    new_name: str

class Presets(BaseModel):
    shops: List[ShopItem]
    hosts: List[str]
    rooms: List[str] = []  # 添加直播间列表字段，默认为空列表
    sales: List[Dict[str, Any]] = []  # 添加销售组列表字段，默认为空列表

# Excel预览响应模型
class ExcelPreviewResponse(BaseModel):
    columns: List[str]
    data: List[List[Any]]
    total_rows: int

# Excel导入配置模型
class ExcelImportConfig(BaseModel):
    field_mappings: Dict[str, str] = Field(..., description="Excel列名到数据库字段的映射")
    start_row: int = Field(1, description="开始导入的行号（从1开始）")
    end_row: Optional[int] = Field(None, description="结束导入的行号，如果为空则导入到最后一行")

# 主播Excel导入配置模型
class HostExcelImportConfig(BaseModel):
    field_mappings: Dict[str, str] = Field(..., description="Excel列名到主播名称字段的映射")
    start_row: int = Field(1, description="开始导入的行号（从1开始）")
    end_row: Optional[int] = Field(None, description="结束导入的行号，如果为空则导入到最后一行")

# 批量删除请求模型
class BatchDeleteRequest(BaseModel):
    items: List[str]

# 电商店铺批量删除请求模型
class EcommerceStoreBatchDeleteRequest(BaseModel):
    items: List[str] = Field(..., description="要删除的电商店铺ID列表")

class SalesBatchDeleteRequest(BaseModel):
    ids: List[str] = Field(..., description="要删除的销售组ID列表")

# 直播间Excel导入配置模型
class RoomExcelImportConfig(BaseModel):
    field_mappings: Dict[str, str] = Field(..., description="Excel列名到直播间名称字段的映射")
    start_row: int = Field(1, description="开始导入的行号（从1开始）")
    end_row: Optional[int] = Field(None, description="结束导入的行号，如果为空则导入到最后一行")

# 销售组数据模型
class SalesItem(BaseModel):
    group: str
    leader: str
    members: str

class SalesUpdate(BaseModel):
    id: str
    group: str
    leader: str
    members: str

class SalesImportItem(BaseModel):
    group: str
    leader: str
    members: str

class SalesImportConfig(BaseModel):
    data: List[SalesImportItem]
    overwrite: bool = False

# 电商店铺数据模型
class EcommerceStoreItem(BaseModel):
    id: Optional[str] = None
    platform: str
    stores: str
    
class EcommerceStoreUpdateItem(BaseModel):
    id: str
    platform: str
    stores: str

# 主播组数据模型
class HostItem(BaseModel):
    leader: str
    group: str
    hosts: str
    
class HostUpdateItem(BaseModel):
    id: str
    leader: str
    group: str
    hosts: str

class HostDeleteRequest(BaseModel):
    ids: List[str]

# 辅助函数：读取预设值
def read_presets():
    try:
        if not os.path.exists(PRESETS_FILE):
            # 如果文件不存在，创建默认文件
            default_presets = {"shops": [], "hosts": [], "rooms": [], "sales": []}
            with open(PRESETS_FILE, "w", encoding="utf-8") as f:
                json.dump(default_presets, f, ensure_ascii=False, indent=2)
            return default_presets
        
        with open(PRESETS_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)
            
            # 兼容旧数据格式（字符串列表）转换为新格式（对象列表）
            if data.get("shops") and isinstance(data["shops"][0], str):
                data["shops"] = [{"name": shop, "manager": "", "douyin_abbr": "", "video_abbr": "", "xiaohongshu_abbr": "", "kuaishou_abbr": ""} for shop in data["shops"]]
            
            # 确保rooms字段存在
            if "rooms" not in data:
                data["rooms"] = []
                
            return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取预设值失败: {str(e)}")

# 辅助函数：写入预设值
def write_presets(presets):
    try:
        with open(PRESETS_FILE, "w", encoding="utf-8") as f:
            json.dump(presets, f, ensure_ascii=False, indent=2)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存预设值失败: {str(e)}")

# 获取所有预设值
@router.get("")
async def get_presets():
    try:
        presets = read_presets()
        # print("返回预设值:", presets)  # 添加调试信息
        
        # 为了兼容前端，将复杂的店铺对象转换为简单的店铺名称列表
        shop_names = [shop["name"] for shop in presets["shops"]]
        
        # 确保sales字段存在
        if "sales" not in presets:
            presets["sales"] = []
            
        # 处理主播数据，如果是对象数组，提取主播名称
        host_names = []
        if presets.get("hosts"):
            for host in presets["hosts"]:
                if isinstance(host, dict) and "hosts" in host:
                    # 如果是主播组对象，提取所有主播名称并添加到列表
                    hosts_str = host.get("hosts", "")
                    if hosts_str:
                        host_list = [h.strip() for h in hosts_str.split(",")]
                        host_names.extend(host_list)
                elif isinstance(host, str):
                    # 如果是字符串，直接添加
                    host_names.append(host)
            
        return {"shops": shop_names, "hosts": host_names, "rooms": presets["rooms"], "sales": presets["sales"]}
    except Exception as e:
        print("获取预设值出错:", str(e))  # 添加调试信息
        raise

# 获取详细的店铺预设值（包含所有字段）
@router.get("/shops/detail")
async def get_shops_detail():
    try:
        presets = read_presets()
        return presets["shops"]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取店铺详情失败: {str(e)}")

# 测试路由，用于检查预设值是否正确
@router.get("/test")
async def test_presets():
    try:
        presets = read_presets()
        return {
            "status": "success",
            "message": "预设值读取成功",
            "data": presets,
            "file_path": PRESETS_FILE
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"预设值读取失败: {str(e)}",
            "file_path": PRESETS_FILE
        }

# 添加店铺预设值
@router.post("/shops")
async def add_shop(item: ShopItem):
    presets = read_presets()
    
    # 检查店铺名是否已存在
    for shop in presets["shops"]:
        if shop["name"] == item.name:
            raise HTTPException(status_code=400, detail="店铺名已存在")
    
    presets["shops"].append(item.dict())
    write_presets(presets)
    return {"message": "店铺添加成功"}

# 更新店铺预设值
@router.put("/shops")
async def update_shop(update: ShopUpdate):
    presets = read_presets()
    
    # 查找要更新的店铺
    found = False
    for i, shop in enumerate(presets["shops"]):
        if shop["name"] == update.old_name:
            found = True
            # 检查新名称是否与其他店铺冲突
            if update.old_name != update.new_name:
                for other_shop in presets["shops"]:
                    if other_shop["name"] == update.new_name:
                        raise HTTPException(status_code=400, detail="新店铺名已存在")
            
            # 更新店铺信息
            presets["shops"][i] = {
                "name": update.new_name,
                "manager": update.manager,
                "douyin_abbr": update.douyin_abbr,
                "video_abbr": update.video_abbr,
                "xiaohongshu_abbr": update.xiaohongshu_abbr,
                "kuaishou_abbr": update.kuaishou_abbr
            }
            break
    
    if not found:
        raise HTTPException(status_code=404, detail="店铺不存在")
    
    write_presets(presets)
    return {"message": "店铺更新成功"}

# 删除店铺预设值
@router.delete("/shops/{shop_name}")
async def delete_shop(shop_name: str):
    presets = read_presets()
    
    # 查找要删除的店铺
    found = False
    for i, shop in enumerate(presets["shops"]):
        if shop["name"] == shop_name:
            found = True
            presets["shops"].pop(i)
            break
    
    if not found:
        raise HTTPException(status_code=404, detail="店铺不存在")
    
    write_presets(presets)
    return {"message": "店铺删除成功"}

# 查询主播预设值
@router.get("/hosts")
async def get_hosts():
    presets = read_presets()
    if "hosts" not in presets:
        presets["hosts"] = []
    return {"hosts": presets["hosts"]}

# 添加主播预设值
@router.post("/hosts")
async def add_host(item: HostItem):
    presets = read_presets()
    
    # 生成唯一ID
    import uuid
    new_id = str(uuid.uuid4())
    
    # 计算主播人数
    host_count = len(item.hosts.split(',')) if item.hosts else 0
    
    # 创建新主播组记录
    new_host = {
        "id": new_id,
        "leader": item.leader,
        "group": item.group,
        "hosts": item.hosts,
        "count": host_count
    }
    
    presets["hosts"].append(new_host)
    write_presets(presets)
    return {"message": "主播组添加成功", "id": new_id}

# 更新主播预设值
@router.put("/hosts")
async def update_host(item: HostUpdateItem):
    presets = read_presets()
    
    # 查找要更新的记录
    host_item = None
    for host in presets["hosts"]:
        if host["id"] == item.id:
            host_item = host
            break
    
    if not host_item:
        raise HTTPException(status_code=404, detail="主播组不存在")
    
    # 计算主播人数
    host_count = len(item.hosts.split(',')) if item.hosts else 0
    
    # 更新记录
    host_item["leader"] = item.leader
    host_item["group"] = item.group
    host_item["hosts"] = item.hosts
    host_item["count"] = host_count
    
    write_presets(presets)
    return {"message": "主播组更新成功"}

# 删除主播预设值
@router.delete("/hosts/{host_id}")
async def delete_host(host_id: str):
    presets = read_presets()
    
    # 查找并删除指定ID的记录
    for i, host in enumerate(presets["hosts"]):
        if host["id"] == host_id:
            del presets["hosts"][i]
            write_presets(presets)
            return {"message": "主播组删除成功"}
    
    raise HTTPException(status_code=404, detail="主播组不存在")

# 批量删除主播预设值
@router.post("/hosts/batch-delete")
async def batch_delete_hosts(request: HostDeleteRequest):
    presets = read_presets()
    
    # 记录删除前的数量
    before_count = len(presets["hosts"])
    
    # 过滤掉要删除的主播组
    presets["hosts"] = [host for host in presets["hosts"] if host["id"] not in request.ids]
    
    # 计算删除的数量
    deleted_count = before_count - len(presets["hosts"])
    
    write_presets(presets)
    return {"message": f"成功删除{deleted_count}个主播组"}

# 上传Excel文件并预览
@router.post("/shops/upload-excel")
async def upload_excel_file(file: UploadFile = File(...)):
    # 检查文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持.xlsx或.xls格式的Excel文件")
    
    # 创建临时文件保存上传的Excel
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    try:
        # 写入临时文件
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        # 使用pandas读取Excel文件
        df = pd.read_excel(temp_file.name)
        
        # 获取所有数据而不仅仅是前5行
        preview_data = df.values.tolist()
        
        # 处理特殊浮点值（NaN, Infinity等）
        processed_data = []
        for row in preview_data:
            processed_row = []
            for value in row:
                if isinstance(value, float):
                    if pd.isna(value):
                        processed_row.append(None)  # 将NaN转换为None
                    elif value == float('inf') or value == float('-inf'):
                        processed_row.append(None)  # 将Infinity转换为None
                    else:
                        processed_row.append(value)
                else:
                    processed_row.append(value)
            processed_data.append(processed_row)
        
        # 返回预览数据
        return ExcelPreviewResponse(
            columns=df.columns.tolist(),
            data=processed_data,
            total_rows=len(df)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理Excel文件失败: {str(e)}")
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

# 导入Excel数据
@router.post("/shops/import-excel")
async def import_excel_data(file: UploadFile = File(...), config: str = None):
    # 解析config参数
    try:
        config_data = json.loads(config) if config else {}
        import_config = ExcelImportConfig(**config_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"解析导入配置失败: {str(e)}")
    
    # 检查文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持.xlsx或.xls格式的Excel文件")
    
    # 创建临时文件保存上传的Excel
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    try:
        # 写入临时文件
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        # 使用pandas读取Excel文件
        df = pd.read_excel(temp_file.name)
        
        # 安全处理特殊值
        for col in df.columns:
            # 只对数值列应用替换
            if pd.api.types.is_numeric_dtype(df[col]):
                df[col] = df[col].replace([float('inf'), float('-inf')], None)
        
        # 检查行范围
        if import_config.start_row < 1:
            import_config.start_row = 1
        
        if import_config.end_row is None or import_config.end_row > len(df):
            import_config.end_row = len(df)
        
        # 过滤行范围
        df = df.iloc[import_config.start_row-1:import_config.end_row]
        
        # 读取现有预设
        presets = read_presets()
        existing_shop_names = [shop["name"] for shop in presets["shops"]]
        
        # 导入计数
        imported_count = 0
        skipped_count = 0
        
        # 检查必须映射店铺名称字段
        if "name" not in import_config.field_mappings.values():
            raise HTTPException(status_code=400, detail="必须映射店铺名称字段")
        
        # 处理每一行数据
        for _, row in df.iterrows():
            # 根据映射创建店铺数据
            shop_data = {}
            for excel_col, db_field in import_config.field_mappings.items():
                if excel_col in df.columns:
                    # 安全处理值
                    value = row[excel_col]
                    # 检查是否为NaN
                    if pd.isna(value):
                        value = ""
                    # 检查是否为数值类型的无穷大
                    elif isinstance(value, (int, float)) and (value == float('inf') or value == float('-inf')):
                        value = ""
                    shop_data[db_field] = str(value)
            
            # 检查是否有店铺名
            if "name" not in shop_data or not shop_data["name"]:
                skipped_count += 1
                continue
            
            # 检查店铺名是否已存在
            if shop_data["name"] in existing_shop_names:
                skipped_count += 1
                continue
            
            # 创建完整的店铺对象
            shop_item = {
                "name": shop_data.get("name", ""),
                "manager": shop_data.get("manager", ""),
                "douyin_abbr": shop_data.get("douyin_abbr", ""),
                "video_abbr": shop_data.get("video_abbr", ""),
                "xiaohongshu_abbr": shop_data.get("xiaohongshu_abbr", ""),
                "kuaishou_abbr": shop_data.get("kuaishou_abbr", "")
            }
            
            # 添加到预设
            presets["shops"].append(shop_item)
            existing_shop_names.append(shop_item["name"])
            imported_count += 1
        
        # 保存预设
        write_presets(presets)
        
        return {
            "message": "Excel导入成功",
            "imported_count": imported_count,
            "skipped_count": skipped_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入Excel数据失败: {str(e)}")
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

# 上传Excel文件并预览主播数据
@router.post("/hosts/upload-excel")
async def upload_host_excel_file(file: UploadFile = File(...)):
    # 检查文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持.xlsx或.xls格式的Excel文件")
    
    # 创建临时文件保存上传的Excel
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    try:
        # 写入临时文件
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        # 使用pandas读取Excel文件
        df = pd.read_excel(temp_file.name)
        
        # 获取所有数据
        preview_data = df.values.tolist()
        
        # 处理特殊浮点值（NaN, Infinity等）
        processed_data = []
        for row in preview_data:
            processed_row = []
            for value in row:
                if isinstance(value, float):
                    if pd.isna(value):
                        processed_row.append(None)  # 将NaN转换为None
                    elif value == float('inf') or value == float('-inf'):
                        processed_row.append(None)  # 将Infinity转换为None
                    else:
                        processed_row.append(value)
                else:
                    processed_row.append(value)
            processed_data.append(processed_row)
        
        # 返回预览数据
        return ExcelPreviewResponse(
            columns=df.columns.tolist(),
            data=processed_data,
            total_rows=len(df)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理Excel文件失败: {str(e)}")
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

# 导入Excel主播数据
@router.post("/hosts/import-excel")
async def import_host_excel_data(file: UploadFile = File(...), config: str = None):
    # 解析config参数
    try:
        config_data = json.loads(config) if config else {}
        import_config = HostExcelImportConfig(**config_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"解析导入配置失败: {str(e)}")
    
    # 检查文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持.xlsx或.xls格式的Excel文件")
    
    # 创建临时文件保存上传的Excel
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    try:
        # 写入临时文件
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        # 使用pandas读取Excel文件
        df = pd.read_excel(temp_file.name)
        
        # 安全处理特殊值
        for col in df.columns:
            # 只对数值列应用替换
            if pd.api.types.is_numeric_dtype(df[col]):
                df[col] = df[col].replace([float('inf'), float('-inf')], None)
        
        # 检查行范围
        if import_config.start_row < 1:
            import_config.start_row = 1
        
        if import_config.end_row is None or import_config.end_row > len(df):
            import_config.end_row = len(df)
        
        # 过滤行范围
        df = df.iloc[import_config.start_row-1:import_config.end_row]
        
        # 读取现有预设
        presets = read_presets()
        
        # 使用集合来存储所有主播名称，避免重复
        all_hosts_set = set(presets["hosts"])
        print(f"导入前的主播数量: {len(all_hosts_set)}")
        
        # 导入计数
        imported_count = 0
        skipped_count = 0
        
        # 检查必须映射主播名称字段
        if "name" not in import_config.field_mappings.values():
            raise HTTPException(status_code=400, detail="必须映射主播名称字段")
        
        # 处理每一行数据
        for _, row in df.iterrows():
            # 根据映射获取主播名称
            host_name = None
            for excel_col, field_name in import_config.field_mappings.items():
                if excel_col in df.columns and field_name == "name":
                    # 安全处理值
                    value = row[excel_col]
                    # 检查是否为NaN
                    if pd.isna(value):
                        continue
                    # 检查是否为数值类型的无穷大
                    elif isinstance(value, (int, float)) and (value == float('inf') or value == float('-inf')):
                        continue
                    host_name = str(value).strip()
                    break
            
            # 检查是否有主播名
            if not host_name:
                skipped_count += 1
                continue
            
            # 检查主播名是否已存在于集合中
            if host_name in all_hosts_set:
                skipped_count += 1
                continue
            
            # 添加到主播集合
            all_hosts_set.add(host_name)
            imported_count += 1
        
        # 更新预设中的主播列表（使用集合转换为列表，确保没有重复）
        presets["hosts"] = list(all_hosts_set)
        
        print(f"导入后的主播数量: {len(presets['hosts'])}")
        print(f"新增主播数量: {imported_count}")
        
        # 保存预设
        write_presets(presets)
        
        return {
            "message": "Excel导入成功",
            "imported_count": imported_count,
            "skipped_count": skipped_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入Excel数据失败: {str(e)}")
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

# 批量删除店铺预设值
@router.post("/shops/batch-delete")
async def batch_delete_shops(request: BatchDeleteRequest):
    presets = read_presets()
    
    # 记录删除的数量
    deleted_count = 0
    
    # 遍历要删除的店铺名称
    for shop_name in request.items:
        # 查找要删除的店铺
        for i, shop in enumerate(presets["shops"]):
            if shop["name"] == shop_name:
                presets["shops"].pop(i)
                deleted_count += 1
                break
    
    write_presets(presets)
    return {"message": f"成功删除{deleted_count}个店铺"}

# 添加直播间预设值
@router.post("/rooms")
async def add_room(item: PresetItem):
    presets = read_presets()
    
    # 确保rooms字段存在
    if "rooms" not in presets:
        presets["rooms"] = []
        
    if item.name in presets["rooms"]:
        raise HTTPException(status_code=400, detail="直播间名已存在")
    
    presets["rooms"].append(item.name)
    write_presets(presets)
    return {"message": "直播间添加成功"}

# 更新直播间预设值
@router.put("/rooms")
async def update_room(update: PresetUpdate):
    presets = read_presets()
    
    # 确保rooms字段存在
    if "rooms" not in presets:
        presets["rooms"] = []
        
    if update.old_name not in presets["rooms"]:
        raise HTTPException(status_code=404, detail="直播间不存在")
    if update.new_name in presets["rooms"]:
        raise HTTPException(status_code=400, detail="新直播间名已存在")
    
    index = presets["rooms"].index(update.old_name)
    presets["rooms"][index] = update.new_name
    write_presets(presets)
    return {"message": "直播间更新成功"}

# 删除直播间预设值
@router.delete("/rooms/{room_name}")
async def delete_room(room_name: str):
    presets = read_presets()
    
    # 确保rooms字段存在
    if "rooms" not in presets:
        presets["rooms"] = []
        
    if room_name not in presets["rooms"]:
        raise HTTPException(status_code=404, detail="直播间不存在")
    
    presets["rooms"].remove(room_name)
    write_presets(presets)
    return {"message": "直播间删除成功"}

# 批量删除直播间预设值
@router.post("/rooms/batch-delete")
async def batch_delete_rooms(request: BatchDeleteRequest):
    presets = read_presets()
    
    # 确保rooms字段存在
    if "rooms" not in presets:
        presets["rooms"] = []
    
    # 记录删除的数量
    deleted_count = 0
    
    # 遍历要删除的直播间名称
    for room_name in request.items:
        if room_name in presets["rooms"]:
            presets["rooms"].remove(room_name)
            deleted_count += 1
    
    write_presets(presets)
    return {"message": f"成功删除{deleted_count}个直播间"}

# 上传Excel文件并预览直播间数据
@router.post("/rooms/upload-excel")
async def upload_room_excel_file(file: UploadFile = File(...)):
    # 检查文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持.xlsx或.xls格式的Excel文件")
    
    # 创建临时文件保存上传的Excel
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    try:
        # 写入临时文件
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        # 使用pandas读取Excel文件
        df = pd.read_excel(temp_file.name)
        
        # 获取所有数据
        preview_data = df.values.tolist()
        
        # 处理特殊浮点值（NaN, Infinity等）
        processed_data = []
        for row in preview_data:
            processed_row = []
            for value in row:
                if isinstance(value, float):
                    if pd.isna(value):
                        processed_row.append(None)  # 将NaN转换为None
                    elif value == float('inf') or value == float('-inf'):
                        processed_row.append(None)  # 将Infinity转换为None
                    else:
                        processed_row.append(value)
                else:
                    processed_row.append(value)
            processed_data.append(processed_row)
        
        # 返回预览数据
        return ExcelPreviewResponse(
            columns=df.columns.tolist(),
            data=processed_data,
            total_rows=len(df)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理Excel文件失败: {str(e)}")
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

# 导入Excel直播间数据
@router.post("/rooms/import-excel")
async def import_room_excel_data(file: UploadFile = File(...), config: str = None):
    # 解析config参数
    try:
        config_data = json.loads(config) if config else {}
        import_config = RoomExcelImportConfig(**config_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"解析导入配置失败: {str(e)}")
    
    # 检查文件格式
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持.xlsx或.xls格式的Excel文件")
    
    # 创建临时文件保存上传的Excel
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    try:
        # 写入临时文件
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        # 使用pandas读取Excel文件
        df = pd.read_excel(temp_file.name)
        
        # 安全处理特殊值
        for col in df.columns:
            # 只对数值列应用替换
            if pd.api.types.is_numeric_dtype(df[col]):
                df[col] = df[col].replace([float('inf'), float('-inf')], None)
        
        # 检查行范围
        if import_config.start_row < 1:
            import_config.start_row = 1
        
        if import_config.end_row is None or import_config.end_row > len(df):
            import_config.end_row = len(df)
        
        # 过滤行范围
        df = df.iloc[import_config.start_row-1:import_config.end_row]
        
        # 读取现有预设
        presets = read_presets()
        
        # 使用集合来存储所有直播间名称，避免重复
        all_rooms_set = set(presets["rooms"])
        print(f"导入前的直播间数量: {len(all_rooms_set)}")
        
        # 导入计数
        imported_count = 0
        skipped_count = 0
        
        # 检查必须映射直播间名称字段
        if "name" not in import_config.field_mappings.values():
            raise HTTPException(status_code=400, detail="必须映射直播间名称字段")
        
        # 处理每一行数据
        for _, row in df.iterrows():
            # 根据映射获取直播间名称
            room_name = None
            for excel_col, field_name in import_config.field_mappings.items():
                if excel_col in df.columns and field_name == "name":
                    # 安全处理值
                    value = row[excel_col]
                    # 检查是否为NaN
                    if pd.isna(value):
                        continue
                    # 检查是否为数值类型的无穷大
                    elif isinstance(value, (int, float)) and (value == float('inf') or value == float('-inf')):
                        continue
                    room_name = str(value).strip()
                    break
            
            # 检查是否有直播间名
            if not room_name:
                skipped_count += 1
                continue
            
            # 检查直播间名是否已存在于集合中
            if room_name in all_rooms_set:
                skipped_count += 1
                continue
            
            # 添加到直播间集合
            all_rooms_set.add(room_name)
            imported_count += 1
        
        # 更新预设中的直播间列表（使用集合转换为列表，确保没有重复）
        presets["rooms"] = list(all_rooms_set)
        
        print(f"导入后的直播间数量: {len(presets['rooms'])}")
        print(f"新增直播间数量: {imported_count}")
        
        # 保存预设
        write_presets(presets)
        
        return {
            "message": "Excel导入成功",
            "imported_count": imported_count,
            "skipped_count": skipped_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入Excel数据失败: {str(e)}")
    finally:
        # 删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

# 销售组API路由
@router.get("/sales")
async def get_sales():
    presets = read_presets()
    if "sales" not in presets:
        presets["sales"] = []
    return {"sales": presets["sales"]}

@router.post("/sales")
async def add_sales(item: SalesItem):
    presets = read_presets()
    
    # 确保sales字段存在
    if "sales" not in presets:
        presets["sales"] = []
    
    # 检查是否已存在相同分组
    for sale in presets["sales"]:
        if sale["group"] == item.group:
            raise HTTPException(status_code=400, detail=f"销售组 '{item.group}' 已存在")
    
    # 生成唯一ID
    import uuid
    sale_id = str(uuid.uuid4())
    
    # 添加新销售组
    presets["sales"].append({
        "id": sale_id,
        "group": item.group,
        "leader": item.leader,
        "members": item.members
    })
    
    write_presets(presets)
    return {"message": "销售组添加成功", "id": sale_id}

@router.put("/sales/{sale_id}")
async def update_sales(sale_id: str, item: SalesItem):
    presets = read_presets()
    
    # 确保sales字段存在
    if "sales" not in presets:
        presets["sales"] = []
    
    # 查找要更新的销售组
    found = False
    for i, sale in enumerate(presets["sales"]):
        if sale["id"] == sale_id:
            found = True
            # 检查是否有其他销售组使用相同分组名称
            for other_sale in presets["sales"]:
                if other_sale["id"] != sale_id and other_sale["group"] == item.group:
                    raise HTTPException(status_code=400, detail=f"销售组 '{item.group}' 已存在")
            
            # 更新销售组
            presets["sales"][i] = {
                "id": sale_id,
                "group": item.group,
                "leader": item.leader,
                "members": item.members
            }
            break
    
    if not found:
        raise HTTPException(status_code=404, detail=f"销售组ID '{sale_id}' 不存在")
    
    write_presets(presets)
    return {"message": "销售组更新成功"}

@router.delete("/sales/{sale_id}")
async def delete_sales(sale_id: str):
    presets = read_presets()
    
    # 确保sales字段存在
    if "sales" not in presets:
        presets["sales"] = []
    
    # 查找要删除的销售组
    found = False
    for i, sale in enumerate(presets["sales"]):
        if sale["id"] == sale_id:
            found = True
            presets["sales"].pop(i)
            break
    
    if not found:
        raise HTTPException(status_code=404, detail=f"销售组ID '{sale_id}' 不存在")
    
    write_presets(presets)
    return {"message": "销售组删除成功"}

@router.delete("/sales/batch")
async def batch_delete_sales(request: SalesBatchDeleteRequest):
    presets = read_presets()
    
    # 确保sales字段存在
    if "sales" not in presets:
        presets["sales"] = []
    
    # 记录删除前的数量
    before_count = len(presets["sales"])
    
    # 过滤掉要删除的销售组
    presets["sales"] = [sale for sale in presets["sales"] if sale["id"] not in request.ids]
    
    # 计算删除的数量
    deleted_count = before_count - len(presets["sales"])
    
    write_presets(presets)
    return {"message": f"成功删除 {deleted_count} 个销售组"}

@router.post("/sales/import")
async def import_sales(config: SalesImportConfig):
    presets = read_presets()
    
    # 确保sales字段存在
    if "sales" not in presets:
        presets["sales"] = []
    
    # 导入数据
    imported_count = 0
    for item in config.data:
        # 检查是否已存在相同分组
        existing_index = None
        for i, sale in enumerate(presets["sales"]):
            if sale["group"] == item.group:
                existing_index = i
                break
        
        # 生成唯一ID
        import uuid
        sale_id = str(uuid.uuid4())
        
        # 如果存在且允许覆盖，则更新
        if existing_index is not None and config.overwrite:
            presets["sales"][existing_index] = {
                "id": presets["sales"][existing_index]["id"],  # 保留原ID
                "group": item.group,
                "leader": item.leader,
                "members": item.members
            }
            imported_count += 1
        # 如果不存在，则添加
        elif existing_index is None:
            presets["sales"].append({
                "id": sale_id,
                "group": item.group,
                "leader": item.leader,
                "members": item.members
            })
            imported_count += 1
    
    write_presets(presets)
    return {"message": f"成功导入 {imported_count} 个销售组", "imported_count": imported_count}

# 读取电商店铺预设值
def read_ecommerce_stores():
    try:
        if not os.path.exists(ECOMMERCE_STORES_FILE):
            # 如果文件不存在，创建默认文件
            default_stores = {"stores": []}
            with open(ECOMMERCE_STORES_FILE, "w", encoding="utf-8") as f:
                json.dump(default_stores, f, ensure_ascii=False, indent=2)
            return default_stores
        
        with open(ECOMMERCE_STORES_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)
            return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取电商店铺预设值失败: {str(e)}")

# 写入电商店铺预设值
def write_ecommerce_stores(data):
    try:
        with open(ECOMMERCE_STORES_FILE, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"写入电商店铺预设值失败: {str(e)}")

# 获取所有电商店铺预设值
@router.get("/ecommerce-stores")
async def get_ecommerce_stores():
    try:
        stores = read_ecommerce_stores()
        return stores["stores"]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取电商店铺预设值失败: {str(e)}")

# 添加电商店铺预设值
@router.post("/ecommerce-stores")
async def add_ecommerce_store(item: EcommerceStoreItem):
    stores = read_ecommerce_stores()
    
    # 生成唯一ID
    import uuid
    store_id = str(uuid.uuid4())
    
    # 创建新店铺对象
    new_store = {
        "id": store_id,
        "platform": item.platform,
        "stores": item.stores
    }
    
    stores["stores"].append(new_store)
    write_ecommerce_stores(stores)
    return {"message": "电商店铺添加成功", "id": store_id}

# 更新电商店铺预设值
@router.put("/ecommerce-stores")
async def update_ecommerce_store(item: EcommerceStoreUpdateItem):
    stores = read_ecommerce_stores()
    
    # 查找要更新的店铺
    found = False
    for i, store in enumerate(stores["stores"]):
        if store["id"] == item.id:
            found = True
            stores["stores"][i] = {
                "id": item.id,
                "platform": item.platform,
                "stores": item.stores
            }
            break
    
    if not found:
        raise HTTPException(status_code=404, detail="电商店铺不存在")
    
    write_ecommerce_stores(stores)
    return {"message": "电商店铺更新成功"}

# 删除电商店铺预设值
@router.delete("/ecommerce-stores/{store_id}")
async def delete_ecommerce_store(store_id: str):
    stores = read_ecommerce_stores()
    
    # 查找要删除的店铺
    found = False
    for i, store in enumerate(stores["stores"]):
        if store["id"] == store_id:
            found = True
            stores["stores"].pop(i)
            break
    
    if not found:
        raise HTTPException(status_code=404, detail="电商店铺不存在")
    
    write_ecommerce_stores(stores)
    return {"message": "电商店铺删除成功"}

# 批量删除电商店铺预设值
@router.post("/ecommerce-stores/batch-delete")
async def batch_delete_ecommerce_stores(request: EcommerceStoreBatchDeleteRequest):
    stores = read_ecommerce_stores()
    
    # 记录删除的数量
    deleted_count = 0
    
    # 遍历要删除的店铺ID
    for store_id in request.items:
        # 查找要删除的店铺
        for i, store in enumerate(stores["stores"]):
            if store["id"] == store_id:
                stores["stores"].pop(i)
                deleted_count += 1
                break
    
    write_ecommerce_stores(stores)
    return {"message": f"成功删除{deleted_count}个电商店铺"} 