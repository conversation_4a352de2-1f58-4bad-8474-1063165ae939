from fastapi import APIRouter, Depends, Query, HTTPException, Path, Request
from sqlalchemy.orm import Session
from typing import Optional
from database import get_db
from models import ClueSheet, User, UserPermissionFrontend,SalaCrm
from datetime import datetime
from sqlalchemy import func
from auth import get_current_user

router = APIRouter()

@router.get("/api/recycle-bin")
def get_deleted_leads(
    request: Request,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = "",
    db: Session = Depends(get_db)
):
    """获取已删除的线索数据"""
    # 获取当前用户
    current_user = get_current_user(request, db)
    if not current_user:
        raise HTTPException(status_code=401, detail="未登录或登录已过期")
    
    skip = (page - 1) * size
    query = db.query(ClueSheet).filter(ClueSheet.is_deleted == True)
    
    # 根据用户角色和权限过滤数据
    if current_user.is_admin != 0:  # 非超级管理员
        # 获取用户权限设置
        permission = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.department == current_user.department,
            UserPermissionFrontend.identity == current_user.identity
        ).first()
        
        # 默认只能查看自己的记录
        if not permission or permission.recycle_bin != 0:
            query = query.filter(ClueSheet.registrar == current_user.name)
        else:
            # 根据权限级别过滤
            if permission.check_all_bin == 0:
                # 可查看同部门所有记录
                # 获取同部门用户名单
                dept_users = db.query(User.name).filter(User.department == current_user.department).all()
                dept_user_names = [user.name for user in dept_users]
                query = query.filter(ClueSheet.registrar.in_(dept_user_names))
            elif permission.check_member_bin == 0:
                # 可查看同部门同分组记录
                # 获取同部门同分组用户名单
                group_users = db.query(User.name).filter(
                    User.department == current_user.department,
                    User.group_name == current_user.group_name
                ).all()
                group_user_names = [user.name for user in group_users]
                query = query.filter(ClueSheet.registrar.in_(group_user_names))
            elif permission.check_mine_bin == 0:
                # 只能查看自己的记录
                query = query.filter(ClueSheet.registrar == current_user.name)
            else:
                # 如果所有权限检查都不通过，默认只能查看自己的记录
                query = query.filter(ClueSheet.registrar == current_user.name)
    
    if search:
        search = f"%{search}%"
        query = query.filter(
            ClueSheet.ID.like(search) |
            ClueSheet.wangwang_id.like(search) |
            ClueSheet.phone_number.like(search) |
            ClueSheet.wechat_id.like(search) |
            ClueSheet.wechat_name.like(search) |
            ClueSheet.store.like(search) |
            ClueSheet.registrar.like(search) |
            ClueSheet.contact_person.like(search) |
            ClueSheet.anchor.like(search) |
            ClueSheet.shift.like(search) |
            ClueSheet.remarks.like(search)
        )
    
    total = query.count()
    items = query.order_by(ClueSheet.delete_time.desc()).offset(skip).limit(size).all()
    
    # 解密查询结果中的敏感数据用于显示
    decrypted_items = []
    for item in items:
        # 调用模型中的解密方法
        item.decrypt_sensitive_data()
        decrypted_items.append(item)
    
    return {
        "items": decrypted_items,
        "total": total
    }

@router.post("/api/recycle-bin/{lead_id}/restore")
def restore_lead(
    request: Request,
    lead_id: str = Path(..., description="要恢复的线索ID"),
    db: Session = Depends(get_db)
):
    """恢复已删除的线索"""
    # 获取当前用户
    current_user = get_current_user(request, db)
    if not current_user:
        raise HTTPException(status_code=401, detail="未登录或登录已过期")
    
    lead = db.query(ClueSheet).filter(ClueSheet.ID == lead_id, ClueSheet.is_deleted == True).first()
    if not lead:
        raise HTTPException(status_code=404, detail="找不到该线索或线索未被删除")
    
    # 检查权限：只有管理员或超级管理员，或者是该记录的登记人才能恢复
    if current_user.is_admin not in [0, 1] and lead.registrar != current_user.name:
        raise HTTPException(status_code=403, detail="没有权限恢复此记录")

    sala_crm_data = db.query(SalaCrm).filter(SalaCrm.ID == lead_id, SalaCrm.is_deleted == False).first()
    if sala_crm_data:
        sala_crm_data.is_deleted = False

    lead.is_deleted = False
    lead.delete_time = None
    db.commit()
    
    return {"message": f"线索 {lead_id} 已成功恢复"}

@router.delete("/api/recycle-bin/{lead_id}")
def permanently_delete_lead(
    request: Request,
    lead_id: str = Path(..., description="要彻底删除的线索ID"),
    db: Session = Depends(get_db)
):
    """彻底删除线索（从数据库中移除）"""
    # 获取当前用户
    current_user = get_current_user(request, db)
    if not current_user:
        raise HTTPException(status_code=401, detail="未登录或登录已过期")
    
    lead = db.query(ClueSheet).filter(ClueSheet.ID == lead_id, ClueSheet.is_deleted == True).first()
    if not lead:
        raise HTTPException(status_code=404, detail="找不到该线索或线索未被删除")
    
    # 检查权限：只有管理员或超级管理员，或者是该记录的登记人才能删除
    if current_user.is_admin not in [0, 1] and lead.registrar != current_user.name:
        raise HTTPException(status_code=403, detail="没有权限删除此记录")
    
    db.delete(lead)
    db.commit()
    
    return {"message": f"线索 {lead_id} 已被彻底删除"} 