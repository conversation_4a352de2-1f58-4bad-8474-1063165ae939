import os
import time
import asyncio
from typing import Optional
from datetime import datetime, date, timedelta
from threading import Thread, Event
import threading

from fastapi import APIRouter, Depends, HTTPException, Request, BackgroundTasks, Query
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session
from database import get_db
from models import DistributionRulesTask, User, DistributionRule, DistributionQueue, ClueSheet
from pydantic import BaseModel
from auth import get_current_user, base64_decrypt_data

router = APIRouter()

# 全局变量管理任务状态
task_manager = {
    "current_task": None,  # 当前运行的任务线程
    "stop_event": None,    # 停止信号
    "task_status": False   # 任务运行状态
}

# Pydantic模型定义
class TaskUpdateRequest(BaseModel):
    """修改任务启动时间的请求模型"""
    date_time: datetime
    status: Optional[int] = None

class TaskResponse(BaseModel):
    """任务信息响应模型"""
    id: int
    date_time: datetime
    status: int

class TaskStatusRequest(BaseModel):
    """任务状态控制请求模型"""
    status: int  # 0: 关闭，1: 开启

class TaskTimeRequest(BaseModel):
    """任务时间设置请求模型"""
    time: str  # 格式: "HH:MM"
    date: Optional[str] = None  # 格式: "YYYY-MM-DD", 可选，默认为今天

def generate_distribution_queue_sync(db: Session, queue_date: date, channel_type: str):
    """
    同步版本的队列生成函数
    根据分发规则生成分发队列
    """
    try:
        print(f"开始生成分发队列: 日期={queue_date}, 渠道类型={channel_type}")
        
        # 查询指定日期和渠道类型的规则
        rules = db.query(DistributionRule).filter(
            DistributionRule.date == queue_date,
            DistributionRule.channel_type == channel_type
        ).all()
        
        if not rules:
            print(f"未找到 {queue_date} {channel_type} 的分发规则")
            return False
        
        # 删除已有的队列数据
        db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type
        ).delete()
        
        # 按组聚合规则并生成队列
        from collections import defaultdict
        groups = defaultdict(list)
        for rule in rules:
            groups[rule.group_name].append(rule)
        
        # 计算每个组的预计接待总数
        group_quotas = {}
        for group_name, group_rules in groups.items():
            total_expected = sum(rule.expected_total for rule in group_rules)
            group_quotas[group_name] = total_expected
        
        # 生成免费和付费队列
        for queue_type in ["free", "paid"]:
            position = 1
            for group_name, group_rules in groups.items():
                # 按时间段排序组内规则
                sorted_rules = sorted(group_rules, key=lambda x: x.time_range)
                
                # 计算该组在此队列类型的预期数量
                if queue_type == "free":
                    group_expected = sum(rule.expected_free for rule in group_rules)
                else:
                    group_expected = sum(rule.expected_paid for rule in group_rules if rule.expected_paid > 0)
                
                # 为该组生成队列项
                for _ in range(group_expected):
                    # 选择组内成员（循环分配）
                    rule_index = (position - 1) % len(sorted_rules)
                    rule = sorted_rules[rule_index]
                    
                    queue_item = DistributionQueue(
                        queue_date=queue_date,
                        channel_type=channel_type,
                        queue_type=queue_type,
                        position=position,
                        group_name=group_name,
                        leader=rule.leader,
                        member=rule.member,
                        time_slot=rule.time_range,
                        status="pending",
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        time_slot_beg=rule.time_range_start,
                        time_slot_end=rule.time_range_end
                    )
                    db.add(queue_item)
                    position += 1
        
        db.commit()
        print(f"成功生成分发队列: 日期={queue_date}, 渠道类型={channel_type}")
        return True
        
    except Exception as e:
        db.rollback()
        print(f"生成分发队列失败: {e}")
        return False

def scheduled_task_worker(task_id: int, target_time: datetime, stop_event: Event):
    """
    定时任务工作线程
    使用time.sleep()实现延迟执行
    """
    try:
        print(f"定时任务启动: ID={task_id}, 目标时间={target_time}")
        
        # 计算需要等待的时间（秒）
        current_time = datetime.now()
        wait_seconds = (target_time - current_time).total_seconds()
        
        if wait_seconds <= 0:
            print(f"目标时间已过，立即执行任务")
            wait_seconds = 0
        else:
            print(f"等待 {wait_seconds} 秒后执行任务")
        
        # 分段等待，每10秒检查一次停止信号
        while wait_seconds > 0 and not stop_event.is_set():
            sleep_time = min(10, wait_seconds)
            time.sleep(sleep_time)
            wait_seconds -= sleep_time
        
        # 检查是否被停止
        if stop_event.is_set():
            print(f"任务被停止: ID={task_id}")
            return
        
        # 执行队列生成任务
        print(f"开始执行队列生成任务: ID={task_id}")
        
        # 获取数据库连接
        from database import SessionLocal
        db = SessionLocal()
        
        try:
            # 获取任务日期（目标时间的日期）
            task_date = target_time.date()
            
            # 为电商渠道和新媒体渠道生成队列
            success_ecommerce = generate_distribution_queue_sync(db, task_date, "电商渠道")
            success_newmedia = generate_distribution_queue_sync(db, task_date, "新媒体渠道")
            
            if success_ecommerce or success_newmedia:
                print(f"队列生成任务完成: 电商渠道={'成功' if success_ecommerce else '失败'}, 新媒体渠道={'成功' if success_newmedia else '失败'}")
                
                # 队列生成完成后的处理
                print("队列生成完成，线索分发将在后台处理")
            else:
                print(f"队列生成任务失败")
                
        finally:
            db.close()
        
    except Exception as e:
        print(f"定时任务执行异常: {e}")
    finally:
        # 清理任务状态
        task_manager["current_task"] = None
        task_manager["stop_event"] = None
        task_manager["task_status"] = False
        print(f"定时任务结束: ID={task_id}")

def start_scheduled_task(task_id: int, target_time: datetime):
    """启动定时任务"""
    # 停止已有任务
    stop_current_task()
    
    # 创建新的停止信号
    stop_event = Event()
    task_manager["stop_event"] = stop_event
    
    # 创建并启动新任务线程
    task_thread = Thread(
        target=scheduled_task_worker,
        args=(task_id, target_time, stop_event),
        daemon=True
    )
    
    task_manager["current_task"] = task_thread
    task_manager["task_status"] = True
    
    task_thread.start()
    print(f"定时任务已启动: ID={task_id}, 目标时间={target_time}")

def stop_current_task():
    """停止当前运行的定时任务"""
    if task_manager["stop_event"]:
        task_manager["stop_event"].set()
        print("已发送停止信号")
    
    if task_manager["current_task"] and task_manager["current_task"].is_alive():
        # 等待任务线程结束（最多等待5秒）
        task_manager["current_task"].join(timeout=5)
        print("定时任务已停止")
    
    task_manager["current_task"] = None
    task_manager["stop_event"] = None
    task_manager["task_status"] = False

# API接口实现

@router.post("/api/task/set-time")
def set_task_time(
    request: TaskTimeRequest,
    db: Session = Depends(get_db)
):
    """
    设置任务时间接口（适配前端页面）
    """
    try:
        # 解析日期，如果没有提供日期则使用今天
        if request.date:
            target_date = datetime.strptime(request.date, "%Y-%m-%d").date()
        else:
            target_date = datetime.now().date()
        
        # 解析时间
        time_parts = request.time.split(":")
        if len(time_parts) != 2:
            raise HTTPException(status_code=400, detail="时间格式错误，请使用 HH:MM 格式")
        
        hour = int(time_parts[0])
        minute = int(time_parts[1])
        
        if not (0 <= hour <= 23) or not (0 <= minute <= 59):
            raise HTTPException(status_code=400, detail="时间值超出有效范围")
        
        # 构建完整的datetime对象
        target_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=hour, minute=minute))
        
        # 查询当天的任务记录
        task = db.query(DistributionRulesTask).filter(
            func.DATE(DistributionRulesTask.date_time) == target_date
        ).first()
        
        if not task:
            # 如果当天没有记录，创建新记录
            task = DistributionRulesTask(
                date_time=target_datetime,
                status=0  # 默认关闭状态
            )
            db.add(task)
        else:
            # 更新现有记录的时间
            task.date_time = target_datetime
        
        db.commit()
        db.refresh(task)
        
        # 如果任务状态为开启，重新调度定时任务
        if task.status == 1:
            start_scheduled_task(task.id, task.date_time)
        
        return {
            "success": True,
            "message": "任务时间设置成功",
            "data": {
                "id": task.id,
                "date_time": task.date_time.isoformat(),
                "status": task.status,
                "formatted_time": task.date_time.strftime("%H:%M")
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"日期或时间格式错误: {str(e)}")
    except Exception as e:
        db.rollback()
        print(f"设置任务时间失败: {e}")
        raise HTTPException(status_code=500, detail=f"设置任务时间失败: {str(e)}")

@router.post("/api/task/toggle-status")
def toggle_task_status(
    request: TaskStatusRequest,
    db: Session = Depends(get_db)
):
    """
    控制任务开关接口
    开启或关闭定时任务
    """
    try:
        # 获取今天的日期
        today = datetime.now().date()
        
        # 查询今天的任务记录
        task = db.query(DistributionRulesTask).filter(
            func.DATE(DistributionRulesTask.date_time) == today
        ).first()
        
        if not task:
            # 如果今天没有记录，创建新记录
            task = DistributionRulesTask(
                date_time=datetime.now().replace(hour=9, minute=0, second=0, microsecond=0),  # 默认9点
                status=request.status
            )
            db.add(task)
        else:
            # 更新现有记录的状态
            task.status = request.status
        
        db.commit()
        db.refresh(task)
        
        # 根据状态控制定时任务
        if request.status == 1:
            # 开启任务
            start_scheduled_task(task.id, task.date_time)
            message = "自动生成队列已开启"
        else:
            # 关闭任务
            stop_current_task()
            message = "自动生成队列已关闭"
        
        return {
            "success": True,
            "message": message,
            "data": {
                "id": task.id,
                "date_time": task.date_time.isoformat(),
                "status": task.status,
                "formatted_time": task.date_time.strftime("%H:%M"),
                "task_running": task_manager["task_status"]
            }
        }
        
    except Exception as e:
        db.rollback()
        print(f"切换任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"切换任务状态失败: {str(e)}")

@router.get("/api/task/info")
def get_task_info(
    date_riqi: str = Query(..., description="日期字符串，格式：YYYY-MM-DD"),
    db: Session = Depends(get_db)
):
    """
    查询某天任务信息接口
    根据日期查询任务记录
    """
    try:
        # 解析日期字符串
        try:
            query_date = datetime.strptime(date_riqi, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM-DD 格式")
        
        # 查询指定日期的任务记录
        task = db.query(DistributionRulesTask).filter(
            func.DATE(DistributionRulesTask.date_time) == query_date
        ).first()
        
        if not task:
            return {
                "success": True,
                "message": f"未找到 {date_riqi} 的任务记录",
                "data": None
            }
        
        # 检查当前任务运行状态
        current_task_running = (
            task_manager["task_status"] and 
            task_manager["current_task"] and 
            task_manager["current_task"].is_alive()
        )
        
        return {
            "success": True,
            "message": f"找到 {date_riqi} 的任务记录",
            "data": {
                "id": task.id,
                "date_time": task.date_time.isoformat(),
                "status": task.status,
                "formatted_time": task.date_time.strftime("%H:%M"),
                "task_running": current_task_running
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"查询任务信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询任务信息失败: {str(e)}")

@router.get("/api/task/today")
def get_today_task_info(db: Session = Depends(get_db)):
    """
    获取今天的任务信息（用于页面初始化）
    """
    try:
        today = datetime.now().date()
        
        # 查询今天的任务记录
        task = db.query(DistributionRulesTask).filter(
            func.DATE(DistributionRulesTask.date_time) == today
        ).first()
        
        if not task:
            # 如果今天没有记录，返回默认值
            return {
                "success": True,
                "message": "今天无任务记录，返回默认值",
                "data": {
                    "id": None,
                    "date_time": None,
                    "status": 0,
                    "formatted_time": "09:00",
                    "task_running": False
                }
            }
        
        # 检查当前任务运行状态
        current_task_running = (
            task_manager["task_status"] and 
            task_manager["current_task"] and 
            task_manager["current_task"].is_alive()
        )
        
        return {
            "success": True,
            "message": "获取今天任务信息成功",
            "data": {
                "id": task.id,
                "date_time": task.date_time.isoformat(),
                "status": task.status,
                "formatted_time": task.date_time.strftime("%H:%M"),
                "task_running": current_task_running
            }
        }
        
    except Exception as e:
        print(f"获取今天任务信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取今天任务信息失败: {str(e)}")

@router.get("/api/task/status")
def get_task_status():
    """
    获取当前任务运行状态
    """
    return {
        "success": True,
        "message": "获取任务状态成功",
        "data": {
            "task_running": task_manager["task_status"],
            "has_current_task": task_manager["current_task"] is not None,
            "thread_alive": (
                task_manager["current_task"].is_alive() 
                if task_manager["current_task"] else False
            )
        }
    }

@router.post("/api/task/manual-trigger")
def manual_trigger_task(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    手动触发队列生成任务（用于测试）
    """
    try:
        current_date = datetime.now().date()
        
        # 为电商渠道和新媒体渠道生成队列
        success_ecommerce = generate_distribution_queue_sync(db, current_date, "电商渠道")
        success_newmedia = generate_distribution_queue_sync(db, current_date, "新媒体渠道")
        
        if success_ecommerce or success_newmedia:
            return {
                "success": True,
                "message": "手动触发队列生成成功",
                "data": {
                    "date": current_date.isoformat(),
                    "ecommerce_success": success_ecommerce,
                    "newmedia_success": success_newmedia
                }
            }
        else:
            return {
                "success": False,
                "message": "手动触发队列生成失败",
                "data": {
                    "date": current_date.isoformat(),
                    "ecommerce_success": success_ecommerce,
                    "newmedia_success": success_newmedia
                }
            }
            
    except Exception as e:
        print(f"手动触发任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"手动触发任务失败: {str(e)}")

# 应用启动时自动恢复任务状态
def restore_tasks_on_startup(db: Session):
    """
    应用启动时恢复活跃的任务
    """
    try:
        today = datetime.now().date()
        
        # 查询今天状态为开启的任务
        task = db.query(DistributionRulesTask).filter(
            func.DATE(DistributionRulesTask.date_time) == today,
            DistributionRulesTask.status == 1
        ).first()
        
        if task:
            # 如果任务时间还未到，重新启动定时任务
            if task.date_time > datetime.now():
                start_scheduled_task(task.id, task.date_time)
                print(f"恢复定时任务: ID={task.id}, 时间={task.date_time}")
            else:
                print(f"任务时间已过，不恢复: ID={task.id}, 时间={task.date_time}")
        else:
            print("没有需要恢复的活跃任务")
            
    except Exception as e:
        print(f"恢复任务失败: {e}")

