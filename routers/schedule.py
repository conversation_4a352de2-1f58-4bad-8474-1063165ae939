import logging
# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

from fastapi import APIRouter, Depends, HTTPException, Query, Body, Request
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from database import get_db
from models import Channel, Store, Anchor, Schedule, ClueSheet
from datetime import datetime, date, time, timedelta
from sqlalchemy import func, and_, or_, text
import json
from fastapi.responses import JSONResponse
from collections import defaultdict
from fastapi.responses import RedirectResponse
from auth import get_current_user, check_backend_permission
from fastapi.templating import Jinja2Templates

router = APIRouter(tags=["schedule"])

# 渠道管理API
@router.get("/channels")
def get_channels(db: Session = Depends(get_db)):
    """获取所有渠道"""
    channels = db.query(Channel).all()
    return [channel.to_dict() for channel in channels]

@router.post("/channels")
def create_channel(channel_data: Dict[str, Any], db: Session = Depends(get_db)):
    """创建渠道"""
    try:
        channel = Channel(
            name=channel_data.get("name"),
            description=channel_data.get("description")
        )
        db.add(channel)
        db.commit()
        db.refresh(channel)
        return channel.to_dict()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建渠道失败: {str(e)}")

@router.put("/channels/{channel_id}")
def update_channel(channel_id: int, channel_data: Dict[str, Any], db: Session = Depends(get_db)):
    """更新渠道"""
    try:
        channel = db.query(Channel).filter(Channel.id == channel_id).first()
        if not channel:
            raise HTTPException(status_code=404, detail="渠道不存在")
        
        channel.name = channel_data.get("name", channel.name)
        channel.description = channel_data.get("description", channel.description)
        channel.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(channel)
        return channel.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新渠道失败: {str(e)}")

@router.delete("/channels/{channel_id}")
def delete_channel(channel_id: int, db: Session = Depends(get_db)):
    """删除渠道"""
    try:
        channel = db.query(Channel).filter(Channel.id == channel_id).first()
        if not channel:
            raise HTTPException(status_code=404, detail="渠道不存在")
        
        # 检查是否有关联的店铺
        stores_count = db.query(Store).filter(Store.channel_id == channel_id).count()
        if stores_count > 0:
            raise HTTPException(status_code=400, detail="该渠道下有关联的店铺，无法删除")
        
        db.delete(channel)
        db.commit()
        return {"message": "渠道删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除渠道失败: {str(e)}")

# 店铺管理API
@router.get("/stores")
def get_stores(channel_id: Optional[int] = None, db: Session = Depends(get_db)):
    """获取店铺列表，可按渠道筛选"""
    query = db.query(Store)
    if channel_id:
        query = query.filter(Store.channel_id == channel_id)
    
    stores = query.all()
    return [store.to_dict() for store in stores]

@router.post("/stores")
def create_store(store_data: Dict[str, Any], db: Session = Depends(get_db)):
    """创建店铺"""
    try:
        # 如果提供了channel_id，检查渠道是否存在
        channel_id = store_data.get("channel_id")
        if channel_id:
            channel = db.query(Channel).filter(Channel.id == channel_id).first()
            if not channel:
                raise HTTPException(status_code=404, detail="指定的渠道不存在")
        
        store = Store(
            name=store_data.get("name"),
            channel_id=channel_id,
            description=store_data.get("description")
        )
        db.add(store)
        db.commit()
        db.refresh(store)
        return store.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建店铺失败: {str(e)}")

@router.put("/stores/{store_id}")
def update_store(store_id: int, store_data: Dict[str, Any], db: Session = Depends(get_db)):
    """更新店铺"""
    try:
        store = db.query(Store).filter(Store.id == store_id).first()
        if not store:
            raise HTTPException(status_code=404, detail="店铺不存在")
        
        # 如果提供了channel_id，检查渠道是否存在
        channel_id = store_data.get("channel_id")
        if channel_id:
            channel = db.query(Channel).filter(Channel.id == channel_id).first()
            if not channel:
                raise HTTPException(status_code=404, detail="指定的渠道不存在")
            store.channel_id = channel_id
        
        store.name = store_data.get("name", store.name)
        store.description = store_data.get("description", store.description)
        store.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(store)
        return store.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新店铺失败: {str(e)}")

@router.delete("/stores/{store_id}")
def delete_store(store_id: int, db: Session = Depends(get_db)):
    """删除店铺"""
    try:
        store = db.query(Store).filter(Store.id == store_id).first()
        if not store:
            raise HTTPException(status_code=404, detail="店铺不存在")
        
        # 检查是否有关联的排班
        schedules_count = db.query(Schedule).filter(Schedule.store_id == store_id).count()
        if schedules_count > 0:
            raise HTTPException(status_code=400, detail="该店铺下有关联的排班，无法删除")
        
        db.delete(store)
        db.commit()
        return {"message": "店铺删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除店铺失败: {str(e)}")

# 主播管理API
@router.get("/anchors")
def get_anchors(status: Optional[str] = None, db: Session = Depends(get_db)):
    """获取主播列表，可按状态筛选"""
    query = db.query(Anchor)
    if status:
        query = query.filter(Anchor.status == status)
    
    anchors = query.all()
    return [anchor.to_dict() for anchor in anchors]

@router.post("/anchors")
def create_anchor(anchor_data: Dict[str, Any], db: Session = Depends(get_db)):
    """创建主播"""
    try:
        anchor = Anchor(
            name=anchor_data.get("name"),
            contact=anchor_data.get("contact"),
            status=anchor_data.get("status", "active")
        )
        db.add(anchor)
        db.commit()
        db.refresh(anchor)
        return anchor.to_dict()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建主播失败: {str(e)}")

@router.put("/anchors/{anchor_id}")
def update_anchor(anchor_id: int, anchor_data: Dict[str, Any], db: Session = Depends(get_db)):
    """更新主播"""
    try:
        anchor = db.query(Anchor).filter(Anchor.id == anchor_id).first()
        if not anchor:
            raise HTTPException(status_code=404, detail="主播不存在")
        
        anchor.name = anchor_data.get("name", anchor.name)
        anchor.contact = anchor_data.get("contact", anchor.contact)
        anchor.status = anchor_data.get("status", anchor.status)
        anchor.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(anchor)
        return anchor.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新主播失败: {str(e)}")

@router.delete("/anchors/{anchor_id}")
def delete_anchor(anchor_id: int, db: Session = Depends(get_db)):
    """删除主播"""
    try:
        anchor = db.query(Anchor).filter(Anchor.id == anchor_id).first()
        if not anchor:
            raise HTTPException(status_code=404, detail="主播不存在")
        
        # 检查是否有关联的排班
        schedules_count = db.query(Schedule).filter(Schedule.anchor_id == anchor_id).count()
        if schedules_count > 0:
            raise HTTPException(status_code=400, detail="该主播有关联的排班，无法删除")
        
        db.delete(anchor)
        db.commit()
        return {"message": "主播删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除主播失败: {str(e)}")

# 排班管理API
@router.get("/schedules")
def get_schedules(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    store_id: Optional[str] = None,
    anchor_id: Optional[str] = None,
    channel_id: Optional[str] = None,
    shift: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取排班列表，支持多种筛选条件"""
    query = db.query(Schedule)
    
    # 应用筛选条件
    if start_date:
        query = query.filter(Schedule.date >= start_date)
    if end_date:
        query = query.filter(Schedule.date <= end_date)
    if store_id:
        query = query.filter(Schedule.store_id == store_id)
    if anchor_id:
        query = query.filter(Schedule.anchor_id == anchor_id)
    if channel_id:
        query = query.filter(Schedule.channel_id == channel_id)
    if shift:
        query = query.filter(Schedule.shift == shift)
    
    # 按日期和开始时间排序
    query = query.order_by(Schedule.date, Schedule.start_time)
    
    schedules = query.all()
    return [schedule.to_dict() for schedule in schedules]

@router.get("/schedules/day/{date_str}")
def get_schedules_by_day(date_str: str, db: Session = Depends(get_db)):
    """获取指定日期的排班"""
    try:
        schedule_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        schedules = db.query(Schedule).filter(Schedule.date == schedule_date).order_by(Schedule.start_time).all()
        return [schedule.to_dict() for schedule in schedules]
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用YYYY-MM-DD格式")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取排班失败: {str(e)}")

@router.post("/schedules")
def create_schedules(schedule_data: Any = Body(...), db: Session = Depends(get_db)):
    """批量创建排班，支持灵活的数据格式，实现排班合并而非替换"""
    try:
        # 检查请求数据格式，支持列表或包含schedules列表的字典
        schedules_list = []
        if isinstance(schedule_data, list):
            # 如果直接是列表格式
            schedules_list = schedule_data
        elif isinstance(schedule_data, dict) and "schedules" in schedule_data and isinstance(schedule_data["schedules"], list):
            # 如果是包含schedules列表的字典格式
            schedules_list = schedule_data["schedules"]
        else:
            raise HTTPException(status_code=400, detail="请求数据格式错误，应为排班列表或包含schedules列表的字典")
        
        created_schedules = []
        to_be_created_schedules = []  # 存储所有待创建的排班，用于冲突检查
        
        # 第一阶段：验证并准备数据
        print("第一阶段：验证并准备数据")
        for schedule_item in schedules_list:
            # 获取必要字段
            schedule_id = schedule_item.get("id")  # 获取排班ID，用于判断是否为编辑操作
            anchor_id = schedule_item.get("anchor_id")
            store_id = schedule_item.get("store_id")
            channel_id = schedule_item.get("channel_id")
            shift = schedule_item.get("shift")
            
            # 验证必要字段
            if not anchor_id:
                raise HTTPException(status_code=400, detail="主播ID不能为空")
            if not store_id:
                raise HTTPException(status_code=400, detail="店铺ID不能为空")
            if not channel_id:
                raise HTTPException(status_code=400, detail="渠道ID不能为空")
            if not shift:
                raise HTTPException(status_code=400, detail="班次不能为空")
            
            # 对于全天班次，确保休息时长为0
            rest_duration = 0 if shift == 'fullday' else float(schedule_item.get("rest_duration", 1.5))
            
            # 解析日期和时间
            try:
                schedule_date = datetime.strptime(schedule_item.get("date"), "%Y-%m-%d").date()
                start_time_str = schedule_item.get("start_time")
                end_time_str = schedule_item.get("end_time")
                
                # 处理时间格式，支持HH:MM和HH:MM:SS
                if ":" in start_time_str:
                    parts = start_time_str.split(":")
                    if len(parts) == 2:
                        start_time = datetime.strptime(start_time_str, "%H:%M").time()
                    else:
                        start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
                else:
                    raise ValueError("时间格式错误")
                
                if ":" in end_time_str:
                    parts = end_time_str.split(":")
                    if len(parts) == 2:
                        end_time = datetime.strptime(end_time_str, "%H:%M").time()
                    else:
                        end_time = datetime.strptime(end_time_str, "%H:%M:%S").time()
                else:
                    raise ValueError("时间格式错误")
                
            except ValueError as e:
                raise HTTPException(status_code=400, detail=f"日期或时间格式错误: {str(e)}")
            
            # 计算时长（小时）
            start_datetime = datetime.combine(schedule_date, start_time)
            end_datetime = datetime.combine(schedule_date, end_time)
            if end_datetime <= start_datetime:
                # 如果结束时间小于开始时间，认为是跨天排班，结束时间加一天
                end_datetime = datetime.combine(schedule_date + timedelta(days=1), end_time)
            
            # 计算总时长（小时）
            total_duration_hours = round((end_datetime - start_datetime).total_seconds() / 3600, 1)
            
            # 减去休息时长
            rest_duration = 0 if shift == 'fullday' else float(schedule_item.get("rest_duration", 1.5))
            duration_hours = round(total_duration_hours - rest_duration, 1)
            
            # 如果是编辑操作（有ID），则查找并更新现有排班
            if schedule_id:
                print(f"编辑现有排班: ID={schedule_id}")
                existing_schedule = db.query(Schedule).filter(Schedule.id == schedule_id).first()
                
                if existing_schedule:
                    # 更新现有排班
                    existing_schedule.date = schedule_date
                    existing_schedule.anchor_id = anchor_id
                    existing_schedule.store_id = store_id
                    existing_schedule.channel_id = channel_id
                    existing_schedule.shift = shift
                    existing_schedule.start_time = start_time
                    existing_schedule.end_time = end_time
                    existing_schedule.duration = duration_hours
                    existing_schedule.rest_duration = rest_duration
                    existing_schedule.room = schedule_item.get("room")
                    existing_schedule.expected_count = schedule_item.get("expected_count", 0)
                    existing_schedule.real_count = schedule_item.get("real_count", 0)
                    existing_schedule.live_count = schedule_item.get("live_count", 0)
                    existing_schedule.notes = schedule_item.get("notes")
                    existing_schedule.updated_at = datetime.utcnow()
                    
                    created_schedules.append(existing_schedule)
                    print(f"更新排班成功: ID={schedule_id}")
                    continue
                else:
                    print(f"未找到ID为{schedule_id}的排班记录，将创建新排班")
            
            # 准备要创建的排班数据
            new_schedule_data = {
                "date": schedule_date,
                "anchor_id": anchor_id,
                "store_id": store_id,
                "channel_id": channel_id,
                "shift": shift,
                "start_time": start_time,
                "end_time": end_time,
                "duration": duration_hours,
                "rest_duration": rest_duration,
                "room": schedule_item.get("room"),
                "expected_count": schedule_item.get("expected_count", 0),
                "real_count": schedule_item.get("real_count", 0),
                "live_count": schedule_item.get("live_count", 0),
                "notes": schedule_item.get("notes"),
                "created_by": schedule_item.get("created_by")
            }
            
            to_be_created_schedules.append(new_schedule_data)
            
        # 第二阶段：批量检查冲突
        print("第二阶段：批量检查冲突")
        conflict_check_map = {}  # 用于检查内部冲突
        
        # 首先，检查待创建的排班之间是否有冲突
        for idx, schedule_data in enumerate(to_be_created_schedules):
            # 创建唯一键：日期_班次_渠道
            conflict_key = f"{schedule_data['date']}_{schedule_data['shift']}_{schedule_data['channel_id']}"
            
            if conflict_key in conflict_check_map:
                # 冲突错误
                first_conflicting_data = to_be_created_schedules[conflict_check_map[conflict_key]]
                error_msg = (
                    f"检测到排班表单内部冲突：\n"
                    f"日期:{schedule_data['date']}, "
                    f"班次:{schedule_data['shift']}, "
                    f"渠道:{schedule_data['channel_id']}\n"
                    f"冲突主播: {first_conflicting_data['anchor_id']} 与 {schedule_data['anchor_id']}"
                )
                print(f"内部冲突: {error_msg}")
                db.rollback()
                return JSONResponse(
                    status_code=400,
                    content={"detail": error_msg}
                )
            
            conflict_check_map[conflict_key] = idx
        
        # 然后，检查待创建的排班与数据库中现有排班的冲突
        for schedule_data in to_be_created_schedules:
            # 查询同一天相同店铺、渠道、班次的排班，不考虑主播
            existing_schedules = db.query(Schedule).filter(
                Schedule.date == schedule_data["date"],
                Schedule.store_id == schedule_data["store_id"],
                Schedule.channel_id == schedule_data["channel_id"],
                Schedule.shift == schedule_data["shift"]
            ).all()
            
            # 打印调试信息
            print(f"检查排班冲突: 日期={schedule_data['date']}, 店铺={schedule_data['store_id']}, 渠道={schedule_data['channel_id']}, 班次={schedule_data['shift']}")
            print(f"找到 {len(existing_schedules)} 条相同店铺、渠道、班次的排班")
            
            # 如果存在相同店铺、渠道、班次的排班，则报告冲突
            if existing_schedules:
                existing = existing_schedules[0]
                print(f"发现冲突: 相同的店铺、渠道和班次")
                
                # 获取冲突主播的名字
                try:
                    conflict_anchor = db.query(Anchor).filter(Anchor.id == existing.anchor_id).first()
                    conflict_anchor_name = conflict_anchor.name if conflict_anchor else "未知主播"
                except Exception as e:
                    print(f"获取冲突主播信息失败: {str(e)}")
                    conflict_anchor_name = "未知主播"
                
                error_msg = f"检测到排班冲突，日期: {existing.date}，班次: {existing.shift}，渠道: {existing.channel_id}，冲突主播: {conflict_anchor_name}"
                print(f"冲突错误: {error_msg}")
                
                # 在抛出异常前回滚事务
                db.rollback()
                
                # 使用return而不是raise，避免异步上下文管理器的问题
                return JSONResponse(
                    status_code=400,
                    content={"detail": error_msg}
                )
        
        # 第三阶段：创建排班
        print("第三阶段：创建排班")
        for schedule_data in to_be_created_schedules:
            # 如果没有找到冲突，创建新排班
            print(f"创建新排班: 主播={schedule_data['anchor_id']}, 店铺={schedule_data['store_id']}, 渠道={schedule_data['channel_id']}, 班次={schedule_data['shift']}")
            schedule = Schedule(
                date=schedule_data["date"],
                anchor_id=schedule_data["anchor_id"],
                store_id=schedule_data["store_id"],
                channel_id=schedule_data["channel_id"],
                shift=schedule_data["shift"],
                start_time=schedule_data["start_time"],
                end_time=schedule_data["end_time"],
                duration=schedule_data["duration"],
                rest_duration=schedule_data.get("rest_duration", 1.5),
                room=schedule_data["room"],
                expected_count=schedule_data["expected_count"],
                real_count=schedule_data["real_count"],
                live_count=schedule_data["live_count"],
                notes=schedule_data["notes"],
                created_by=schedule_data["created_by"]
            )
            
            db.add(schedule)
            created_schedules.append(schedule)
        
        db.commit()
        
        # 刷新所有创建的排班
        for schedule in created_schedules:
            db.refresh(schedule)
        
        return {"message": f"成功创建或更新{len(created_schedules)}条排班记录"}
    except HTTPException as he:
        print(f"HTTP异常: {he.detail}")
        db.rollback()
        raise
    except Exception as e:
        print(f"创建排班失败: {str(e)}")
        print(f"异常类型: {type(e).__name__}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建排班失败: {str(e)}")

@router.put("/schedules/{schedule_id}")
def update_schedule(schedule_id: int, schedule_data: Dict[str, Any], db: Session = Depends(get_db)):
    """更新排班"""
    try:
        schedule = db.query(Schedule).filter(Schedule.id == schedule_id).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="排班不存在")
        
        # 验证店铺和主播是否存在
        store_id = schedule_data.get("store_id")
        if store_id:
            store = db.query(Store).filter(Store.id == store_id).first()
            if not store:
                raise HTTPException(status_code=404, detail="指定的店铺不存在")
            schedule.store_id = store_id
        
        anchor_id = schedule_data.get("anchor_id")
        if anchor_id:
            anchor = db.query(Anchor).filter(Anchor.id == anchor_id).first()
            if not anchor:
                raise HTTPException(status_code=404, detail="指定的主播不存在")
            schedule.anchor_id = anchor_id
        
        # 获取渠道和班次
        channel_id = schedule_data.get("channel_id")
        if channel_id:
            schedule.channel_id = channel_id
        
        shift = schedule_data.get("shift")
        if shift:
            schedule.shift = shift
        
        # 解析日期和时间
        schedule_date = schedule.date
        start_time = schedule.start_time
        end_time = schedule.end_time
        
        if "date" in schedule_data:
            try:
                schedule_date = datetime.strptime(schedule_data.get("date"), "%Y-%m-%d").date()
                schedule.date = schedule_date
            except ValueError:
                raise HTTPException(status_code=400, detail="日期格式错误")
        
        if "start_time" in schedule_data:
            try:
                start_time = datetime.strptime(schedule_data.get("start_time"), "%H:%M").time()
                schedule.start_time = start_time
            except ValueError:
                raise HTTPException(status_code=400, detail="开始时间格式错误")
        
        if "end_time" in schedule_data:
            try:
                end_time = datetime.strptime(schedule_data.get("end_time"), "%H:%M").time()
                schedule.end_time = end_time
            except ValueError:
                raise HTTPException(status_code=400, detail="结束时间格式错误")
        
        # 计算时长（分钟）
        start_datetime = datetime.combine(schedule_date, start_time)
        end_datetime = datetime.combine(schedule_date, end_time)
        if end_datetime <= start_datetime:
            # 如果结束时间小于开始时间，认为是跨天排班，结束时间加一天
            end_datetime = datetime.combine(schedule_date + timedelta(days=1), end_time)
        
        # 计算总时长（小时）
        total_duration_hours = round((end_datetime - start_datetime).total_seconds() / 3600, 1)
        
        # 减去休息时长
        rest_duration = 0 if shift == 'fullday' else float(schedule_data.get("rest_duration", 1.5))
        duration = round(total_duration_hours - rest_duration, 1)
        
        schedule.duration = duration
        
        # 检查冲突 - 只检查店铺、渠道、班次是否重复，不考虑主播
        check_conflict_query = db.query(Schedule).filter(
            Schedule.date == schedule_date,
            Schedule.store_id == (store_id or schedule.store_id),
            Schedule.channel_id == (channel_id or schedule.channel_id),
            Schedule.shift == (shift or schedule.shift),
            Schedule.id != schedule_id  # 排除自身
        )
        
        conflict = check_conflict_query.first()
        if conflict:
            # 获取冲突主播的名字
            try:
                conflict_anchor = db.query(Anchor).filter(Anchor.id == conflict.anchor_id).first()
                conflict_anchor_name = conflict_anchor.name if conflict_anchor else "未知主播"
            except Exception as e:
                print(f"获取冲突主播信息失败: {str(e)}")
                conflict_anchor_name = "未知主播"
            
            error_msg = f"检测到排班冲突，冲突主播为:{conflict_anchor_name} (店铺: {conflict.store_id}, 渠道: {conflict.channel_id}, 班次: {conflict.shift})"
            
            # 在抛出异常前回滚事务
            db.rollback()
            
            # 使用return而不是raise，避免异步上下文管理器的问题
            return JSONResponse(
                status_code=400,
                content={"detail": error_msg}
            )
        
        # 更新其他字段
        if "notes" in schedule_data:
            schedule.notes = schedule_data.get("notes")
        
        if "room" in schedule_data:
            schedule.room = schedule_data.get("room")
            
        if "expected_count" in schedule_data:
            schedule.expected_count = schedule_data.get("expected_count", 0)
            
        if "real_count" in schedule_data:
            schedule.real_count = schedule_data.get("real_count", 0)
            
        if "live_count" in schedule_data:
            schedule.live_count = schedule_data.get("live_count", 0)
        
        if "rest_duration" in schedule_data:
            schedule.rest_duration = schedule_data.get("rest_duration", 1.5)
        
        schedule.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(schedule)
        return schedule.to_dict()
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新排班失败: {str(e)}")

@router.delete("/schedules/{schedule_id}")
def delete_schedule(schedule_id: int, db: Session = Depends(get_db)):
    """删除排班"""
    try:
        schedule = db.query(Schedule).filter(Schedule.id == schedule_id).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="排班不存在")
        
        db.delete(schedule)
        db.commit()
        return {"message": "排班删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除排班失败: {str(e)}")

# 获取排班统计
@router.get("/schedules/stats")
def get_schedule_stats(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取排班统计信息"""
    try:
        # 如果未提供日期范围，默认为当前月
        if not start_date:
            today = datetime.now().date()
            start_date = date(today.year, today.month, 1)
        
        if not end_date:
            # 获取当月最后一天
            if start_date.month == 12:
                next_month = date(start_date.year + 1, 1, 1)
            else:
                next_month = date(start_date.year, start_date.month + 1, 1)
            end_date = next_month - timedelta(days=1)
        
        # 按主播统计排班时长
        anchor_stats = db.query(
            Schedule.anchor_id,
            func.sum(Schedule.duration).label("total_hours"),
            func.count(Schedule.id).label("schedule_count")
        ).filter(
            Schedule.date >= start_date,
            Schedule.date <= end_date
        ).group_by(
            Schedule.anchor_id
        ).all()
        
        # 按店铺统计排班时长
        store_stats = db.query(
            Schedule.store_id,
            func.sum(Schedule.duration).label("total_hours"),
            func.count(Schedule.id).label("schedule_count")
        ).filter(
            Schedule.date >= start_date,
            Schedule.date <= end_date
        ).group_by(
            Schedule.store_id
        ).all()
        
        # 按日期统计排班数量
        date_stats = db.query(
            Schedule.date,
            func.count(Schedule.id).label("schedule_count"),
            func.sum(Schedule.duration).label("total_hours")
        ).filter(
            Schedule.date >= start_date,
            Schedule.date <= end_date
        ).group_by(
            Schedule.date
        ).order_by(
            Schedule.date
        ).all()
        
        # 计算总计数据
        total_hours = sum(stat.total_hours for stat in anchor_stats) if anchor_stats else 0
        anchor_count = len(set(stat.anchor_id for stat in anchor_stats)) if anchor_stats else 0
        store_count = len(set(stat.store_id for stat in store_stats)) if store_stats else 0
        
        # 简化返回的统计数据，只返回前端需要的总计信息
        return {
            "total_hours": round(total_hours, 1),
            "anchor_count": anchor_count,
            "store_count": store_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取排班统计失败: {str(e)}")

@router.get("/schedules/get-anchor")
def get_anchor_by_schedule(
    date: str,
    channel_id: str,
    shift: str,
    db: Session = Depends(get_db)
):
    """根据日期、渠道(店铺)和班次获取主播信息"""
    try:
        # 解析日期
        schedule_date = datetime.strptime(date, "%Y-%m-%d").date()
        
        # 查询符合条件的排班
        schedule = db.query(Schedule).filter(
            Schedule.date == schedule_date,
            Schedule.channel_id == channel_id,
            Schedule.shift == shift
        ).first()
        
        if schedule:
            # 获取主播信息
            anchor = db.query(Anchor).filter(Anchor.id == schedule.anchor_id).first()
            
            if anchor:
                return {
                    "success": True,
                    "anchor_id": schedule.anchor_id,
                    "anchor_name": anchor.name,
                    "schedule_id": schedule.id
                }
            else:
                # 如果找不到主播信息，直接返回排班中的anchor_id
                return {
                    "success": True,
                    "anchor_id": schedule.anchor_id,
                    "anchor_name": schedule.anchor_id,  # 使用anchor_id作为名称
                    "schedule_id": schedule.id
                }
        else:
            return {
                "success": True,
                "anchor_id": None,
                "anchor_name": "捡漏",
                "schedule_id": None,
                "source": "preset"
            }
            # 排班表中没有找到匹配记录，尝试从预设数据中查找主播信息
            try:
                # 读取预设值
                from routers.presets import read_presets
                presets = read_presets()
                shift_name = "早班" if shift == "morning" else "晚班"
                
                # 尝试匹配最适合的主播
                # 优先根据店铺名匹配，然后根据当前班次选择一个主播
                if "hosts" in presets and presets["hosts"]:
                    # 检查主播数据格式，兼容新的数据结构
                    if isinstance(presets["hosts"], list) and presets["hosts"]:
                        if isinstance(presets["hosts"][0], dict) and "hosts" in presets["hosts"][0]:
                            # 新格式：主播组对象，包含hosts字段
                            # 假设每组主播都有对应的负责店铺，这里简单实现随机分配
                            import random
                            
                            # 随机选择一个主播组
                            host_group = random.choice(presets["hosts"])
                            if host_group.get("hosts"):
                                # 从主播组的hosts字段中分割出主播名称
                                hosts_list = host_group["hosts"].split(",")
                                if hosts_list:
                                    # 随机选择一个主播
                                    anchor_name = random.choice(hosts_list).strip()
                                    return {
                                        "success": True,
                                        "anchor_id": None,
                                        "anchor_name": anchor_name,
                                        "schedule_id": None,
                                        "source": "preset"
                                    }
                        else:
                            # 旧格式：主播名称字符串数组
                            import random
                            anchor_name = random.choice(presets["hosts"])
                            return {
                                "success": True,
                                "anchor_id": None,
                                "anchor_name": anchor_name,
                                "schedule_id": None,
                                "source": "preset"
                            }
            except Exception as preset_error:
                print(f"从预设数据查找主播失败: {str(preset_error)}")
                # 如果查找预设失败，继续返回原来的错误信息
            
            return {
                "success": False,
                "message": "未找到符合条件的排班信息"
            }
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用YYYY-MM-DD格式")
    except Exception as e:
        print(f"获取主播信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取主播信息失败: {str(e)}")

@router.get("/schedules/anchor-stats")
def get_anchor_schedule_stats(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取主播排班统计信息"""
    try:
        # 如果未提供日期范围，默认为当前月
        # start和end_date是用户当前选择的日期
        if not start_date:
            today = datetime.now().date()
            start_date = date(today.year, today.month, 1)
        
        if not end_date:
            end_date = start_date
        
        # 转换日期为字符串格式，用于后续与VARCHAR类型的record_date比较
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

        # 从排班表schedule查询排班数据
        schedules = db.query(Schedule).filter(
            Schedule.date >= start_date,
            Schedule.date <= end_date
        ).all()
        for schedule in schedules:
            print(f"当前日期:schedule.date: {schedule.date}, start_date_str: {start_date_str}, end_date_str: {end_date_str}")
        
        # 从线索表获取数据，不过滤日期
        clues = db.query(ClueSheet).filter(
            ClueSheet.is_deleted == False,
            ## 只获取当天的数据
            func.date(ClueSheet.record_date) == start_date  
        ).all()
        print(f"查询到 {len(clues)} 条线索数据")
        
        ## 初始化捡漏数为线索表中当天的数据总数
        salvage_count = len(clues)

        # 创建映射关系: 日期_店铺_班次 -> 线索数量
        clue_counts = defaultdict(int)
        filtered_clues = []  # 存储经过日期筛选后的线索数据
        for clue in clues:
            try:
                # 处理日期格式转换逻辑，处理后clue_date_str将是str类型的YYYY-MM-DD格式
                if '/' in clue.record_date:
                    clue_date_parts = clue.record_date.split('/')
                    # 确保月和日是两位数
                    if len(clue_date_parts) == 3:
                        year = clue_date_parts[0]
                        month = clue_date_parts[1].zfill(2)  # 补零到两位
                        day = clue_date_parts[2].zfill(2)    # 补零到两位
                        clue_date_str = f"{year}-{month}-{day}"
                    else:
                        continue
                else:
                    # 如果已经是"-"分隔的格式，直接使用
                    clue_date_str = clue.record_date

                # 将 clue_date_str 转换为 date 对象
                try:
                    clue_date = datetime.strptime(clue_date_str, '%Y-%m-%d').date()
                except ValueError:
                    print(f"无法将 {clue_date_str} 转换为日期对象，跳过该线索")
                    continue
                # 筛选 clue_date_str
                if not (start_date <= clue_date <= end_date):
                    continue
                print(f"clue.date_str: {clue_date_str}")

                filtered_clues.append(clue)

                # 转换班次格式：早班->morning, 晚班->evening, 全天->fullday
                schedule_shift = ""
                if clue.shift == "早班":
                    schedule_shift = "morning"
                elif clue.shift == "晚班":
                    schedule_shift = "evening"
                elif clue.shift == "全天":
                    schedule_shift = "fullday"
                else:
                    continue  # 跳过无法识别的班次
                
                # 标准化店铺名称，去除前后空格
                store_name = clue.store.strip() if clue.store else ""
                
                # 创建线索表的标准化键: 日期_店铺_班次
                key = f"{clue_date_str}_{store_name}_{schedule_shift}"
                logging.info(f"生成的线索表标准化键: {key}")
                clue_counts[key] += 1
            
            except Exception as e:
                print(f"处理线索数据时出错: {str(e)}, 线索ID: {clue.ID if hasattr(clue, 'ID') else 'unknown'}")
                continue
        
        # 处理实际数
        morning_real_count = 0
        evening_real_count = 0
        fullday_real_count = 0
        not_fullday_real_count = 0

        # 遍历筛选后的线索表和排班表数据
        for clue in filtered_clues:
            # 转换线索表的 shift 字段
            if clue.shift == "早班":
                clue_shift = "morning"
            elif clue.shift == "晚班":
                clue_shift = "evening"
            elif clue.shift == "全天":
                clue_shift = "fullday"
            else:
                continue

            store_name = clue.store.strip() if clue.store else ""

            for schedule in schedules:
                if store_name == schedule.channel_id.strip() and clue_shift == schedule.shift:
                    if clue_shift == "morning":
                        morning_real_count += 1
                        salvage_count -= 1  # 实际数 +1，捡漏数 -1
                    elif clue_shift == "evening":
                        evening_real_count += 1
                        salvage_count -= 1  # 实际数 +1，捡漏数 -1
                    elif clue_shift == "fullday":
                        fullday_real_count += 1
                        salvage_count -= 1  # 实际数 +1，捡漏数 -1
        
        # 统计数据
        total_hours = 0
        morning_hours = 0
        evening_hours = 0
        anchors_set = set()
        morning_anchors_set = set()
        evening_anchors_set = set()
        stores_set = set()
        morning_stores_set = set()
        evening_stores_set = set()
        fullday_stores = set()  # 记录全天班次的店铺ID
        
        # 预计数、实际数、捡漏统计
        morning_expected_count = 0
        evening_expected_count = 0
        fullday_expected_count = 0
        
        all_stores = set()  # 记录所有参与排班的店铺ID

        # 新增逻辑：按主播和班次分组
        anchor_shift_groups = defaultdict(lambda: defaultdict(list))
        for schedule in schedules:

             ## 记录该店铺已参与排班
            all_stores.add(schedule.store_id)

            # 转换schedule.date为字符串，用于和clue_counts的键匹配
            schedule_date_str = schedule.date.strftime('%Y-%m-%d')

            anchor_shift_groups[schedule.anchor_id][schedule.shift].append((schedule.duration, schedule.store_id))
            
            ## 统计全天班次的店铺数和预计数
            if schedule.shift == 'fullday':
                fullday_stores.add(schedule.store_id)
                if schedule.expected_count:
                    fullday_expected_count += schedule.expected_count
                # 根据clue_sheet_2025计算实际数量
                key = f"{schedule_date_str}_{schedule.store_id}_fullday"
                fullday_real_count += clue_counts[key]
            
            # 统计预计数量
            if schedule.shift == 'morning' and schedule.expected_count:
                morning_expected_count += schedule.expected_count
            elif schedule.shift == 'evening' and schedule.expected_count:
                evening_expected_count += schedule.expected_count
        
        # 计算时长
        store_shift_map = defaultdict(set)  # 记录店铺在哪些班次中出现
        for anchor_id, shifts in anchor_shift_groups.items():
            for shift, durations_stores in shifts.items():
                durations = [ds[0] for ds in durations_stores]
                store_ids = {ds[1] for ds in durations_stores}  # 使用集合去重
                for store_id in store_ids:
                    store_shift_map[store_id].add(shift)
                if len(set(durations)) == 1:
                    # 如果所有时长都一样，取任意一条
                    duration_to_add = durations[0]
                else:
                    # 如果时长不一样，取最长的
                    duration_to_add = max(durations)
        
                # 根据班次累加时长
                if shift == 'morning':
                    morning_hours += duration_to_add
                    morning_anchors_set.add(anchor_id)
                    morning_stores_set.update(store_ids)
                elif shift == 'evening':
                    evening_hours += duration_to_add
                    evening_anchors_set.add(anchor_id)
                    evening_stores_set.update(store_ids)
        
        # 计算店铺数量
        morning_store_count = sum(1 for shifts in store_shift_map.values() if 'morning' in shifts)
        evening_store_count = sum(1 for shifts in store_shift_map.values() if 'evening' in shifts)

        ## 计算全天班次的店铺数
        fullday_store_count = len(fullday_stores)

        # 计算付费的实际数
        not_fullday_real_count = fullday_store_count - fullday_real_count
        
        # 返回统计结果
        return {
            "total_hours": morning_hours + evening_hours,  # 总排班时长
            "anchor_count": len(morning_anchors_set) + len(evening_anchors_set),  # 参与主播数
            "store_count": morning_store_count + evening_store_count,  # 参与店铺数
            "expected_count": morning_expected_count + evening_expected_count,  # 总预计数量
            "real_count": morning_real_count + evening_real_count,  # 总实际数量
            "salvage_count": salvage_count,  # 捡漏数
            "fullday": {
                "store_count": fullday_store_count,  # 全天班次店铺数
                "expected_count": fullday_expected_count,  # 全天班次预计数
                "real_count": fullday_real_count,  # 当天参与排班的实际店铺数
                "not_real_count": not_fullday_real_count
            },
            "morning": {
                "total_hours": morning_hours,
                "anchor_count": len(morning_anchors_set),
                "store_count": morning_store_count,
                "expected_count": morning_expected_count,  # 添加早班预计数量
                "real_count": morning_real_count  # 添加早班实际数量
            },
            "evening": {
                "total_hours": evening_hours,
                "anchor_count": len(evening_anchors_set),
                "store_count": evening_store_count,
                "expected_count": evening_expected_count,  # 添加晚班预计数量
                "real_count": evening_real_count  # 添加晚班实际数量
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取主播排班统计失败: {str(e)}")


@router.get("/anchors/groups")
def get_anchor_groups(anchor_names: str = Query(None, description="单个或多个主播名称，用逗号分隔")):
    """
    根据主播名称获取主播所在的分组信息
    
    Args:
        anchor_names: 单个或多个主播名称，多个名称用逗号分隔
    
    Returns:
        映射表，key为主播名，value为包含分组信息的字典
    """
    try:
        # 如果没有提供主播名称，返回空结果
        if not anchor_names:
            print("未提供主播名称，返回空结果")
            return {}
        
        # 分割主播名称列表
        names_list = [name.strip() for name in anchor_names.split(",") if name.strip()]
        print(f"需要查找的主播列表: {names_list}")
        
        # 读取预设值
        try:
            from routers.presets import read_presets
            presets = read_presets()
        except Exception as e:
            print(f"读取预设值失败: {str(e)}")
            # 返回空结果
            return {}
            
        print(f"成功读取预设值，hosts数据类型: {type(presets.get('hosts'))}")
        
        if 'hosts' in presets:
            print(f"主播组数量: {len(presets['hosts'])}")
            for i, host_group in enumerate(presets['hosts']):
                if isinstance(host_group, dict):
                    print(f"主播组 {i+1}: {host_group.get('group')} - {host_group.get('hosts')}")
        
        # 初始化结果字典
        result = {}
        
        # 遍历主播组
        if "hosts" in presets and isinstance(presets["hosts"], list):
            for host_group in presets["hosts"]:
                # 确保数据格式正确
                if isinstance(host_group, dict) and "hosts" in host_group and "group" in host_group:
                    # 获取主播列表
                    hosts_str = host_group.get("hosts", "")
                    if hosts_str:
                        host_list = [h.strip() for h in hosts_str.split(",")]
                        print(f"主播组 {host_group.get('group')} 中的主播: {host_list}")
                        # 检查每个需要查找的主播是否在这个组中
                        for name in names_list:
                            if name in host_list:
                                # 如果找到主播，将分组信息添加到结果中
                                result[name] = {
                                    "group": host_group.get("group", ""),
                                    "leader": host_group.get("leader", ""),
                                    "group_id": host_group.get("id", "")
                                }
                                print(f"找到主播 {name} 所在的分组: {host_group.get('group')}")
        
        print(f"最终结果: {result}")
        return result
    except Exception as e:
        print(f"获取主播分组信息失败: {str(e)}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取主播分组信息失败: {str(e)}") 