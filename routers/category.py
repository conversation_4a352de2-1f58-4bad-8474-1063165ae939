from typing import Optional, List, Dict, Any
import json
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session
from database import get_db
from models import Category
from pydantic import BaseModel, Field

router = APIRouter()

# ========== Pydantic 模型定义 ==========

class CategoryCreate(BaseModel):
    """添加分类请求模型"""
    type: Optional[str] = Field(None, description="类型")
    name: Optional[str] = Field(None, description="名称")
    pid: Optional[int] = Field(0, description="父级ID，默认为0表示一级分类")

class CategoryUpdate(BaseModel):
    """编辑分类请求模型"""
    type: Optional[str] = Field(None, description="类型")
    name: Optional[str] = Field(None, description="名称")
    pid: Optional[int] = Field(None, description="父级ID")

class CategoryQuery(BaseModel):
    """查询分类请求模型"""
    name: Optional[str] = Field(None, description="分类名称（模糊搜索）")
    parent_name: Optional[str] = Field(None, description="一级分类名称")
    page: Optional[int] = Field(1, ge=1, description="页码")
    page_size: Optional[int] = Field(10, ge=1, le=100, description="每页数量")

class CategoryResponse(BaseModel):
    """分类响应模型"""
    id: int
    pid: Optional[int]
    type: Optional[str]
    name: Optional[str]
    weight: Optional[int]
    status: int
    parent_name: Optional[str] = Field(None, description="父级分类名称")
    children: Optional[List['CategoryResponse']] = Field(None, description="子分类列表")

# 更新前向引用
CategoryResponse.model_rebuild()

def create_api_response(code: int = 200, message: str = "操作成功", data: Any = None) -> Dict[str, Any]:
    """
    创建统一的API响应格式
    
    Args:
        code: 响应状态码
        message: 响应消息
        data: 响应数据
    
    Returns:
        统一格式的响应字典
    """
    return {
        "code": code,
        "message": message,
        "data": data
    }

def get_parent_name(db: Session, pid: int) -> Optional[str]:
    """
    根据父级ID获取父级分类名称
    
    Args:
        db: 数据库会话
        pid: 父级ID
    
    Returns:
        父级分类名称或None
    """
    if pid == 0 or pid is None:
        return None
    
    parent = db.query(Category).filter(
        Category.id == pid,
        Category.status == 1
    ).first()
    
    return parent.name if parent else None

def build_category_tree(categories: List[Category], parent_id: int = 0) -> List[CategoryResponse]:
    """
    构建分类树结构
    
    Args:
        categories: 分类列表
        parent_id: 父级ID
    
    Returns:
        构建好的分类树
    """
    tree = []
    for category in categories:
        if category.pid == parent_id:
            category_data = CategoryResponse(
                id=category.id,
                pid=category.pid,
                type=category.type,
                name=category.name,
                weight=category.weight,
                status=category.status
            )
            
            # 递归获取子分类
            children = build_category_tree(categories, category.id)
            if children:
                category_data.children = children
            
            tree.append(category_data)
    
    return tree

# ========== API接口实现 ==========

@router.post("/api/category", summary="添加分类接口")
async def create_category(
    category_data: CategoryCreate,
    db: Session = Depends(get_db)
):
    """
    添加新分类接口
    
    - 支持选择所有 pid = 0 的一级分类作为父级
    - 如果未指定 weight，默认设置为当前生成的 id
    - 成功后返回新增分类的完整信息
    """
    try:
        # 验证父级分类是否存在（如果指定了父级ID且不为0）
        if category_data.pid and category_data.pid != 0:
            parent = db.query(Category).filter(
                Category.id == category_data.pid,
                Category.status == 1
            ).first()
            if not parent:
                return create_api_response(400, "指定的父级分类不存在")
        
        # 创建新分类
        new_category = Category(
            pid=category_data.pid or 0,
            type=category_data.type,
            name=category_data.name,
            status=1
        )
        
        db.add(new_category)
        db.flush()  # 获取自动生成的ID
        
        # 如果未指定权重，设置为当前ID
        if not new_category.weight:
            new_category.weight = new_category.id
        
        db.commit()
        
        # 获取父级名称
        parent_name = get_parent_name(db, new_category.pid)
        
        # 构建响应数据
        response_data = CategoryResponse(
            id=new_category.id,
            pid=new_category.pid,
            type=new_category.type,
            name=new_category.name,
            weight=new_category.weight,
            status=new_category.status,
            parent_name=parent_name
        )
        
        return create_api_response(200, "分类添加成功", response_data.dict())
        
    except Exception as e:
        db.rollback()
        return create_api_response(500, f"服务器内部错误: {str(e)}")

@router.put("/api/category/{category_id}", summary="编辑分类接口")
async def update_category(
    category_id: int,
    category_data: CategoryUpdate,
    db: Session = Depends(get_db)
):
    """
    修改已有分类信息接口
    
    - 支持字段更新：type, name, pid
    - 编辑页面需显示当前分类的父级名称（非 ID）
    - 成功后返回更新后的完整数据
    """
    try:
        # 查找要更新的分类
        category = db.query(Category).filter(
            Category.id == category_id,
            Category.status == 1
        ).first()
        
        if not category:
            return create_api_response(404, "分类不存在")
        
        # 验证父级分类是否存在（如果指定了父级ID且不为0）
        if category_data.pid is not None and category_data.pid != 0:
            parent = db.query(Category).filter(
                Category.id == category_data.pid,
                Category.status == 1
            ).first()
            if not parent:
                return create_api_response(400, "指定的父级分类不存在")
        
        # 防止将分类设置为自己的子分类（避免循环引用）
        if category_data.pid == category_id:
            return create_api_response(400, "不能将分类设置为自己的父级")
        
        # 更新分类信息
        if category_data.type is not None:
            category.type = category_data.type
        if category_data.name is not None:
            category.name = category_data.name
        if category_data.pid is not None:
            category.pid = category_data.pid
        
        db.commit()
        
        # 获取父级名称
        parent_name = get_parent_name(db, category.pid)
        
        # 构建响应数据
        response_data = CategoryResponse(
            id=category.id,
            pid=category.pid,
            type=category.type,
            name=category.name,
            weight=category.weight,
            status=category.status,
            parent_name=parent_name
        )
        
        return create_api_response(200, "分类更新成功", response_data.dict())
        
    except Exception as e:
        db.rollback()
        return create_api_response(500, f"服务器内部错误: {str(e)}")

@router.delete("/api/category/{category_id}", summary="删除分类接口（软删除）")
async def delete_category(
    category_id: int,
    db: Session = Depends(get_db)
):
    """
    软删除分类接口
    
    - 实现软删除操作，仅将 status 字段设为 0，不执行物理删除
    - 返回操作结果及影响记录数
    """
    try:
        # 查找要删除的分类
        category = db.query(Category).filter(
            Category.id == category_id,
            Category.status == 1
        ).first()
        
        if not category:
            return create_api_response(404, "分类不存在")
        
        # 检查是否有子分类
        child_count = db.query(Category).filter(
            Category.pid == category_id,
            Category.status == 1
        ).count()
        
        if child_count > 0:
            return create_api_response(400, f"该分类下有 {child_count} 个子分类，请先删除子分类")
        
        # 软删除：设置status为0
        category.status = 0
        db.commit()
        
        return create_api_response(200, "分类删除成功", {"affected_rows": 1})
        
    except Exception as e:
        db.rollback()
        return create_api_response(500, f"服务器内部错误: {str(e)}")

@router.post("/api/categorydata", summary="查询分类接口")
async def query_categories(
    query_data: CategoryQuery,
    db: Session = Depends(get_db)
):
    """
    查询分类接口
    
    - 支持根据 name 进行模糊查询
    - 支持根据一级分类（pid = 0）的名称进行筛选
    - 查询结果需包含完整的三级分类结构（父子联动展示）
    - 支持分页功能
    """
    try:
        # 构建查询条件
        query = db.query(Category).filter(Category.status == 1)
        
        # 按名称模糊搜索
        if query_data.name:
            query = query.filter(Category.name.like(f"%{query_data.name}%"))

        
        # 按一级分类名称筛选
        if query_data.parent_name:
            # 先找到对应的一级分类ID
            parent_category = db.query(Category).filter(
                Category.name == query_data.parent_name,
                Category.pid == 0,
                Category.status == 1
            ).first()
            
            if parent_category:
                # 查询该一级分类下的所有子分类（包括多级）
                parent_ids = [parent_category.id]
                all_parent_ids = []
                
                # 递归查找所有子分类ID
                while parent_ids:
                    current_level_ids = parent_ids.copy()
                    all_parent_ids.extend(parent_ids)
                    parent_ids = []
                    
                    for pid in current_level_ids:
                        child_categories = db.query(Category).filter(
                            Category.pid == pid,
                            Category.status == 1
                        ).all()
                        parent_ids.extend([cat.id for cat in child_categories])
                
                # 筛选出属于该一级分类树的所有分类
                query = query.filter(or_(
                    Category.id == parent_category.id,
                    Category.pid.in_(all_parent_ids)
                ))
            else:
                # 如果找不到对应的一级分类，返回空结果
                return create_api_response(200, "查询成功", {
                    "categories": [],
                    "tree": [],
                    "total": 0,
                    "page": query_data.page,
                    "page_size": query_data.page_size
                })
        
        # 获取总数
        total = query.count()
        
        # 分页处理
        offset = (query_data.page - 1) * query_data.page_size
        categories = query.order_by(Category.weight.asc(), Category.id.asc()).offset(offset).limit(query_data.page_size).all()
        
        # 构建扁平化的分类列表（带父级名称）
        category_list = []
        for category in categories:
            parent_name = get_parent_name(db, category.pid)
            category_data = CategoryResponse(
                id=category.id,
                pid=category.pid,
                type=category.type,
                name=category.name,
                weight=category.weight,
                status=category.status,
                parent_name=parent_name
            )
            category_list.append(category_data.dict())
        
        # 构建树形结构（用于前端展示层级关系）
        all_categories = db.query(Category).filter(Category.status == 1).order_by(Category.weight.asc(), Category.id.asc()).all()
        category_tree = build_category_tree(all_categories)
        
        response_data = {
            "categories": category_list,  # 分页的扁平化列表
            "tree": [item.dict() for item in category_tree],  # 完整的树形结构
            "total": total,
            "page": query_data.page,
            "page_size": query_data.page_size
        }
        
        return create_api_response(200, "查询成功", response_data)
        
    except Exception as e:
        return create_api_response(500, f"服务器内部错误: {str(e)}")

@router.get("/api/category/parents", summary="获取所有一级分类")
async def get_parent_categories(db: Session = Depends(get_db)):
    """
    获取所有一级分类接口
    
    - 用于前端下拉选择框显示可选的父级分类
    - 只返回 pid = 0 的一级分类
    """
    try:
        parent_categories = db.query(Category).filter(
            Category.pid == 0,
            Category.status == 1
        ).order_by(Category.weight.asc(), Category.id.asc()).all()
        
        category_list = []
        for category in parent_categories:
            category_data = {
                "id": category.id,
                "name": category.name,
                "type": category.type
            }
            category_list.append(category_data)
        
        return create_api_response(200, "获取一级分类成功", category_list)
        
    except Exception as e:
        return create_api_response(500, f"服务器内部错误: {str(e)}")

