import random
import re

import requests
from fastapi import APIRout<PERSON>, Depends, Query, HTTPException, Request
from sqlalchemy.orm import Session
from typing import Optional
from database import get_db, SessionLocal
from models import User, UserPermissionBackend,Emailseed,UserConfig
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from passlib.context import CryptContext
from sqlalchemy.exc import SQLAlchemyError
from auth import get_current_user, base64_encrypt_data, base64_decrypt_data,aoksend_send_code_email
from sqlalchemy import distinct

router = APIRouter()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class UserRegister(BaseModel):
    account: str = Field(..., min_length=8, max_length=20, pattern=r"^[a-zA-Z0-9]+$")
    password: str = Field(..., min_length=8, max_length=20, pattern=r"^[a-zA-Z0-9]+$")
    secondpass: str = Field(..., min_length=8, max_length=20, pattern=r"^[a-zA-Z0-9]+$")
    contact: str = Field(..., pattern=r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$")
    name: str =  Field(..., min_length=2, max_length=8, pattern=r"^[\u4e00-\u9fff]+$")
    department: str =  Field(..., min_length=2, max_length=8, pattern=r"^[\u4e00-\u9fff]+$")
    group_name: Optional[str] = None  # 允许为空
    code: Optional[int] = None  # 允许为空


# 前端注册页函数
@router.post("/api/register",summary="注册")
async def register(user: UserRegister):
   
    db = SessionLocal()
    try:
        if not user.name or not user.department or not user.code or not user.password or not user.account or not user.contact:
            raise HTTPException(status_code=400, detail={"message": "参数为空", "error_code": "NAME_GROUP_REQUIRED"})

        if not re.match(r"^[a-zA-Z0-9]+$", user.account):
            raise HTTPException(status_code=400,
                                detail={"message": "账号必须是8-20位的英文或数字", "error_code": "GROUP_REQUIRED"})
        # 检查销售部是否填写分组
        if user.department == "销售部" and not user.group_name:
            raise HTTPException(status_code=400, detail={"message": "销售部必须选择分组", "error_code": "GROUP_REQUIRED"})

        # 检查用户名是否已存在
        if db.query(User).filter(User.account == user.account).first():
            raise HTTPException(status_code=400, detail={"message": "账号已存在", "error_code": "USER_EXISTS"})

        ten_minute_ago = datetime.now().date() - timedelta(minutes=1)
        seed_exist = db.query(Emailseed).filter(Emailseed.email == user.contact,Emailseed.created_at >= ten_minute_ago).order_by(
            Emailseed.created_at.desc()).first()
        if not seed_exist:
            raise HTTPException(status_code=400, detail={"message": "验证码不存在", "error_code": "CODE_ERR"})
        if seed_exist.code != user.code:
            raise HTTPException(status_code=400, detail={"message": "验证码错误", "error_code": "CODE_ERR"})
        # 检查用户名是否已存在
        if user.password != user.secondpass:
            raise HTTPException(status_code=400, detail={"message": "前后两次密码不一致", "error_code": "PASS_ERR"})

        contact_hash = base64_encrypt_data(user.contact)
        user_email_exist = db.query(User).filter(User.contact == contact_hash).order_by(User.created_at.desc()).first()
        if user_email_exist:
            raise HTTPException(status_code=400, detail={"message": "此邮箱已被注册", "error_code": "Email_ERROR"})

        try:
            # 生成用户ID
            user_id = User.generate_user_id(user.department)
        except Exception as e:
            print(f"Error generating user ID: {str(e)}")
            raise HTTPException(status_code=500, detail={"message": "生成用户ID失败", "error_code": "ID_GENERATION_ERROR"})

        try:
            # 生成密码哈希
            password_hash = pwd_context.hash(user.password)


        except Exception as e:
            print(f"Error hashing password: {str(e)}")
            raise HTTPException(status_code=500, detail={"message": "密码加密失败", "error_code": "PASSWORD_HASH_ERROR"})

        # 创建新用户
        new_user = User(
            ID=user_id,
            account=user.account,
            password_hash=password_hash,
            contact=contact_hash,
            name=user.name,
            department=user.department,
            created_at=datetime.now(),
            is_admin=3,
            group_name=user.group_name if user.group_name else None  # 确保空值存储为 NULL
        )

        new_user_config = UserConfig(
            ID=user_id,
            tzstatus=1,
            nodisturbing=1,
            beg="22:00",
            end="08:00",           
        )

        try:
            # 添加到数据库
            db.add(new_user)
            db.add(new_user_config)
            db.commit()
            return {"success": True, "message": "注册成功", "user_id": user_id}
        except SQLAlchemyError as e:
            db.rollback()
            error_msg = str(e)
            print(f"Database error during user creation: {error_msg}")
            if "unique constraint" in error_msg.lower():
                raise HTTPException(status_code=400, detail={"message": "用户信息冲突，请检查输入", "error_code": "UNIQUE_CONSTRAINT_ERROR"})
            raise HTTPException(status_code=500, detail={"message": "数据库操作失败，请稍后重试", "error_code": "DB_ERROR"})

    except HTTPException as he:
        # 直接重新抛出HTTP异常
        raise he
    except Exception as e:
        print(f"Unexpected error in register: {str(e)}")
        raise HTTPException(status_code=500, detail={"message": "服务器内部错误，请稍后重试", "error_code": "INTERNAL_ERROR"})
    finally:
        try:
            db.close()
        except Exception as e:
            print(f"Error closing database connection: {str(e)}")

@router.get("/api/pending-users")
async def get_pending_users(request: Request, db: Session = Depends(get_db)):
    try:
        current_user = get_current_user(request, db)
        
        # 根据用户权限级别返回不同结果
        if not current_user:
            return []  # 未登录用户返回空列表
        
        # 查询待审核的用户(is_admin == 3)
        query = db.query(User).filter(User.is_admin == 3)
        
        if current_user.is_admin == 0:
            # 超级管理员可以看到所有待审核用户
            users = query.all()
        elif current_user.is_admin == 1:
            # 管理员只能看到本部门和本分组的待审核用户
            # 创建部门和分组的过滤条件
            filters = []
            
            # 添加部门过滤条件
            if current_user.department:
                filters.append(User.department == current_user.department)
            
            # 添加分组过滤条件(如果管理员有分组)
            if current_user.group_name:
                filters.append(User.group_name == current_user.group_name)
            
            # 组合过滤条件(使用OR，满足任一条件)
            from sqlalchemy import or_
            if filters:
                users = query.filter(or_(*filters)).all()
            else:
                users = []
        else:
            # 普通用户和待审核用户看不到任何申请
            users = []
        
        user_list = [{
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "username": user.account,
            "register_time": user.created_at.isoformat(),
            "is_admin": user.is_admin,
            "group_name": user.group_name,
            "identity": user.identity  # 确保返回身份字段
        } for user in users]
        
        return user_list
    except Exception as e:
        print(f"获取待审核用户失败: {str(e)}")
        return []

# 用户审核通过
@router.post("/api/approve-user/{user_id}")
async def approve_user(user_id: str, role: dict):
    try:
        db = SessionLocal()
        user = db.query(User).filter(User.ID == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 获取身份字段
        identity = role.get("identity")
        if not identity:
            raise HTTPException(status_code=400, detail="必须选择身份")
        
        # 将用户设置为普通用户
        user.is_admin = 2

        # 设置用户身份
        user.identity = identity

        db.commit()
        return {"message": "用户审核通过"}
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    finally:
        db.close()

# 用户审核拒绝
@router.post("/api/reject-user/{user_id}")
async def reject_user(user_id: str):
    try:
        db = SessionLocal()
        user = db.query(User).filter(User.ID == user_id).first()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        db.delete(user)
        db.commit()
        return {"message": "用户已删除"}
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    finally:
        db.close()

# 获取分组列表
@router.get("/api/group-list")
async def get_group_list(request: Request, db: Session = Depends(get_db)):
    try:
        current_user = get_current_user(request, db)
        
        # 未登录用户或普通用户返回空列表
        if not current_user or current_user.is_admin > 1:
            print("用户无权限获取分组列表")
            return []
            
        # 查询所有不重复的分组名称
        query = db.query(User.group_name).distinct().filter(User.group_name != None, User.group_name != '')
        
        # 管理员只能看到自己部门的分组
        if current_user.is_admin == 1 and current_user.department:
            query = query.filter(User.department == current_user.department)
            
        # 执行查询并获取结果
        groups = [group[0] for group in query.all()]
        print(f"获取到的分组列表: {groups}")
        return groups
        
    except Exception as e:
        print(f"获取分组列表失败: {str(e)}")
        return []

# 获取身份列表(从后端权限表获取)
@router.get("/api/identity-list")
async def get_identity_list(department: Optional[str] = None, request: Request = None, db: Session = Depends(get_db)):
    try:
        # 确保request参数存在
        if not request:
            print("请求对象为空")
            return []
            
        # 获取当前用户
        current_user = get_current_user(request, db)
        
        # 检查权限：只有管理员和超级管理员可以获取身份列表
        if not current_user or current_user.is_admin > 1:
            print("用户无权限获取身份列表")
            return []
            
        # 从UserPermissionBackend表中查询所有不重复的身份值，同时过滤掉"超管"
        query = db.query(distinct(UserPermissionBackend.identity)).filter(
            UserPermissionBackend.identity != None, 
            UserPermissionBackend.identity != '',
            UserPermissionBackend.identity != '超管'  # 添加过滤条件，排除"超管"
        )
        
        # 如果提供了部门参数，则根据部门过滤
        if department:
            query = query.filter(UserPermissionBackend.department == department)
        
        # 执行查询并获取结果
        identities = [identity[0] for identity in query.all()]
        return identities
        
    except Exception as e:
        print(f"获取身份列表失败: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        # 返回空列表而不是抛出异常，确保API不会返回500错误
        return []

# 获取用户的审核权限
@router.get("/api/user-review-permissions")
async def get_user_review_permissions(request: Request, db: Session = Depends(get_db)):
    try:
        # 获取当前用户
        current_user = get_current_user(request, db)
        if not current_user:
            return {"review_all": 1, "review_group": 1}

        # 超级管理员有全部权限
        if current_user.is_admin == 0:
            return {"review_all": 0, "review_group": 0}
            
        # 普通用户和待审核用户没有审核权限
        if current_user.is_admin > 1:
            return {"review_all": 1, "review_group": 1}
        
        # 查询管理员对应的权限
        permission = db.query(UserPermissionBackend).filter(
            UserPermissionBackend.department == current_user.department,
            UserPermissionBackend.identity == current_user.identity
        ).first()
        
        if permission:
            return {
                "review_all": permission.review_all if hasattr(permission, 'review_all') else 0,
                "review_group": permission.review_group if hasattr(permission, 'review_group') else 0
            }
        else:
            # 未找到权限配置时的默认值
            return {"review_all": 0, "review_group": 0}
            
    except Exception as e:
        print(f"获取审核权限出错: {str(e)}")
        return {"review_all": 0, "review_group": 0}



# 忘记密码
class Forgetpassdata(BaseModel):
    username:str
    contact:str
    code:int
    password: str
    secondpass:str
@router.post("/api/forgetpass",summary="忘记密码")
async def forgetpass(user: Forgetpassdata):
        if not user.username or not user.contact or not user.code or not user.password or not user.secondpass:
            return HTTPException(status_code=400, detail="参数不能为空")

        pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        if not re.match(pattern, user.contact):
            return HTTPException(status_code=400, detail="邮箱格式不正确")

        if user.password != user.secondpass:
            return HTTPException(status_code=400, detail="前后两次密码不一致")
        
        ten_minute_ago = datetime.now().date() - timedelta(minutes=1)
        db = SessionLocal()
        seed_exist = db.query(Emailseed).filter(Emailseed.email == user.contact,Emailseed.created_at >= ten_minute_ago).order_by(
            Emailseed.created_at.desc()).first()
        if not seed_exist:
            return HTTPException(status_code=400, detail={"message": "验证码错误", "error_code": "CODE_ERR"})
        
        if seed_exist.code != user.code:
            return HTTPException(status_code=400, detail={"message": "验证码错误", "error_code": "CODE_ERR"})

        #return user.contact
        jiami_contact = base64_encrypt_data(user.contact)

        #return jiami_contact
        userInfo = db.query(User).filter(User.contact == jiami_contact,User.account == user.username).first()
        if not userInfo:
            return HTTPException(status_code=400, detail="此邮箱用户不存在")

        if pwd_context.verify(user.password, userInfo.password_hash):
            return HTTPException(status_code=400, detail="新密码不能与旧密码一致")

        try:
            #  修改用户密码
            userInfo.password_hash = pwd_context.hash(user.password)
            db.commit()
            return HTTPException(status_code=1, detail={"message": "密码重置成功"})

        except SQLAlchemyError as e:
            db.rollback()
            return HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
        finally:
            db.close()

class Emaildata(BaseModel):
    """线索分发请求数据模型"""
    contact: str

#修改邮箱(已登录）
@router.post("/api/upuseremail",summary="修改邮箱")
async def upuserinfo(data: Emaildata,request: Request,db: Session = Depends(get_db)):

    if not data.contact:
        raise HTTPException(status_code=400, detail="邮箱必填")

    pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    if not re.match(pattern, data.contact):
        raise HTTPException(status_code=400, detail="邮箱格式不正确")

    user = get_current_user(request, db)
    if not user:
        raise HTTPException(status_code=401, detail="用户未登录")

    userInfo = db.query(User).filter(User.account == user.account).first()

    try:
        userInfo.contact = base64_encrypt_data(data.contact)
        db.commit()
        return {"message": "邮箱修改成功"}

    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    finally:
        db.close()

class Passdata(BaseModel):
    """线索分发请求数据模型"""
    password: str
    secondpass:str

#修改密码(已登录）
@router.post("/api/upuserpass",summary="修改密码")
async def upuserpass(data: Passdata,request: Request,db: Session = Depends(get_db)):

    if not data.password or not data.secondpass:
        return HTTPException(status_code=400, detail="2次密码必填")

    if data.password != data.secondpass:
        return HTTPException(status_code=400, detail="前后两次密码不一致")

    pattern = r"^[a-zA-Z0-9]+$"
    if not re.match(pattern, data.password):
        return HTTPException(status_code=400, detail="密码格式不正确,8~20位英文或数字组合")
    if len(data.password) < 8 or len(data.password) > 20:
        return HTTPException(status_code=400, detail="密码格式不正确,8~20位英文或数字组合")

    user = get_current_user(request, db)
    if not user:
        return HTTPException(status_code=401, detail="用户未登录")

    userInfo = db.query(User).filter(User.account == user.account).first()

    try:
        userInfo.password = pwd_context.hash(data.password)
        db.commit()
        return {"status_code":1,"detail": "密码修改成功"}

    except SQLAlchemyError as e:
        db.rollback()
        return HTTPException(status_code=400, detail=f"数据库错误: {str(e)}")
    finally:
        db.close()


# 发送邮箱
@router.post("/api/send_email")
async def send_email(data: Emaildata,db: Session = Depends(get_db)):
    if not data.contact:
        return HTTPException(status_code=400, detail="邮箱必填")

    pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    if not re.match(pattern, data.contact):
        return HTTPException(status_code=400, detail="邮箱格式不正确")

    seed_exist = db.query(Emailseed).filter(Emailseed.email == data.contact).order_by(Emailseed.created_at.desc()).first()
    if seed_exist:
        time_since_last_submit = datetime.now() - seed_exist.created_at
        if time_since_last_submit < timedelta(minutes=10):
            return HTTPException(status_code=400, detail="十分钟内不要重复发送验证码")

    # 生成一个 6位随机英文数字字符串
    sixcode = random.randint(100000, 999999)

    result_seed = aoksend_send_code_email(data.contact, sixcode)

    if result_seed:
        try:
            emailseed_item = Emailseed(email=data.contact,code=sixcode,created_at=datetime.now())
            db.add(emailseed_item)
            db.commit()
            return {"status_code": 1, "detail": "验证码发送成功"}

        except SQLAlchemyError as e:
            db.rollback()
            return {"status_code": 1, "detail": "数据库错误"}
        finally:
            db.close()


    else:    return {"status_code":0, "detail": "发送失败"}



# 修改用户配置
class UpUserConfig(BaseModel):
    tzstatus: Optional[int]= None
    nodisturbing: Optional[int]= None
    beg: Optional[str] = None
    end: Optional[str] = None
@router.post("/api/user_config",summary="修改用户配置")
async def post_user_config(data: UpUserConfig,request: Request, db: Session = Depends(get_db)):
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        user_config = db.query(UserConfig).filter(UserConfig.ID == user.ID).first()
        if not user_config:
            raise HTTPException(status_code=400, detail="用户配置不存在")

        if data.tzstatus is not None:
            user_config.tzstatus = data.tzstatus
        if data.nodisturbing is not None:
            user_config.nodisturbing = data.nodisturbing
        if data.beg is not None:
            user_config.beg = data.beg
        if data.end is not None:
            user_config.end = data.end 

        try:
            db.commit()
            return {"status_code":1,"detail": "修改成功"}

        except SQLAlchemyError as e:
            db.rollback()
            return HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
        finally:
            db.close()


# 获取用户配置
@router.get("/api/user_config",summary="获取用户配置")
async def get_user_config(request: Request):
        db = SessionLocal()
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="用户未登录")
       
        user_config = db.query(UserConfig).filter(UserConfig.ID == user.ID).first()
        if user_config:
            new_user_config = UserConfig(
                tzstatus=user_config.tzstatus,
                nodisturbing=user_config.nodisturbing,
                beg=user_config.beg,
                end=user_config.end      
            )
        if not user_config:
            new_user_config = UserConfig(
                ID=user.ID,
                tzstatus=1,
                nodisturbing=1,
                beg="22:00",
                end="08:00",           
            )
            db.add(new_user_config)
            db.commit()
            db.refresh(new_user_config)  # 刷新对象以确保数据已加载
       
        return new_user_config
      
       
       





