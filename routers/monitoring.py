from fastapi import APIRouter, WebSocket, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from database import get_db
from auth import get_current_user
from typing import List, Dict, Any, Optional
from models import User, MonitoringStatus, FormattedData
from datetime import datetime, date
import json

router = APIRouter()

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                await self.disconnect(connection)

manager = ConnectionManager()

@router.websocket("/api/monitoring/ws")
async def websocket_endpoint(websocket: WebSocket, token: str = None):
    # 从WebSocket请求中获取token
    if not token:
        token = websocket.headers.get("Authorization", "").replace("Bearer ", "")
        if not token:
            token = websocket.query_params.get("token")  # 从URL参数中获取token
            if not token:
                token = websocket.cookies.get("token")
    
    # 验证token
    db = next(get_db())
    try:
        if token and token.startswith("user_"):
            user_id = token.split("_")[1]
            user = db.query(User).filter(User.ID == user_id).first()
            if user:
                await manager.connect(websocket)
                try:
                    while True:
                        data = await websocket.receive_text()
                        # 处理接收到的数据
                        await websocket.send_text(data)
                except Exception as e:
                    print(f"WebSocket连接错误: {str(e)}")
                finally:
                    manager.disconnect(websocket)
                return
    except Exception as e:
        print(f"WebSocket认证错误: {str(e)}")
    
    await websocket.close(code=403)
    return

# 获取监测状态接口
@router.get("/api/monitoring/status")
def get_monitoring_status(db: Session = Depends(get_db)):
    """获取当前监测状态"""
    status = db.query(MonitoringStatus).first()
    
    # 如果没有记录，创建一个默认启用的记录
    if not status:
        status = MonitoringStatus(is_enabled=True)
        db.add(status)
        db.commit()
        db.refresh(status)
    
    return {"isEnabled": status.is_enabled}

# 更新监测状态接口
@router.post("/api/monitoring/status")
def update_monitoring_status(data: Dict[str, Any] = Body(...), db: Session = Depends(get_db)):
    """更新监测状态"""
    is_enabled = data.get("isEnabled", True)
    
    status = db.query(MonitoringStatus).first()
    
    # 如果没有记录，创建一个新记录
    if not status:
        status = MonitoringStatus(is_enabled=is_enabled)
        db.add(status)
    else:
        status.is_enabled = is_enabled
    
    status.last_updated = datetime.now()
    db.commit()
    
    return {"isEnabled": status.is_enabled}

# 获取监测统计数据接口
@router.get("/api/monitoring/stats")
def get_monitoring_stats(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取监测统计数据"""
    try:
        query = db.query(FormattedData)
        
        # 如果提供了日期范围，按日期过滤
        if start_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
            query = query.filter(FormattedData.created_at >= start_date_obj)
        
        if end_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
            # 使日期包含整天
            end_date_obj = datetime.combine(end_date_obj, datetime.max.time())
            query = query.filter(FormattedData.created_at <= end_date_obj)
        
        # 获取所有数据
        all_data = query.all()
        
        # 计算今日数据
        today = datetime.now().date()
        today_data = [d for d in all_data if d.created_at.date() == today]
        
        # 已发送的数据
        sent_data = [d for d in all_data if d.is_sent]
        today_sent_data = [d for d in today_data if d.is_sent]
        
        # 等待发送的数据
        waiting_data = [d for d in all_data if not d.is_sent]
        
        # 计算平均处理时间（如果有处理时间记录）
        process_times = [d.process_time for d in all_data if d.process_time is not None]
        avg_process_time = sum(process_times) / len(process_times) if process_times else 0
        
        # 最后更新时间
        last_update = max([d.created_at for d in all_data]) if all_data else datetime.now()
        
        return {
            "today_count": len(today_data),
            "total_count": len(all_data),
            "today_sent": len(today_sent_data),
            "total_sent": len(sent_data),
            "waiting": len(waiting_data),
            "avg_process_time": round(avg_process_time, 2),
            "last_update_time": last_update.isoformat()
        }
    except Exception as e:
        print(f"获取监测统计数据出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取监测统计数据失败: {str(e)}")

# 处理数据监测请求
@router.post("/api/formatting/monitor")
async def monitor_data(data: Dict[str, Any] = Body(...), db: Session = Depends(get_db)):
    """处理数据监测请求"""
    try:
        # 检查监测是否启用
        status = db.query(MonitoringStatus).first()
        if not status or not status.is_enabled:
            return {"success": False, "message": "监测功能已关闭"}
        
        # 记录处理开始时间
        start_time = datetime.now()
        
        # 提取ID，如果没有提供则生成一个唯一ID
        data_id = data.get("ID")
        if not data_id:
            data_id = f"auto_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # 检查是否已存在相同ID的记录
        existing_data = db.query(FormattedData).filter(FormattedData.id == data_id).first()
        if existing_data:
            # 如果记录已存在，更新而不是插入
            existing_data.raw_data = json.dumps(data, ensure_ascii=False)
            existing_data.formatted_text = ""
            existing_data.is_sent = False
            existing_data.created_at = datetime.now()
            db.commit()
            formatted_data = existing_data
        else:
            # 创建新的格式化数据记录
            formatted_data = FormattedData(
                id=data_id,
                raw_data=json.dumps(data, ensure_ascii=False),
                formatted_text="",  # 初始时为空，可以后续处理
                is_sent=False,  # 初始未发送
                created_at=datetime.now()
            )
            # 添加到数据库
            db.add(formatted_data)
            db.commit()
            db.refresh(formatted_data)
        
        # 计算处理时间
        process_time = (datetime.now() - start_time).total_seconds() * 1000  # 毫秒
        
        # 更新处理时间
        formatted_data.process_time = process_time
        db.commit()
        
        # 通过WebSocket广播更新消息
        stats = get_monitoring_stats(db=db)
        await manager.broadcast(json.dumps(stats))
        
        return {"success": True, "id": data_id, "message": "数据已成功监测"}
    except Exception as e:
        print(f"监测数据出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"监测数据失败: {str(e)}")