from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from typing import Optional
import httpx
from database import get_db
from models import User

router = APIRouter()

@router.post("/api/wechat_opened")
async def up_wechat_opened(request: Request, db: Session = Depends(get_db)):

    data = await request.json()
    username = data.get("username")
    wechat_code = data.get("wechat_code")

    if not username or not wechat_code:
        raise HTTPException(status_code=400, detail="提供用户账号,微信小程序code")

    userInfo = db.query(User).filter(User.account == username).first()
    if not userInfo:
        raise HTTPException(status_code=400, detail="用户不存在")

    url = "https://api.weixin.qq.com/sns/jscode2session"
    params = {
        "appid": "wx9eb14635f53a4acf",
        "secret": "1a75fc872c764a5d5147ee5d31632899",
        "js_code": wechat_code,
        "grant_type": "authorization_code"
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, params=params)
            response.raise_for_status()
        except httpx.HTTPStatusError as e:
            raise HTTPException(status_code=400, detail="WeChat API error")

    wxdata = response.json()
    openid = wxdata.get("openid")
    session_key = wxdata.get("session_key")
    if not openid:
        raise HTTPException(status_code=400, detail=wxdata.get("errmsg"))

    if openid:
        userInfo.wechat_openid = openid
        db.commit()

    return {
        "success": True,
        "message": f"成功",
        "data": {"openid": openid,"session_key": session_key}
    }




