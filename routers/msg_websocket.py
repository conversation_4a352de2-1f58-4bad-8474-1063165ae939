from fastapi import APIRouter, Depends, HTTPException, Query, Request, Body, WebSocket, WebSocketDisconnect
from sqlalchemy import func, and_, or_, desc, asc
from database import get_db
from sqlalchemy.orm import Session
from typing import Optional, List
from database import get_db, SessionLocal
from models import DistributionRule, DistributionQueue, User, UserConfig, SalaCrm, ClueSheet, ClueWithdraw
from auth import get_current_user
from datetime import datetime, time
import json
import logging
import os

WEB_URL = os.getenv("WEB_URL")
# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# WebSocket连接池管理
class WebSocketManager:
    def __init__(self):
        # 使用字典存储用户ID到WebSocket连接的映射
        self.active_connections = {}

    async def connect(self, user_id: str, websocket: WebSocket):
        """建立WebSocket连接"""
        await websocket.accept()
        # 如果用户已有连接，先断开旧连接
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].close()
            except:
                pass
        self.active_connections[user_id] = websocket
        logger.info(f"用户 {user_id} WebSocket连接已建立")

    def disconnect(self, user_id: str):
        """断开WebSocket连接"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
            logger.info(f"用户 {user_id} WebSocket连接已断开")

    async def send_personal_message(self, user_id: str, message: str):
        """向指定用户发送消息"""
        print(f"🔍 尝试向用户 {user_id} 发送消息")
        print(f"📋 当前活跃连接: {list(self.active_connections.keys())}")

        if user_id in self.active_connections:
            try:
                print(f"📤 正在向用户 {user_id} 发送WebSocket消息")
                await self.active_connections[user_id].send_text(message)
                print(f"✅ 成功向用户 {user_id} 发送消息")
                return True
            except Exception as e:
                print(f"❌ 向用户 {user_id} 发送消息失败: {e}")
                logger.error(f"向用户 {user_id} 发送消息失败: {e}")
                # 连接异常，移除连接
                self.disconnect(user_id)
                return False
        else:
            print(f"⚠️ 用户 {user_id} 不在活跃连接列表中")
            return False

    def get_connected_users(self) -> List[str]:
        """获取所有在线用户ID列表"""
        return list(self.active_connections.keys())

# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()

# 检查用户推送规则
def check_user_push_rules(user_id: str, db: Session) -> tuple[bool, str]:
    """
    检查用户推送规则，判断是否应该跳过推送

    Args:
        user_id: 用户ID
        db: 数据库会话

    Returns:
        tuple[bool, str]: (是否跳过推送, 跳过原因)
        - (False, "") 表示不跳过，正常推送
        - (True, "原因") 表示跳过推送，并说明原因
    """
    try:
        # 查询用户配置
        user_config = db.query(UserConfig).filter(UserConfig.ID == user_id).first()

        # 如果查询不到用户配置记录，则正常推送
        if not user_config:
            logger.info(f"用户 {user_id} 无配置记录，正常推送")
            return False, ""

        # 如果系统通知已关闭，直接跳过推送
        if user_config.tzstatus == 0:
            return True, "系统通知已关闭"

        # 如果系统通知开启且免打扰模式开启，检查当前时间是否在免打扰时段
        if user_config.tzstatus == 1 and user_config.nodisturbing == 1:
            current_time = datetime.now().time()

            # 解析免打扰开始和结束时间
            try:
                beg_time = datetime.strptime(user_config.beg, "%H:%M").time()
                end_time = datetime.strptime(user_config.end, "%H:%M").time()

                # 判断当前时间是否在免打扰时段内
                if beg_time <= end_time:
                    # 同一天内的时间段，如 09:00-17:00
                    if beg_time <= current_time <= end_time:
                        return True, f"免打扰时段({user_config.beg}-{user_config.end})"
                else:
                    # 跨日的时间段，如 22:00-08:00
                    if current_time >= beg_time or current_time <= end_time:
                        return True, f"免打扰时段({user_config.beg}-{user_config.end})"

            except ValueError as e:
                logger.error(f"用户 {user_id} 免打扰时间格式错误: {e}")
                # 时间格式错误时，不跳过推送
                return False, ""

        # 其他情况正常推送
        return False, ""

    except Exception as e:
        logger.error(f"检查用户 {user_id} 推送规则时出错: {e}")
        # 出错时不跳过推送，确保重要消息能够送达
        return False, ""

# 兼容查询参数的WebSocket端点
@router.websocket("/api/websocket")
async def websocket_endpoint_query(websocket: WebSocket):
    """WebSocket连接端点 - 支持查询参数"""
    # 从查询参数获取用户ID
    query_params = dict(websocket.query_params)
    user_id = query_params.get('token') or query_params.get('user_id')

    print(f"🔗 WebSocket连接请求 (查询参数): user_id={user_id}, query_params={query_params}")
    logger.info(f"WebSocket连接请求 (查询参数): user_id={user_id}")

    if not user_id:
        await websocket.close(code=4000, reason="缺少用户ID参数")
        return

    try:
        await websocket.accept()
        print(f"✅ WebSocket连接已接受 (查询参数): user_id={user_id}")
        logger.info(f"WebSocket连接已接受 (查询参数): user_id={user_id}")

        # 添加到连接池
        websocket_manager.active_connections[user_id] = websocket

        # 发送连接成功消息
        welcome_message = {
            "type": "connection_success",
            "message": f"用户 {user_id} 连接成功",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(welcome_message, ensure_ascii=False))
        print(f"📤 发送欢迎消息给用户 {user_id}")

        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            print(f"📨 收到用户 {user_id} 的消息: {data}")
            logger.info(f"收到用户 {user_id} 的消息: {data}")

            # 处理不同类型的消息
            if data == "ping":
                await websocket.send_text("pong")
                print(f"💓 向用户 {user_id} 发送心跳响应")
            else:
                response = f"服务器收到消息: {data}"
                await websocket.send_text(response)
                print(f"📤 向用户 {user_id} 发送响应: {response}")

    except WebSocketDisconnect:
        print(f"🔌 用户 {user_id} WebSocket连接断开")
        logger.info(f"用户 {user_id} WebSocket连接断开")
    except Exception as e:
        print(f"❌ WebSocket连接异常: {e}")
        logger.error(f"WebSocket连接异常: {e}")
    finally:
        # 清理连接
        if user_id and user_id in websocket_manager.active_connections:
            del websocket_manager.active_connections[user_id]
            print(f"🧹 清理用户 {user_id} 的连接")

@router.websocket("/api/websocket/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket连接端点"""
    print(f"🔗 WebSocket连接请求: user_id={user_id}")
    logger.info(f"WebSocket连接请求: user_id={user_id}")

    try:
        await websocket.accept()
        print(f"✅ WebSocket连接已接受: user_id={user_id}")
        logger.info(f"WebSocket连接已接受: user_id={user_id}")

        # 添加到连接池
        websocket_manager.active_connections[user_id] = websocket

        # 发送连接成功消息
        welcome_message = {
            "type": "connection_success",
            "message": f"用户 {user_id} 连接成功",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(welcome_message, ensure_ascii=False))
        print(f"📤 发送欢迎消息给用户 {user_id}")

        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            print(f"📨 收到用户 {user_id} 的消息: {data}")
            logger.info(f"收到用户 {user_id} 的消息: {data}")

            # 处理不同类型的消息
            if data == "ping":
                await websocket.send_text("pong")
                print(f"💓 向用户 {user_id} 发送心跳响应")
            else:
                response = f"服务器收到消息: {data}"
                await websocket.send_text(response)
                print(f"📤 向用户 {user_id} 发送响应: {response}")

    except WebSocketDisconnect:
        print(f"🔌 用户 {user_id} WebSocket连接断开")
        logger.info(f"用户 {user_id} WebSocket连接断开")
    except Exception as e:
        print(f"❌ WebSocket连接异常: {e}")
        logger.error(f"WebSocket连接异常: {e}")
    finally:
        # 清理连接
        if user_id in websocket_manager.active_connections:
            del websocket_manager.active_connections[user_id]
            print(f"🧹 清理用户 {user_id} 的连接")

# 线索WebSocket推送接口
@router.get("/api/websocket/xiansuo", summary="线索WebSocket推送")
async def get_xiansuo_websocket(db: Session = Depends(get_db)):
    """线索推送接口 - 向有新线索的用户推送通知"""
    try:
        current_date = datetime.now().date()

        # 查询需要推送的用户及其线索数量
        # 使用子查询来确保数据准确性
        try:
            user_clue_counts = db.query(
                User.ID,
                User.name,
                func.count(DistributionQueue.id).label('clue_count')
            ).join(
                DistributionQueue, DistributionQueue.member == User.name
            ).filter(
                DistributionQueue.queue_date == current_date,
                DistributionQueue.email_seeded == 0,
                DistributionQueue.status == "distributed"
            ).group_by(User.ID, User.name).all()
            logger.info(f"查询到 {len(user_clue_counts)} 个用户有待推送线索")
            
        except Exception as query_error:
            logger.error(f"数据库查询失败: {query_error}")
            # 如果JOIN查询失败，尝试使用子查询方式
            try:
                # 先获取需要推送的成员名单
                pending_members = db.query(
                    DistributionQueue.member,
                    func.count(DistributionQueue.id).label('clue_count')
                ).filter(
                    DistributionQueue.queue_date == current_date,
                    DistributionQueue.email_seeded == 0,
                    DistributionQueue.status == "distributed"
                ).group_by(DistributionQueue.member).all()

                logger.info(f"子查询获取到 {len(pending_members)} 个成员")

                # 然后根据成员名称获取用户信息
                user_clue_counts = []
                for member_name, clue_count in pending_members:
                    user = db.query(User).filter(User.name == member_name).first()
                    if user:
                        user_clue_counts.append((user.ID, user.name, clue_count))
                        logger.info(f"找到用户: {user.name} ({user.ID}), 线索数: {clue_count}")
                    else:
                        logger.warning(f"未找到成员 {member_name} 对应的用户记录")

            except Exception as fallback_error:
                logger.error(f"备用查询也失败: {fallback_error}")
                raise HTTPException(status_code=500, detail=f"数据库查询失败: {str(fallback_error)}")

        if not user_clue_counts:
            logger.info("暂无需要推送的线索")
            return {
                "success": True,
                "message": "暂无需要推送的线索",
                "pushed_users": 0,
                "total_clues": 0
            }

        # 获取在线用户列表
        online_users = websocket_manager.get_connected_users()
        print(f"🌐 当前在线用户列表: {online_users}")
        print(f"📊 在线用户数量: {len(online_users)}")
        logger.info(f"当前在线用户: {online_users}")
       
        pushed_users = 0
        total_clues = 0
        push_results = []

        # 开始事务处理
        for user_id, user_name, clue_count in user_clue_counts:
            total_clues += clue_count
            logger.info(f"处理用户: {user_name} ({user_id}), 线索数: {clue_count}")

            # 只向在线用户推送消息
            # 确保user_id是字符串类型，因为WebSocket连接中存储的是字符串
            user_id_str = str(user_id)
            print(f"🔍 检查用户 {user_name}({user_id}) 是否在线，转换后ID: {user_id_str}")

            if user_id_str in online_users:
                # 检查用户推送规则
                should_skip, skip_reason = check_user_push_rules(user_id_str, db)
                if should_skip:
                    push_results.append({
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "clue_count": clue_count,
                        "status": f"skipped - {skip_reason}"
                    })
                    logger.info(f"用户 {user_name}({user_id_str}) 因推送规则限制跳过推送: {skip_reason}")
                    continue

                # 查询用户当日的排班信息
                try:
                    distribution_rules = db.query(DistributionRule).filter(
                        DistributionRule.date == current_date,
                        DistributionRule.member == user_name
                    ).all()
                    # 计算M, X, Y值
                    M = X =0  # M今日预计接收总数# X当前已接收总数
                    for rule in distribution_rules:
                        M += (rule.expected_free or 0)
                        M += (rule.expected_paid or 0)
                        X += (rule.actual_free or 0)
                        X += (rule.actual_paid or 0)
                    
                    Y = M - X  # 剩余数量
     
                    # 构造新的模板消息
                    message = f"你收到了{clue_count}条线索，今日预计接收{M}条，当前已接收{X}条，剩余{Y}条，请前往查看"

                    logger.info(f"用户 {user_name} 排班统计: M={M}, X={X}, Y={Y}")

                except Exception as rule_error:
                    logger.error(f"查询用户 {user_name} 排班信息失败: {rule_error}")
                    # 如果查询失败，使用原始消息
                    message = f"你收到了{clue_count}条线索，请前往查看"

                # 构造推送消息
                push_message = json.dumps({
                    "type": "notification",
                    "message": message,
                    "clue_count": clue_count,
                    "timestamp": datetime.now().isoformat(),
                    "user_name": user_name
                }, ensure_ascii=False)

                print(f"📨 准备向用户 {user_name}({user_id}) 发送消息: {message}")
                print(f"📋 消息内容: {push_message}")

                # 发送WebSocket消息
                success = await websocket_manager.send_personal_message(user_id_str, push_message)
                print(f"📤 消息发送结果: {success}")
                #return success
                if success:
                    pushed_users += 1
                    push_results.append({
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "clue_count": clue_count,
                        "status": "success"
                    })
                    logger.info(f"成功向用户 {user_name}({user_id_str}) 推送 {clue_count} 条线索通知")
                else:
                    push_results.append({
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "clue_count": clue_count,
                        "status": "failed - send error"
                    })
                    logger.warning(f"用户 {user_name}({user_id_str}) 消息发送失败")
            else:
                print(f"⚠️ 用户 {user_name}({user_id_str}) 不在线，跳过推送")
                push_results.append({
                    "user_id": user_id_str,
                    "user_name": user_name,
                    "clue_count": clue_count,
                    "status": "skipped - user offline"
                })
                logger.info(f"用户 {user_name}({user_id_str}) 不在线，跳过推送")

        # 如果有成功推送的用户，更新数据库中的email_seeded字段
        if pushed_users > 0:
            # 获取所有被推送用户的姓名列表
            pushed_user_names = [result["user_name"] for result in push_results if result["status"] == "success"]

            # 更新这些用户的线索为已推送状态
            updated_count = db.query(DistributionQueue).filter(
                DistributionQueue.queue_date == current_date,
                DistributionQueue.email_seeded == 0,
                DistributionQueue.status == "distributed",
                DistributionQueue.member.in_(pushed_user_names)
            ).update(
                {DistributionQueue.email_seeded: 1},
                synchronize_session=False
            )

            db.commit()
            logger.info(f"已更新 {updated_count} 条线索记录为已推送状态")

        return {
            "success": True,
            "message": f"线索推送完成，成功推送给 {pushed_users} 个在线用户",
            "pushed_users": pushed_users,
            "total_users": len(user_clue_counts),
            "total_clues": total_clues,
            "online_users": len(online_users),
            "push_details": push_results
        }

    except Exception as e:
        db.rollback()
        logger.error(f"线索推送失败: {e}")
        raise HTTPException(status_code=500, detail=f"线索推送失败: {str(e)}")



# 客情跟进通知推送接口
@router.get("/api/websocket/sala_crm_follow_tz", summary="客情跟进通知推送")
async def get_sala_crm_follow_tz(db: Session = Depends(get_db)):
    
    """
    客情跟进通知推送接口
    向销售成员和组长发送24小时未跟进线索的提醒通知
    """
    try:
        from datetime import timedelta

        current_time = datetime.now()
        two_days_ago = current_time - timedelta(days=2)
        one_day_ago = current_time - timedelta(days=1)

        print(f"🔍 开始查询24小时未跟进的线索，当前时间: {current_time}")
        print(f"📅 查询范围: 最后跟进时间 > {two_days_ago}")

        # 查询24小时未跟进的线索
        # 表连接关系：SalaCrm.ID == ClueSheet.ID, ClueSheet.contact_person == User.name
        try:
            print("🔍 开始执行数据库查询...")

            # 先测试基本查询是否正常
            sala_crm_count = db.query(SalaCrm).count()
            clue_sheet_count = db.query(ClueSheet).count()
            user_count = db.query(User).count()
            print(f"📊 数据表记录数: SalaCrm={sala_crm_count}, ClueSheet={clue_sheet_count}, User={user_count}")

            # 执行主查询，添加更多调试信息
            unfollow_records = db.query(
                User.ID,
                User.name,
                User.identity,
                User.group_name,
                SalaCrm.allocation_date,
                SalaCrm.SN,
                SalaCrm.ID.label('sala_crm_id'),
                SalaCrm.last_followup_time,
                SalaCrm.clue_stage_follow_time
            ).join(
                ClueSheet, SalaCrm.ID == ClueSheet.ID
            ).join(
                User, ClueSheet.contact_person == User.name
            ).filter(
                # 添加更安全的过滤条件
                SalaCrm.clue_stage_follow_seeded == 0,  # 未推送过通知
                SalaCrm.last_followup_time.isnot(None),  # 确保有最后跟进时间
                SalaCrm.clue_stage_follow_time.isnot(None),  # 确保有阶段跟进时间
                #SalaCrm.last_followup_time > two_days_ago,  # 最后跟进时间在前两天内
                User.department == "销售部"  # 销售部门用户
            ).all()

            print(f"📊 初步查询到 {len(unfollow_records)} 条记录")
           
            # 手动过滤超过24小时未跟进的记录
            filtered_records = []
            for record in unfollow_records:
                if record.last_followup_time and record.clue_stage_follow_time:
                    time_diff = record.last_followup_time - record.clue_stage_follow_time
                    if time_diff > timedelta(days=1):
                        filtered_records.append(record)
                        print(f"✅ 符合条件的记录: 用户={record.name}, 时间差={time_diff}")

            unfollow_records = filtered_records

        except Exception as query_error:
            print(f"❌ 数据库查询失败: {query_error}")
            logger.error(f"数据库查询失败: {query_error}")
            import traceback
            print(f"🔍 查询错误堆栈: {traceback.format_exc()}")

            # 返回错误信息而不是抛出异常
            return {
                "success": False,
                "message": f"数据库查询失败: {str(query_error)}",
                "error": str(query_error),
                "pushed_users": 0,
                "total_clues": 0,
                "push_details": []
            }
        
        print(f"📊 查询到 {len(unfollow_records)} 条24小时未跟进的线索记录")

        if not unfollow_records:
            print("✅ 暂无需要推送的24小时未跟进线索")
            return {
                "success": True,
                "message": "暂无需要推送的24小时未跟进线索",
                "pushed_users": 0,
                "total_clues": 0,
                "push_details": []
            }
        
        # 获取在线用户列表
        online_users = websocket_manager.get_connected_users()
        print(f"🌐 当前在线用户列表: {online_users}")

        pushed_users = 0
        total_clues = len(unfollow_records)
        push_results = []
        successfully_pushed_ids = []  # 记录成功推送的SalaCrm ID

        # 处理每条未跟进记录
        for record in unfollow_records:
            user_id = record.ID
            user_name = record.name
            user_identity = record.identity
            group_name = record.group_name
            allocation_date = record.allocation_date
            sn = record.SN
            sala_crm_id = record.sala_crm_id

            user_id_str = str(user_id)

            print(f"🔍 处理用户: {user_name}({user_id_str}), 身份: {user_identity}, 组别: {group_name}")
            print(f"📋 线索信息: 分配日期={allocation_date}, 序号={sn}")

            # 标记是否有成功推送
            has_successful_push = False

            # 1. 向成员本人发送通知（无论身份如何都发送）
            if user_id_str in online_users:
                # 检查用户推送规则
                should_skip, skip_reason = check_user_push_rules(user_id_str, db)
                if should_skip:
                    push_results.append({
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "clue_count": clue_count,
                        "status": f"skipped - {skip_reason}"
                    })
                    logger.info(f"用户 {user_name}({user_id_str}) 因推送规则限制跳过推送: {skip_reason}")
                    continue

                try:
                    # 安全处理allocation_date
                    if allocation_date:
                        date_str = allocation_date.strftime('%Y-%m-%d')
                        member_message = f"你在{date_str}接收到的第{sn}条线索在24小时内没有跟进记录，请注意及时跟进！"
                    else:
                        date_str = "未知日期"
                        member_message = f"你接收到的第{sn}条线索在24小时内没有跟进记录，请注意及时跟进！"

                    member_push_message = json.dumps({
                        "type": "notification",
                        "message": member_message,
                        "allocation_date": date_str,
                        "sn": sn or 0,
                        "timestamp": current_time.isoformat(),
                        "user_name": user_name,
                        "reminder_type": "member"
                    }, ensure_ascii=False)

                    print(f"📨 向成员 {user_name}({user_id_str}) 发送跟进提醒")

                    # 发送WebSocket消息
                    success = await websocket_manager.send_personal_message(user_id_str, member_push_message)

                    if success:
                        has_successful_push = True
                        pushed_users += 1
                        push_results.append({
                            "user_id": user_id_str,
                            "user_name": user_name,
                            "user_identity": user_identity,
                            "message_type": "member",
                            "status": "success"
                        })
                        print(f"✅ 成功向成员 {user_name}({user_id_str}) 发送跟进提醒")
                    else:
                        push_results.append({
                            "user_id": user_id_str,
                            "user_name": user_name,
                            "user_identity": user_identity,
                            "message_type": "member",
                            "status": "failed - send error"
                        })
                        print(f"❌ 向成员 {user_name}({user_id_str}) 发送跟进提醒失败")

                except Exception as member_error:
                    print(f"❌ 向成员 {user_name}({user_id_str}) 发送消息时出错: {member_error}")
                    push_results.append({
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "user_identity": user_identity,
                        "message_type": "member",
                        "status": f"error - {str(member_error)}"
                    })
            else:
                push_results.append({
                    "user_id": user_id_str,
                    "user_name": user_name,
                    "user_identity": user_identity,
                    "message_type": "member",
                    "status": "skipped - user offline"
                })
                print(f"⚠️ 成员 {user_name}({user_id_str}) 不在线，跳过推送")

            # 2. 如果是成员身份，还需要向组长发送通知
            if user_identity == "成员" and group_name:
                print(f"🔍 查找组长，组别: {group_name}")

                # 查找对应的组长
                group_leader = db.query(User).filter(
                    User.group_name == group_name,
                    User.identity == "组长",
                    User.department == "销售部"
                ).first()

                if group_leader:
                    leader_id_str = str(group_leader.ID)
                    print(f"👑 找到组长: {group_leader.name}({leader_id_str})")

                    if leader_id_str in online_users:
                        try:
                            # 安全处理allocation_date
                            if allocation_date:
                                date_str = allocation_date.strftime('%Y-%m-%d')
                                leader_message = f"{user_name}在{date_str}接收到的第{sn}条线索在24小时内没有跟进记录，请协助及时跟进！"
                            else:
                                date_str = "未知日期"
                                leader_message = f"{user_name}接收到的第{sn}条线索在24小时内没有跟进记录，请协助及时跟进！"

                            leader_push_message = json.dumps({
                                "type": "notification",
                                "message": leader_message,
                                "allocation_date": date_str,
                                "sn": sn or 0,
                                "member_name": user_name,
                                "timestamp": current_time.isoformat(),
                                "user_name": group_leader.name,
                                "reminder_type": "leader"
                            }, ensure_ascii=False)

                            print(f"📨 向组长 {group_leader.name}({leader_id_str}) 发送跟进提醒")

                            # 发送WebSocket消息
                            leader_success = await websocket_manager.send_personal_message(leader_id_str, leader_push_message)

                            if leader_success:
                                has_successful_push = True
                                pushed_users += 1
                                push_results.append({
                                    "user_id": leader_id_str,
                                    "user_name": group_leader.name,
                                    "user_identity": "组长",
                                    "message_type": "leader",
                                    "status": "success",
                                    "for_member": user_name
                                })
                                print(f"✅ 成功向组长 {group_leader.name}({leader_id_str}) 发送跟进提醒")
                            else:
                                push_results.append({
                                    "user_id": leader_id_str,
                                    "user_name": group_leader.name,
                                    "user_identity": "组长",
                                    "message_type": "leader",
                                    "status": "failed - send error",
                                    "for_member": user_name
                                })
                                print(f"❌ 向组长 {group_leader.name}({leader_id_str}) 发送跟进提醒失败")

                        except Exception as leader_error:
                            print(f"❌ 向组长 {group_leader.name}({leader_id_str}) 发送消息时出错: {leader_error}")
                            push_results.append({
                                "user_id": leader_id_str,
                                "user_name": group_leader.name,
                                "user_identity": "组长",
                                "message_type": "leader",
                                "status": f"error - {str(leader_error)}",
                                "for_member": user_name
                            })
                    else:
                        push_results.append({
                            "user_id": leader_id_str,
                            "user_name": group_leader.name,
                            "user_identity": "组长",
                            "message_type": "leader",
                            "status": "skipped - user offline",
                            "for_member": user_name
                        })
                        print(f"⚠️ 组长 {group_leader.name}({leader_id_str}) 不在线，跳过推送")
                else:
                    print(f"⚠️ 未找到组别 {group_name} 的组长")

            # 如果有成功推送，记录这个SalaCrm ID
            if has_successful_push:
                successfully_pushed_ids.append(sala_crm_id)

        # 更新成功推送的记录，将clue_stage_follow_seeded设为1
        if successfully_pushed_ids:
            try:
                updated_count = db.query(SalaCrm).filter(
                    SalaCrm.ID.in_(successfully_pushed_ids)
                ).update(
                    {SalaCrm.clue_stage_follow_seeded: 1},
                    synchronize_session=False
                )

                db.commit()
                print(f"✅ 已更新 {updated_count} 条SalaCrm记录为已推送状态")
            except Exception as update_error:
                db.rollback()
                print(f"❌ 更新SalaCrm记录失败: {update_error}")
                logger.error(f"更新SalaCrm记录失败: {update_error}")

        return {
            "success": True,
            "message": f"客情跟进通知推送完成，成功推送给 {pushed_users} 个在线用户",
            "pushed_users": pushed_users,
            "total_clues": total_clues,
            "online_users": len(online_users),
            "updated_records": len(successfully_pushed_ids),
            "push_details": push_results
        }

    except Exception as e:
        db.rollback()
        print(f"❌ 客情跟进通知推送失败: {e}")
        logger.error(f"客情跟进通知推送失败: {e}")
        import traceback
        print(f"🔍 错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"客情跟进通知推送失败: {str(e)}")

# 审批撤回通知推送接口
@router.get("/api/websocket/shenpi_cehui/{lead_id}", summary="审批撤回通知推送")
async def approval_withdraw_notification(lead_id: str,db: Session = Depends(get_db)):
    #return lead_id
    """
    审批撤回通知推送接口
    向申请撤回线索的用户发送审批结果通知
    """
    try:
        print(f"🔍 开始处理审批撤回通知，线索ID: {lead_id}")

        # 查询撤回申请信息
        try:
            # 表连接关系：ClueWithdraw.ID == SalaCrm.ID, ClueWithdraw.contact_person == User.name
            withdraw_record = db.query(
                User.ID,
                User.name,
                SalaCrm.allocation_date,
                SalaCrm.SN,
                ClueWithdraw.withdraw_status,
                ClueWithdraw.ID.label('withdraw_id')
            ).join(
                SalaCrm, ClueWithdraw.ID == SalaCrm.ID
            ).join(
                User, ClueWithdraw.contact_person == User.name
            ).filter(
                ClueWithdraw.ID == lead_id
            ).first()
            
            if not withdraw_record:
                print(f"⚠️ 未找到线索ID {lead_id} 的撤回申请记录")
                return {
                    "success": False,
                    "message": f"未找到线索ID {lead_id} 的撤回申请记录",
                    "lead_id": lead_id,
                    "pushed_users": 0
                }

            print(f"📋 找到撤回申请记录: 用户={withdraw_record.name}, 状态={withdraw_record.withdraw_status}")
           
        except Exception as query_error:
            print(f"❌ 查询撤回申请记录失败: {query_error}")
            logger.error(f"查询撤回申请记录失败: {query_error}")
            import traceback
            print(f"🔍 查询错误堆栈: {traceback.format_exc()}")

            return {
                "success": False,
                "message": f"查询撤回申请记录失败: {str(query_error)}",
                "error": str(query_error),
                "lead_id": lead_id,
                "pushed_users": 0
            }

        # 解析审批状态
        withdraw_status = withdraw_record.withdraw_status
        if withdraw_status == 1:
            status_text = "通过"
        elif withdraw_status == 2:
            status_text = "拒绝"
        else:
            status_text = f"未知状态({withdraw_status})"
        
        print(f"📊 审批状态: {withdraw_status} -> {status_text}")
       
        # 获取用户信息
        user_id = withdraw_record.ID
        user_name = withdraw_record.name
        allocation_date = withdraw_record.allocation_date
        sn = withdraw_record.SN

        user_id_str = str(user_id)
      
        # 获取在线用户列表
        online_users = websocket_manager.get_connected_users()
        print(f"🌐 当前在线用户列表: {online_users}")
        print(f"🔍 检查用户 {user_name}({user_id_str}) 是否在线")
        
        # 检查用户是否在线
        if user_id_str not in online_users:
            print(f"⚠️ 用户 {user_name}({user_id_str}) 不在线，跳过推送")
            return {
                "success": True,
                "message": f"用户 {user_name} 不在线，无法推送审批通知",
                "lead_id": lead_id,
                "user_info": {
                    "user_id": user_id_str,
                    "user_name": user_name,
                    "online": False
                },
                "approval_info": {
                    "status": status_text,
                    "allocation_date": allocation_date.strftime('%Y-%m-%d') if allocation_date else "未知日期",
                    "sn": sn or 0
                },
                "pushed_users": 0
            }
           
         # 检查用户推送规则
        should_skip, skip_reason = check_user_push_rules(user_id_str, db)
        if should_skip:
            logger.info(f"用户 {user_name}({user_id_str}) 因推送规则限制跳过推送: {skip_reason}")
            return {
                    "success": False,
                    "message": f"向用户 {user_name} 发送审批通知失败",
                    "lead_id": lead_id,
                    "user_info": {
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "online": True
                    },
                    "approval_info": {
                        "status": status_text,
                        "allocation_date": allocation_date.strftime('%Y-%m-%d'),
                        "sn": sn or 0
                    },
                    "pushed_users": 0,
                    "push_status": "failed - send error"
                }
           

        # 构造审批通知消息
        try:
            # 安全处理allocation_date
            if allocation_date:
                date_str = allocation_date.strftime('%Y-%m-%d')
                message = f"你{date_str}接收的第{sn}条线索的撤回申请已{status_text}，点击前往查看"
            else:
                date_str = "未知日期"
                message = f"你接收的第{sn}条线索的撤回申请已{status_text}，点击前往查看"

            approval_message = json.dumps({
                "type": "notification",
                "message": message,
                "allocation_date": date_str,
                "sn": sn or 0,
                "status": status_text,
                "timestamp": datetime.now().isoformat(),
                "user_name": user_name,
                "lead_id": lead_id
            }, ensure_ascii=False)
            
            print(f"📨 准备向用户 {user_name}({user_id_str}) 发送审批通知")
            print(f"📋 消息内容: {message}")

            # 发送WebSocket消息
            success = await websocket_manager.send_personal_message(user_id_str, approval_message)
            #return success
            if success:
                print(f"✅ 成功向用户 {user_name}({user_id_str}) 发送审批通知")
                return {
                    "success": True,
                    "message": f"成功向用户 {user_name} 发送审批通知",
                    "lead_id": lead_id,
                    "user_info": {
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "online": True
                    },
                    "approval_info": {
                        "status": status_text,
                        "allocation_date": date_str,
                        "sn": sn or 0
                    },
                    "pushed_users": 1,
                    "push_status": "success"
                }
            else:
                print(f"❌ 向用户 {user_name}({user_id_str}) 发送审批通知失败")
                return {
                    "success": False,
                    "message": f"向用户 {user_name} 发送审批通知失败",
                    "lead_id": lead_id,
                    "user_info": {
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "online": True
                    },
                    "approval_info": {
                        "status": status_text,
                        "allocation_date": date_str,
                        "sn": sn or 0
                    },
                    "pushed_users": 0,
                    "push_status": "failed - send error"
                }

        except Exception as message_error:
            print(f"❌ 构造或发送审批通知消息时出错: {message_error}")
            logger.error(f"构造或发送审批通知消息时出错: {message_error}")
            import traceback
            print(f"🔍 消息错误堆栈: {traceback.format_exc()}")

            return {
                "success": False,
                "message": f"构造或发送审批通知消息时出错: {str(message_error)}",
                "error": str(message_error),
                "lead_id": lead_id,
                "pushed_users": 0
            }

    except Exception as e:
        print(f"❌ 审批撤回通知推送失败: {e}")
        logger.error(f"审批撤回通知推送失败: {e}")
        import traceback
        print(f"🔍 错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"审批撤回通知推送失败: {str(e)}")



@router.get("/api/websocket/cehui_clue_withdraw/{lead_id}", summary="申请撤回通知推送")
async def apply_withdraw_notification(lead_id: str,db: Session = Depends(get_db)):
    # 申请撤回通知推送接口 向申请撤回线索的审核员发送审批结果通知
    #{对接人}在{时间}接收到的第{N}条线索申请撤回，请尽快处理
    try:
        print(f"🔍 开始处理申请审批撤回通知，线索ID: {lead_id}")

        # 查询撤回申请信息
        try:
            # 表连接关系：ClueWithdraw.ID == SalaCrm.ID, ClueWithdraw.contact_person == User.name
            withdraw_record = db.query(
                User.ID,
                User.name,
                SalaCrm.allocation_date,
                SalaCrm.SN,
                ClueWithdraw.contact_person,
                ClueWithdraw.withdraw_status,
                ClueWithdraw.ID.label('withdraw_id')
            ).join(
                SalaCrm, ClueWithdraw.ID == SalaCrm.ID
            ).join(
                User, ClueWithdraw.reviewer == User.name
            ).filter(
                ClueWithdraw.ID == lead_id
            ).first()


            
            if not withdraw_record:
                print(f"⚠️ 未找到线索ID {lead_id} 的撤回申请记录")
                return {
                    "success": False,
                    "message": f"未找到线索ID {lead_id} 的申请撤回记录",
                    "lead_id": lead_id,
                    "pushed_users": 0
                }

            print(f"📋 找到撤回申请记录: 用户={withdraw_record.name}, 状态={withdraw_record.withdraw_status}")
           
        except Exception as query_error:
            print(f"❌ 查询撤回申请记录失败: {query_error}")
            logger.error(f"查询撤回申请记录失败: {query_error}")
            import traceback
            print(f"🔍 查询错误堆栈: {traceback.format_exc()}")

            return {
                "success": False,
                "message": f"查询撤回申请记录失败: {str(query_error)}",
                "error": str(query_error),
                "lead_id": lead_id,
                "pushed_users": 0
            }

        # 解析审批状态
        withdraw_status = withdraw_record.withdraw_status
        status_text = "待处理"
        print(f"📊 审批状态: {withdraw_status} -> {status_text}")
       
        # 获取用户信息
        user_id = withdraw_record.ID
        user_name = withdraw_record.name
        contact_person = withdraw_record.contact_person
        allocation_date = withdraw_record.allocation_date
        sn = withdraw_record.SN

        user_id_str = str(user_id)
      
        # 获取在线用户列表
        online_users = websocket_manager.get_connected_users()
        print(f"🌐 当前在线用户列表: {online_users}")
        print(f"🔍 检查用户 {user_name}({user_id_str}) 是否在线")
        
        # 检查用户是否在线
        if user_id_str not in online_users:
            print(f"⚠️ 用户 {user_name}({user_id_str}) 不在线，跳过推送")
            return {
                "success": True,
                "message": f"用户 {user_name} 不在线，无法推送审批通知",
                "lead_id": lead_id,
                "user_info": {
                    "user_id": user_id_str,
                    "user_name": user_name,
                    "online": False
                },
                "approval_info": {
                    "status": status_text,
                    "allocation_date": allocation_date.strftime('%Y-%m-%d') if allocation_date else "未知日期",
                    "sn": sn or 0
                },
                "pushed_users": 0
            }
           
         # 检查用户推送规则
        should_skip, skip_reason = check_user_push_rules(user_id_str, db)
        if should_skip:
            logger.info(f"用户 {user_name}({user_id_str}) 因推送规则限制跳过推送: {skip_reason}")
            return {
                    "success": False,
                    "message": f"向用户 {user_name} 发送申请审批通知失败",
                    "lead_id": lead_id,
                    "user_info": {
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "online": True
                    },
                    "approval_info": {
                        "status": status_text,
                        "allocation_date": allocation_date.strftime('%Y-%m-%d'),
                        "sn": sn or 0
                    },
                    "pushed_users": 0,
                    "push_status": "failed - send error"
                }
           

        # 构造审批通知消息
        try:
           
            date_str = allocation_date.strftime('%Y-%m-%d')
            ##{对接人}在{时间}接收到的第{N}条线索申请撤回，请尽快处理
            message = f"{contact_person}在{date_str}接收到的第{sn}条线索申请撤回，请尽快处理"

            approval_message = json.dumps({
                "type": "notification",
                "message": message,
                "allocation_date": date_str,
                "sn": sn or 0,
                "status": status_text,
                "timestamp": datetime.now().isoformat(),
                "user_name": user_name,
                "lead_id": lead_id
            }, ensure_ascii=False)
            
            print(f"📨 准备向用户 {user_name}({user_id_str}) 发送申请审批通知")
            print(f"📋 消息内容: {message}")

            # 发送WebSocket消息
            success = await websocket_manager.send_personal_message(user_id_str, approval_message)
            #return success
            if success:
                print(f"✅ 成功向用户 {user_name}({user_id_str}) 发送审批通知")
                return {
                    "success": True,
                    "message": f"成功向用户 {user_name} 发送审批通知",
                    "lead_id": lead_id,
                    "user_info": {
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "online": True
                    },
                    "approval_info": {
                        "status": status_text,
                        "allocation_date": date_str,
                        "sn": sn or 0
                    },
                    "pushed_users": 1,
                    "push_status": "success"
                }
            else:
                print(f"❌ 向用户 {user_name}({user_id_str}) 发送审批通知失败")
                return {
                    "success": False,
                    "message": f"向用户 {user_name} 发送审批通知失败",
                    "lead_id": lead_id,
                    "user_info": {
                        "user_id": user_id_str,
                        "user_name": user_name,
                        "online": True
                    },
                    "approval_info": {
                        "status": status_text,
                        "allocation_date": date_str,
                        "sn": sn or 0
                    },
                    "pushed_users": 0,
                    "push_status": "failed - send error"
                }

        except Exception as message_error:
            print(f"❌ 构造或发送审批通知消息时出错: {message_error}")
            logger.error(f"构造或发送审批通知消息时出错: {message_error}")
            import traceback
            print(f"🔍 消息错误堆栈: {traceback.format_exc()}")

            return {
                "success": False,
                "message": f"构造或发送审批通知消息时出错: {str(message_error)}",
                "error": str(message_error),
                "lead_id": lead_id,
                "pushed_users": 0
            }

    except Exception as e:
        print(f"❌ 审批撤回通知推送失败: {e}")
        logger.error(f"审批撤回通知推送失败: {e}")
        import traceback
        print(f"🔍 错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"审批撤回通知推送失败: {str(e)}")